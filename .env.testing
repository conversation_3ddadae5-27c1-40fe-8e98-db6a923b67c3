APP_NAME="Jobpilot test"
APP_ENV=local
APP_KEY=base64:SFyXFVQ+IVhnyU3d9WN+pyk2jr+BiIzBvhK3H7iW9Kk=
APP_DEBUG=true
TESTING_MODE=true
APP_URL=http://localhost

APP_TIMEZONE=UTC
APP_CURRENCY=USD
APP_DEFAULT_LANGUAGE=en
APP_CURRENCY_SYMBOL=$
APP_CURRENCY_SYMBOL_POSITION=left

LOG_CHANNEL=stack
LOG_DEPRECATIONS_CHANNEL=null
LOG_LEVEL=debug

DB_CONNECTION=sqlite
DB_HOST=127.0.0.1
DB_PORT=3306
DB_DATABASE=:memory:
DB_USERNAME=root
DB_PASSWORD=password
DB_FOREIGN_KEYS=false

BROADCAST_DRIVER=log
CACHE_DRIVER=file
FILESYSTEM_DISK=local
QUEUE_CONNECTION=sync
SESSION_DRIVER=file
SESSION_LIFETIME=120

MEMCACHED_HOST=127.0.0.1

MAIL_MAILER=smtp
MAIL_HOST=mailhog
MAIL_PORT=1025
MAIL_USERNAME=null
MAIL_PASSWORD=null
MAIL_ENCRYPTION=null
MAIL_FROM_ADDRESS="<EMAIL>"
MAIL_FROM_NAME="${APP_NAME}"

VITE_PUSHER_APP_KEY="${PUSHER_APP_KEY}"
VITE_PUSHER_HOST="${PUSHER_HOST}"
VITE_PUSHER_PORT="${PUSHER_PORT}"
VITE_PUSHER_SCHEME="${PUSHER_SCHEME}"
VITE_PUSHER_APP_CLUSTER="${PUSHER_APP_CLUSTER}"

NOCAPTCHA_SITEKEY=Testing
NOCAPTCHA_SECRET=Secret
NOCAPTCHA_ACTIVE=true
