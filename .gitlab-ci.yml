stages:
  - deploy
 #- test
 # - scan
 # - push

variables:
  GIT_STRATEGY: none
  REMOTE_SSH: "root@***************"
  SSH_PORT: 27588

deploy_to_staging:
  stage: deploy
  only:
    - main
  script:
    - echo "Deploying latest code on development server...."
    - sshpass -p "$SSH_PASSWORD" ssh -o StrictHostKeyChecking=no -p $SSH_PORT $REMOTE_SSH "cd /var/www/html/public_html && git fetch origin && git reset --hard origin/main"
    - echo "Deployment selesai!"
