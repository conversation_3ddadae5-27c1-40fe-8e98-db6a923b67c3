stages:
  - deploy_staging
  - test
  - scan
  - deploy

variables:
  GIT_STRATEGY: none
  TEMP_REPO_DIR: "scan-$CI_PROJECT_NAME"
  HASIL_DIR: "/var/www/html/public_html"

# =======================
# 🚀 DEPLOY TO STAGING
# =======================
deploy_to_staging:
  stage: deploy_staging
  only:
    - main
  allow_failure: true
  script:
    - echo "===== CI/CD DEPLOY TO STAGING DIMULAI ====="
    - echo "[LOG] Menunggu sshpass tersedia/di-install..."
    - 'command -v sshpass >/dev/null || (apt-get update -qq && apt-get install -y sshpass)'
    - echo "[LOG] sshpass OK, memulai SSH ke server staging..."
    - |
      echo "[LOG] Menyambung ke $SSH_USER@$SSH_HOST di port $SSH_PORT ..."
      sshpass -p "$SSH_PASSWORD" ssh -v -o StrictHostKeyChecking=no -p "$SSH_PORT" "$SSH_USER@$SSH_HOST" \
        "echo '[LOG REMOTE] Masuk ke staging. Menjalankan pull.sh ...'; GITLAB_TOKEN='$GITLAB_TOKEN' GITLAB_USER='$GITLAB_USER' GIT_REPO='$GIT_REPO' bash /bin/pull.sh"
    - echo "[LOG] Selesai SSH & deploy ke staging."
    - echo "✅ Deployment to staging selesai!"

# =======================
# 🔍 GITLEAKS - Secret Scan
# =======================
gitleaks_scan:
  stage: test
  only:
    - main
  allow_failure: true
  before_script:
    - mkdir -p "$HASIL_DIR"
    - rm -rf "$TEMP_REPO_DIR"
    - git clone "https://$GITLAB_USER:$<EMAIL>/$CI_PROJECT_PATH.git" "$TEMP_REPO_DIR"
  script:
    - echo "🔐 Memindai secrets dengan Gitleaks..."
    - gitleaks detect --source "$TEMP_REPO_DIR" --verbose --redact | tee "$HASIL_DIR/${CI_PROJECT_NAME}_gitleaks.txt"

# =======================
# 🔍 SEMGREP - Static Analysis
# =======================
semgrep_scan:
  stage: test
  only:
    - main
  allow_failure: true
  before_script:
    - mkdir -p "$HASIL_DIR"
  script:
    - echo "🔍 Memindai kerentanan kode dengan Semgrep..."
    - semgrep --config=auto "$TEMP_REPO_DIR" | tee "$HASIL_DIR/${CI_PROJECT_NAME}_semgrep.txt"

# =======================
# 🔍 TRIVY - File System Scan
# =======================
trivy_scan:
  stage: test
  only:
    - main
  allow_failure: true
  before_script:
    - mkdir -p "$HASIL_DIR"
  script:
    - echo "🐳 Memindai kerentanan file & dependencies..."
    - trivy fs "$TEMP_REPO_DIR" --severity HIGH,CRITICAL | tee "$HASIL_DIR/${CI_PROJECT_NAME}_trivy.txt"

# =======================
# 🔍 ZAP DAST Scan
# =======================
zap_dast:
  stage: scan
  only:
    - main
  allow_failure: true
  before_script:
    - mkdir -p "$HASIL_DIR"
    - |
      cat <<EOF > "/opt/zaproxy/ZAP_D-$(date +%Y-%m-%d)/$CI_PROJECT_NAME-plan.yml"
      env:
        contexts:
          - name: "test-context"
            urls:
              - "https://$SSH_HOST:$HTTPS_PORT"
      jobs:
        - type: "spider"
          name: "spider"
          parameters:
            context: "test-context"
            maxDuration: 60
            maxChildren: 0
        - type: "activeScan"
          name: "activeScan"
          parameters:
            context: "test-context"
            policy: Default Policy
            maxRuleDurationInMins: 10
            maxScanDurationInMins: 60
        - type: "report"
          name: "export-report"
          parameters:
            template: traditional-html
            reportDir: ./
            reportFile: "$CI_PROJECT_NAME-zap-report.html"
      EOF
  script:
    - echo "🔍 Menjalankan ZAP DAST Scan..."
    - zap -cmd -autorun "/opt/zaproxy/ZAP_D-$(date +%Y-%m-%d)/$CI_PROJECT_NAME-plan.yml"
    - cp "$CI_PROJECT_NAME-zap-report.html" "$HASIL_DIR/${CI_PROJECT_NAME}_zap.html"
  artifacts:
    paths:
      - zap-report.html
    when: always

# =======================
# 🚀 FINAL DEPLOY TO SERVER
# =======================
deploy_to_server:
  stage: deploy
  only:
    - main
  when: manual
  allow_failure: true
  script:
    - echo "🚀 Deploying to production server..."
    - rm -rf "$TEMP_REPO_DIR"
    - 'command -v sshpass >/dev/null || (apt-get update -qq && apt-get install -y sshpass)'
    - >
      sshpass -p "$SSH_PASSWORD" ssh -o StrictHostKeyChecking=no -p "$SSH_PORT" "$SSH_USER@$SSH_HOST"
      "GITLAB_TOKEN='$GITLAB_TOKEN' GITLAB_USER='$GITLAB_USER' GIT_REPO='$GIT_REPO' bash /bin/pull.sh --release"
    - echo "✅ Deployment to production selesai!"

# =======================
# 📢 NOTIFY VIA WAHA
# =======================
notify_waha:
  stage: scan
  only:
    - main
  allow_failure: true
  script:
    - echo "📢 Mengirim hasil scan via WAHA....."
    - |
      json_body=$(jq -n --arg chatId "$CHAT_ID" --arg text "$TEXT_SEND" --arg session "$WAHA_SESSION" \
        '{chatId: $chatId, text: $text, session: $session}')
      curl -X POST "$WAHA_URL" -H "Content-Type: application/json" -d "$json_body"
    - chown -R www-data:www-data "$HASIL_DIR"
