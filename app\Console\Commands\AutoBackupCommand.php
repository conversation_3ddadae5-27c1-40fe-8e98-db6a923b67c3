<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Artisan;

class AutoBackupCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'backup:auto';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Jalankan backup otomatis berdasarkan pengaturan jadwal';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        try {
            // Cek apakah backup otomatis diaktifkan
            $enabled = config('backup-cloud.schedule.enabled', false);

            if (!$enabled) {
                $this->info('Backup otomatis tidak diaktifkan.');
                return 0;
            }

            $this->info('Memulai backup otomatis...');

            // Gunakan Spatie backup command yang sudah ada
            Artisan::call('backup:run', [
                '--only-db' => true,
                '--disable-notifications' => true
            ]);

            $this->info('Backup otomatis selesai.');
            Log::info('Auto backup completed successfully');

            // Hapus backup lama berdasarkan pengaturan keep_days
            $this->cleanOldBackups();

            return 0;

        } catch (\Exception $e) {
            $this->error('Backup otomatis gagal: ' . $e->getMessage());
            Log::error('Auto backup failed: ' . $e->getMessage());
            return 1;
        }
    }

    /**
     * Hapus backup lama berdasarkan pengaturan keep_days
     */
    private function cleanOldBackups()
    {
        try {
            // Gunakan Spatie backup clean command
            Artisan::call('backup:clean', [
                '--disable-notifications' => true
            ]);

            $this->info('Backup lama berhasil dibersihkan.');

        } catch (\Exception $e) {
            Log::error('Failed to clean old backups: ' . $e->getMessage());
        }
    }
}
