<?php

namespace App\Console\Commands;

use App\Models\Candidate;
use App\Models\User;
use Illuminate\Console\Command;

class FixCandidateData extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'candidate:fix-data {--dry-run : Show what would be fixed without making changes}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Fix candidate data with invalid or missing user_id';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $dryRun = $this->option('dry-run');
        
        $this->info('Scanning for candidates with invalid user_id...');
        
        // Find candidates with null user_id
        $candidatesWithNullUserId = Candidate::whereNull('user_id')->get();
        
        // Find candidates with invalid user_id (user doesn't exist)
        $candidatesWithInvalidUserId = Candidate::whereNotNull('user_id')
            ->whereDoesntHave('user')
            ->get();
            
        $totalProblems = $candidatesWithNullUserId->count() + $candidatesWithInvalidUserId->count();
        
        if ($totalProblems === 0) {
            $this->info('✅ No candidate data issues found!');
            return 0;
        }
        
        $this->warn("Found {$totalProblems} candidates with data issues:");
        $this->warn("- {$candidatesWithNullUserId->count()} candidates with null user_id");
        $this->warn("- {$candidatesWithInvalidUserId->count()} candidates with invalid user_id");
        
        if ($dryRun) {
            $this->info("\n🔍 DRY RUN MODE - No changes will be made\n");
        }
        
        $fixed = 0;
        $deleted = 0;
        
        // Process candidates with null user_id
        foreach ($candidatesWithNullUserId as $candidate) {
            $this->processCandidate($candidate, 'null user_id', $dryRun, $fixed, $deleted);
        }
        
        // Process candidates with invalid user_id
        foreach ($candidatesWithInvalidUserId as $candidate) {
            $this->processCandidate($candidate, 'invalid user_id', $dryRun, $fixed, $deleted);
        }
        
        if (!$dryRun) {
            $this->info("\n✅ Processing complete!");
            $this->info("- Fixed: {$fixed} candidates");
            $this->info("- Deleted: {$deleted} candidates");
        } else {
            $this->info("\n📋 Summary (DRY RUN):");
            $this->info("- Would fix: {$fixed} candidates");
            $this->info("- Would delete: {$deleted} candidates");
            $this->info("\nRun without --dry-run to apply changes.");
        }
        
        return 0;
    }
    
    private function processCandidate(Candidate $candidate, string $issue, bool $dryRun, int &$fixed, int &$deleted)
    {
        $this->line("\nProcessing candidate ID {$candidate->id} ({$issue}):");
        
        // Try to find a suitable user
        $orphanUser = User::where('role', 'candidate')
            ->whereDoesntHave('candidate')
            ->orderBy('created_at', 'desc')
            ->first();
            
        if ($orphanUser) {
            $this->info("  ✅ Found orphan user: {$orphanUser->name} ({$orphanUser->email})");
            
            if (!$dryRun) {
                $candidate->user_id = $orphanUser->id;
                $candidate->save();
            }
            
            $fixed++;
        } else {
            // Check if candidate has any meaningful data
            $hasData = $candidate->title || 
                      $candidate->bio || 
                      $candidate->photo !== 'backend/image/default.png' ||
                      $candidate->cv ||
                      $candidate->skills()->count() > 0 ||
                      $candidate->languages()->count() > 0;
                      
            if ($hasData) {
                $this->warn("  ⚠️  Candidate has data but no suitable user found - keeping for manual review");
                $this->warn("      Title: " . ($candidate->title ?: 'N/A'));
                $this->warn("      Bio: " . (substr($candidate->bio ?: 'N/A', 0, 50)));
            } else {
                $this->error("  🗑️  No data and no suitable user - marking for deletion");
                
                if (!$dryRun) {
                    // Clean up relations first
                    $candidate->skills()->detach();
                    $candidate->languages()->detach();
                    $candidate->bookmarkJobs()->detach();
                    $candidate->appliedJobs()->detach();
                    
                    // Delete CV views
                    \App\Models\CandidateCvView::where('candidate_id', $candidate->id)->delete();
                    
                    // Delete the candidate
                    $candidate->delete();
                }
                
                $deleted++;
            }
        }
    }
}
