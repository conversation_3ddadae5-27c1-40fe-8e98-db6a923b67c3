<?php

namespace App\Console\Commands;

use App\Models\Kelurahan;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

class FixKelurahanNameFormat extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'fix:kelurahan-names {--dry-run} {--kecamatan=}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Fix kelurahan names from UPPERCASE to Title Case format';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $this->info('Starting kelurahan name format fix...');

        $dryRun = $this->option('dry-run');
        $kecamatanFilter = $this->option('kecamatan');

        try {
            // Get kelurahan with uppercase names
            $query = Kelurahan::whereRaw('name = UPPER(name)');
            
            if ($kecamatanFilter) {
                $query->whereHas('kecamatan', function($q) use ($kecamatanFilter) {
                    $q->where('name', 'like', '%' . $kecamatanFilter . '%');
                });
            }

            $kelurahanList = $query->get();

            if ($kelurahanList->isEmpty()) {
                $this->info('No kelurahan with uppercase names found.');
                return 0;
            }

            $this->info("Found {$kelurahanList->count()} kelurahan with uppercase names:");

            $updatedCount = 0;

            foreach ($kelurahanList as $kelurahan) {
                $oldName = $kelurahan->name;
                $newName = ucwords(strtolower($oldName));

                if ($dryRun) {
                    $this->line("  [DRY RUN] Would update: '{$oldName}' → '{$newName}' (Kecamatan: {$kelurahan->kecamatan->name})");
                } else {
                    $kelurahan->update(['name' => $newName]);
                    $this->line("  Updated: '{$oldName}' → '{$newName}' (Kecamatan: {$kelurahan->kecamatan->name})");
                }
                
                $updatedCount++;
            }

            if ($dryRun) {
                $this->info("\nDry run completed. {$updatedCount} kelurahan would be updated.");
                $this->info("Run without --dry-run to actually update the names.");
            } else {
                $this->info("\nFormat fix completed! {$updatedCount} kelurahan names updated.");
            }

            return 0;

        } catch (\Exception $e) {
            $this->error("Fix failed: {$e->getMessage()}");
            return 1;
        }
    }
}
