<?php

namespace App\Console\Commands;

use App\Models\Kelurahan;
use App\Models\Kecamatan;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class ImportKelurahanByKecamatan extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'import:kelurahan-by-kecamatan {--kecamatan=} {--force} {--fresh} {--dry-run}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Import kelurahan data based on existing kecamatan in database. Use --fresh to delete all existing data first.';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $this->info('Starting kelurahan import based on existing kecamatan...');

        try {
            $kecamatanFilter = $this->option('kecamatan');
            $force = $this->option('force');
            $fresh = $this->option('fresh');
            $dryRun = $this->option('dry-run');

            // Get all kecamatan from database
            $kecamatanQuery = Kecamatan::with('city.state');

            if ($kecamatanFilter) {
                $kecamatanQuery->where('name', 'like', '%' . $kecamatanFilter . '%');
            }

            $kecamatans = $kecamatanQuery->get();

            if ($kecamatans->isEmpty()) {
                $this->error('No kecamatan found in database');
                return 1;
            }

            $this->info("Found {$kecamatans->count()} kecamatan to process");

            // Handle fresh import - delete existing kelurahan data
            if ($fresh && !$dryRun) {
                $this->warn("⚠️  FRESH IMPORT MODE: This will delete all existing kelurahan data for the selected kecamatan!");

                if (!$this->confirm('Are you sure you want to delete all existing kelurahan data and import fresh? This action cannot be undone.')) {
                    $this->info('Import cancelled by user.');
                    return 0;
                }

                $this->info('Deleting existing kelurahan data...');
                $kecamatanIds = $kecamatans->pluck('id');
                $deletedCount = Kelurahan::whereIn('kecamatan_id', $kecamatanIds)->count();

                if ($deletedCount > 0) {
                    Kelurahan::whereIn('kecamatan_id', $kecamatanIds)->delete();
                    $this->info("Deleted {$deletedCount} existing kelurahan records.");
                } else {
                    $this->info("No existing kelurahan data found to delete.");
                }
            } elseif ($fresh && $dryRun) {
                $this->warn("[DRY RUN] FRESH IMPORT MODE: Would delete all existing kelurahan data for the selected kecamatan!");
                $kecamatanIds = $kecamatans->pluck('id');
                $wouldDeleteCount = Kelurahan::whereIn('kecamatan_id', $kecamatanIds)->count();
                $this->info("[DRY RUN] Would delete {$wouldDeleteCount} existing kelurahan records.");
            }

            // Get all provinces from API first
            $this->info('Fetching provinces from API...');
            $provincesResponse = Http::timeout(30)->get('https://tupski.github.io/api-wilayah-indonesia/api/provinces.json');

            if (!$provincesResponse->successful()) {
                $this->error('Failed to fetch provinces from API');
                return 1;
            }

            $provinces = $provincesResponse->json();
            $processedCount = 0;
            $skippedCount = 0;
            $notFoundCount = 0;

            foreach ($kecamatans as $kecamatan) {
                $this->info("Processing kecamatan: {$kecamatan->name}");

                // Find matching province
                $matchingProvince = null;
                foreach ($provinces as $province) {
                    if ($kecamatan->city && $kecamatan->city->state) {
                        if (stripos($province['name'], $kecamatan->city->state->name) !== false) {
                            $matchingProvince = $province;
                            break;
                        }
                    }
                }

                if (!$matchingProvince) {
                    $this->warn("  Province not found for kecamatan: {$kecamatan->name}");
                    $notFoundCount++;
                    continue;
                }

                // Get regencies for this province
                $regenciesResponse = Http::timeout(30)->get("https://tupski.github.io/api-wilayah-indonesia/api/regencies/{$matchingProvince['id']}.json");

                if (!$regenciesResponse->successful()) {
                    $this->warn("  Failed to fetch regencies for province: {$matchingProvince['name']}");
                    continue;
                }

                $regencies = $regenciesResponse->json();

                // Find matching regency (city)
                $matchingRegency = null;
                foreach ($regencies as $regency) {
                    if ($kecamatan->city) {
                        if (stripos($regency['name'], $kecamatan->city->name) !== false) {
                            $matchingRegency = $regency;
                            break;
                        }
                    }
                }

                if (!$matchingRegency) {
                    $this->warn("  City not found for kecamatan: {$kecamatan->name}");
                    $notFoundCount++;
                    continue;
                }

                // Get districts for this regency
                $districtsResponse = Http::timeout(30)->get("https://tupski.github.io/api-wilayah-indonesia/api/districts/{$matchingRegency['id']}.json");

                if (!$districtsResponse->successful()) {
                    $this->warn("  Failed to fetch districts for city: {$matchingRegency['name']}");
                    continue;
                }

                $districts = $districtsResponse->json();

                // Find matching district
                $matchingDistrict = null;
                foreach ($districts as $district) {
                    if (stripos($district['name'], $kecamatan->name) !== false ||
                        stripos($kecamatan->name, $district['name']) !== false) {
                        $matchingDistrict = $district;
                        break;
                    }
                }

                if (!$matchingDistrict) {
                    $this->warn("  District not found in API for kecamatan: {$kecamatan->name}");
                    $notFoundCount++;
                    continue;
                }

                // Get villages for this district
                $villagesResponse = Http::timeout(30)->get("https://tupski.github.io/api-wilayah-indonesia/api/villages/{$matchingDistrict['id']}.json");

                if (!$villagesResponse->successful()) {
                    $this->warn("  Failed to fetch villages for district: {$matchingDistrict['name']}");
                    continue;
                }

                $villages = $villagesResponse->json();
                $this->info("  Found " . count($villages) . " villages for {$kecamatan->name}");

                foreach ($villages as $village) {
                    // Format nama kelurahan - ubah dari KAPITAL SEMUA menjadi Title Case
                    $formattedName = ucwords(strtolower($village['name']));

                    // Check if kelurahan already exists (cek dengan nama yang sudah diformat)
                    // Skip checking if fresh mode is enabled since we already deleted all data
                    $existingKelurahan = null;
                    if (!$fresh) {
                        $existingKelurahan = Kelurahan::where('name', $formattedName)
                            ->where('kecamatan_id', $kecamatan->id)
                            ->first();

                        if ($existingKelurahan && !$force) {
                            $skippedCount++;
                            continue;
                        }
                    }

                    if ($dryRun) {
                        if ($existingKelurahan) {
                            $this->line("    [DRY RUN] Would update: {$formattedName}");
                        } else {
                            $this->line("    [DRY RUN] Would add: {$formattedName}");
                        }
                        $processedCount++;
                        continue;
                    }

                    try {
                        if ($fresh) {
                            // Fresh mode: always create new (since we deleted all existing data)
                            Kelurahan::create([
                                'name' => $formattedName,
                                'kecamatan_id' => $kecamatan->id,
                            ]);
                            $this->line("    Added: {$formattedName}");
                        } elseif ($existingKelurahan && $force) {
                            // Update existing
                            $existingKelurahan->update([
                                'name' => $formattedName,
                                'kecamatan_id' => $kecamatan->id,
                            ]);
                            $this->line("    Updated: {$formattedName}");
                        } else {
                            // Create new
                            Kelurahan::create([
                                'name' => $formattedName,
                                'kecamatan_id' => $kecamatan->id,
                            ]);
                            $this->line("    Added: {$formattedName}");
                        }

                        $processedCount++;
                    } catch (\Exception $e) {
                        $this->error("    Failed to save: {$village['name']} - {$e->getMessage()}");
                        Log::error("Failed to import kelurahan: {$village['name']}", [
                            'error' => $e->getMessage(),
                            'kecamatan_id' => $kecamatan->id
                        ]);
                    }
                }
            }

            $this->info("\nImport completed!");
            $this->info("Processed: {$processedCount} kelurahan");
            $this->info("Skipped: {$skippedCount} kelurahan (already exists)");
            $this->info("Not found: {$notFoundCount} kecamatan");

            if ($dryRun) {
                $this->warn("This was a dry run. No data was actually imported.");
                $this->info("Run without --dry-run to actually import the data.");
            }

            return 0;

        } catch (\Exception $e) {
            $this->error("Import failed: {$e->getMessage()}");
            Log::error('Kelurahan import failed', ['error' => $e->getMessage()]);
            return 1;
        }
    }
}
