<?php

namespace App\Console\Commands;

use App\Models\Kelurahan;
use App\Models\Kecamatan;
use App\Models\City;
use App\Models\State;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class ImportKelurahanData extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'import:kelurahan {--province=} {--city=} {--district=} {--force} {--fresh}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Import kelurahan data from API wilayah Indonesia';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $this->info('Starting kelurahan data import...');

        try {
            // Get filter options
            $provinceFilter = $this->option('province');
            $cityFilter = $this->option('city');
            $districtFilter = $this->option('district');
            $force = $this->option('force');

            // Get provinces from API
            $this->info('Fetching provinces from API...');
            $provincesResponse = Http::timeout(30)->get('https://tupski.github.io/api-wilayah-indonesia/api/provinces.json');

            if (!$provincesResponse->successful()) {
                $this->error('Failed to fetch provinces from API');
                return 1;
            }

            $provinces = $provincesResponse->json();
            $processedCount = 0;
            $skippedCount = 0;

            foreach ($provinces as $province) {
                // Filter by province if specified
                if ($provinceFilter && stripos($province['name'], $provinceFilter) === false) {
                    continue;
                }

                $this->info("Processing province: {$province['name']}");

                // Get regencies (cities) for this province
                $regenciesResponse = Http::timeout(30)->get("https://tupski.github.io/api-wilayah-indonesia/api/regencies/{$province['id']}.json");

                if (!$regenciesResponse->successful()) {
                    $this->warn("Failed to fetch regencies for province: {$province['name']}");
                    continue;
                }

                $regencies = $regenciesResponse->json();

                foreach ($regencies as $regency) {
                    // Filter by city if specified
                    if ($cityFilter && stripos($regency['name'], $cityFilter) === false) {
                        continue;
                    }

                    $this->info("  Processing city: {$regency['name']}");

                    // Get districts for this regency
                    $districtsResponse = Http::timeout(30)->get("https://tupski.github.io/api-wilayah-indonesia/api/districts/{$regency['id']}.json");

                    if (!$districtsResponse->successful()) {
                        $this->warn("    Failed to fetch districts for city: {$regency['name']}");
                        continue;
                    }

                    $districts = $districtsResponse->json();

                    foreach ($districts as $district) {
                        // Filter by district if specified
                        if ($districtFilter && stripos($district['name'], $districtFilter) === false) {
                            continue;
                        }

                        $this->info("    Processing district: {$district['name']}");

                        // Find kecamatan in our database
                        $kecamatan = Kecamatan::where('name', 'like', '%' . $district['name'] . '%')->first();

                        if (!$kecamatan) {
                            $this->warn("      Kecamatan not found in database: {$district['name']}");
                            continue;
                        }

                        // Get villages for this district
                        $villagesResponse = Http::timeout(30)->get("https://tupski.github.io/api-wilayah-indonesia/api/villages/{$district['id']}.json");

                        if (!$villagesResponse->successful()) {
                            $this->warn("      Failed to fetch villages for district: {$district['name']}");
                            continue;
                        }

                        $villages = $villagesResponse->json();

                        foreach ($villages as $village) {
                            // Format nama kelurahan - ubah dari KAPITAL SEMUA menjadi Title Case
                            $formattedName = ucwords(strtolower($village['name']));

                            // Check if kelurahan already exists
                            $existingKelurahan = Kelurahan::where('name', $formattedName)
                                ->where('kecamatan_id', $kecamatan->id)
                                ->first();

                            if ($existingKelurahan && !$force) {
                                $skippedCount++;
                                continue;
                            }

                            try {
                                if ($existingKelurahan && $force) {
                                    // Update existing
                                    $existingKelurahan->update([
                                        'name' => $formattedName,
                                        'kecamatan_id' => $kecamatan->id,
                                    ]);
                                    $this->line("        Updated: {$formattedName}");
                                } else {
                                    // Create new
                                    Kelurahan::create([
                                        'name' => $formattedName,
                                        'kecamatan_id' => $kecamatan->id,
                                    ]);
                                    $this->line("        Added: {$formattedName}");
                                }

                                $processedCount++;
                            } catch (\Exception $e) {
                                $this->error("        Failed to save: {$village['name']} - {$e->getMessage()}");
                                Log::error("Failed to import kelurahan: {$village['name']}", [
                                    'error' => $e->getMessage(),
                                    'kecamatan_id' => $kecamatan->id
                                ]);
                            }
                        }
                    }
                }
            }

            $this->info("\nImport completed!");
            $this->info("Processed: {$processedCount} kelurahan");
            $this->info("Skipped: {$skippedCount} kelurahan (already exists)");

            return 0;

        } catch (\Exception $e) {
            $this->error("Import failed: {$e->getMessage()}");
            Log::error('Kelurahan import failed', ['error' => $e->getMessage()]);
            return 1;
        }
    }
}
