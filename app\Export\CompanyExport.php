<?php

namespace App\Export;

use App\Models\Company;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithStyles;
use Maatwebsite\Excel\Concerns\WithColumnWidths;
use Maatwebsite\Excel\Concerns\WithTitle;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use Maatwebsite\Excel\Concerns\WithColumnFormatting;
use PhpOffice\PhpSpreadsheet\Style\Fill;
use PhpOffice\PhpSpreadsheet\Style\Alignment;
use PhpOffice\PhpSpreadsheet\Style\Border;
use PhpOffice\PhpSpreadsheet\Style\Color;
use PhpOffice\PhpSpreadsheet\Style\NumberFormat;

class CompanyExport implements FromCollection, WithHeadings, WithStyles, WithColumnWidths, WithTitle, ShouldAutoSize, WithColumnFormatting
{
    protected $request;

    public function __construct($request = null)
    {
        $this->request = $request;
    }

    public function collection()
    {
        $query = Company::with(['user', 'organization', 'industry', 'user.contactInfo'])
            ->join('users', 'companies.user_id', '=', 'users.id')
            ->where('users.role', 'company');

        // Apply filters if request is provided
        if ($this->request) {
            // Filter berdasarkan keyword
            if ($this->request->filled('keyword')) {
                $keyword = $this->request->keyword;
                $query->where(function($q) use ($keyword) {
                    $q->where('users.name', 'LIKE', "%{$keyword}%")
                        ->orWhere('users.email', 'LIKE', "%{$keyword}%")
                        ->orWhere('users.no_hp', 'LIKE', "%{$keyword}%")
                        ->orWhere('users.nama_hrd', 'LIKE', "%{$keyword}%");
                });
            }

            // Filter berdasarkan organization type
            if ($this->request->filled('organization_type')) {
                $query->where('companies.organization_type_id', $this->request->organization_type);
            }

            // Filter berdasarkan industry type
            if ($this->request->filled('industry_type')) {
                $query->where('companies.industry_type_id', $this->request->industry_type);
            }

            // Filter berdasarkan status
            if ($this->request->filled('status')) {
                if ($this->request->status === 'active') {
                    $query->where('users.status', 1);
                } elseif ($this->request->status === 'inactive') {
                    $query->where('users.status', 0);
                }
            }

            // Filter berdasarkan user_status
            if ($this->request->filled('user_status')) {
                $query->where('users.status', $this->request->user_status);
            }

            // Filter perusahaan dengan loker
            if ($this->request->filled('has_jobs') && $this->request->has_jobs === 'true') {
                $query->whereHas('jobs', function($q) {
                    $q->where('status', true);
                });
            }
        }

        $companies = $query->orderBy('companies.created_at', 'desc')->get();

        if ($companies->isEmpty()) {
            return collect(); // Return empty
        }

        return $companies->map(function ($company) {
            // Get contact info
            $contactInfo = $company->user->contactInfo;
            $companyPhone = $contactInfo ? $contactInfo->phone : '-';

            return [
                'nama_perusahaan' => formatCompanyName($company),
                'no_telp_perusahaan' => $companyPhone,
                'nama_penanggung_jawab' => $company->user->nama_hrd ?? '-',
                'no_telp_penanggung_jawab' => $company->user->no_hp ?? '-',
                'email' => $company->user->email ?? '-',
                'badan_hukum' => $company->organization ? $company->organization->name : '-',
                'jenis_industri' => $company->industry ? $company->industry->name : '-',
                'website' => $company->website ?? '-',
                'alamat' => $company->address ?? '-',
                'kecamatan' => $company->district ?? '-',
                'kabupaten_kota' => $company->region ?? '-',
                'provinsi' => $company->country ?? '-',
                'tanggal_berdiri' => $company->establishment_date ? $company->establishment_date->format('d/m/Y') : '-',
                'jumlah_loker_aktif' => $company->jobs()->where('status', true)->count(),
                'status' => $company->user->status == 1 ? 'Aktif' : 'Tidak Aktif',
                'tanggal_daftar' => $company->created_at ? $company->created_at->format('d/m/Y') : '-',
            ];
        });
    }

    public function headings(): array
    {
        return [
            'Nama Perusahaan',
            'No Telp Perusahaan',
            'Nama Penanggung Jawab',
            'No Telp Penanggung Jawab',
            'Email',
            'Badan Hukum',
            'Jenis Industri',
            'Website',
            'Alamat',
            'Kecamatan',
            'Kabupaten/Kota',
            'Provinsi',
            'Tanggal Berdiri',
            'Jumlah Loker Aktif',
            'Status',
            'Tanggal Daftar'
        ];
    }

    public function columnWidths(): array
    {
        return [
            'A' => 35, // Nama Perusahaan
            'B' => 20, // No Telp Perusahaan
            'C' => 30, // Nama Penanggung Jawab
            'D' => 20, // No Telp Penanggung Jawab
            'E' => 30, // Email
            'F' => 20, // Badan Hukum
            'G' => 25, // Jenis Industri
            'H' => 30, // Website
            'I' => 40, // Alamat
            'J' => 20, // Kecamatan
            'K' => 20, // Kabupaten/Kota
            'L' => 20, // Provinsi
            'M' => 15, // Tanggal Berdiri
            'N' => 15, // Jumlah Loker Aktif
            'O' => 15, // Status
            'P' => 15, // Tanggal Daftar
        ];
    }

    public function columnFormats(): array
    {
        return [
            'B' => NumberFormat::FORMAT_TEXT, // No Telp Perusahaan as text
            'D' => NumberFormat::FORMAT_TEXT, // No Telp Penanggung Jawab as text
        ];
    }

    public function title(): string
    {
        return 'Daftar Perusahaan';
    }

    public function styles($sheet)
    {
        $lastColumn = 'P'; // Sesuaikan dengan jumlah kolom (16 kolom)
        $lastRow = $sheet->getHighestRow();

        // Style untuk header
        $headerRange = 'A1:' . $lastColumn . '1';
        $sheet->getStyle($headerRange)->applyFromArray([
            'font' => [
                'bold' => true,
                'color' => ['rgb' => 'FFFFFF'],
            ],
            'fill' => [
                'fillType' => Fill::FILL_SOLID,
                'startColor' => ['rgb' => '007BFF'],
            ],
            'alignment' => [
                'horizontal' => Alignment::HORIZONTAL_CENTER,
                'vertical' => Alignment::VERTICAL_CENTER,
            ],
            'borders' => [
                'allBorders' => [
                    'borderStyle' => Border::BORDER_THIN,
                    'color' => ['rgb' => '000000'],
                ],
            ],
        ]);

        // Style untuk data
        $dataRange = 'A2:' . $lastColumn . $lastRow;
        $sheet->getStyle($dataRange)->applyFromArray([
            'alignment' => [
                'vertical' => Alignment::VERTICAL_CENTER,
            ],
            'borders' => [
                'allBorders' => [
                    'borderStyle' => Border::BORDER_THIN,
                    'color' => ['rgb' => '000000'],
                ],
            ],
        ]);

        // Zebra striping untuk baris
        for ($row = 2; $row <= $lastRow; $row++) {
            if ($row % 2 == 0) {
                $sheet->getStyle('A' . $row . ':' . $lastColumn . $row)->applyFromArray([
                    'fill' => [
                        'fillType' => Fill::FILL_SOLID,
                        'startColor' => ['rgb' => 'F2F2F2'],
                    ],
                ]);
            }
        }

        // Menyesuaikan tinggi baris header
        $sheet->getRowDimension(1)->setRowHeight(25);

        // Menyesuaikan tinggi baris data
        for ($row = 2; $row <= $lastRow; $row++) {
            $sheet->getRowDimension($row)->setRowHeight(20);
        }

        // Mengaktifkan wrap text untuk kolom alamat dan website
        $sheet->getStyle('H2:H' . $lastRow)->getAlignment()->setWrapText(true);
        $sheet->getStyle('I2:I' . $lastRow)->getAlignment()->setWrapText(true);
    }
}
