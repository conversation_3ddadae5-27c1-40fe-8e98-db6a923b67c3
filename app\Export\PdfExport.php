<?php

namespace App\Export;

use App\Models\Candidate;
use Illuminate\Contracts\View\View;
use Maatwebsite\Excel\Concerns\FromView;

class PdfExport implements FromView
{
    public function view(): View
    {
        $candidates = Candidate::with(['user', 'education', 'profession'])
            ->orderBy('created_at', 'desc')
            ->get()
            ->map(function ($candidate) {
                return [
                    'nik' => $candidate->user->nik ?? '-',
                    'nama' => $candidate->user->name ?? '-',
                    'email' => $candidate->user->email ?? '-',
                    'jenis_kelamin' => $candidate->gender == 'male' ? 'Laki-laki' : 'Perempuan',
                    'no_hp' => $candidate->user->no_hp ?? '-',
                    'pendidikan' => $candidate->education ? $candidate->education->name : '-',
                    'profesi' => $candidate->profession ? $candidate->profession->name : '-',
                    'alamat' => $candidate->user->alamat_ktp ?? '-',
                    'kecamatan' => $candidate->user->kecamatan ?? '-',
                    'kabupaten_kota' => $candidate->user->kabupaten_kota ?? '-',
                    'provinsi' => $candidate->user->provinsi ?? '-',
                    'status' => $candidate->user->status == 1 ? 'Aktif' : 'Tidak Aktif',
                    'tanggal_daftar' => $candidate->created_at ? $candidate->created_at->format('d/m/Y') : '-',
                ];
            });

        return view('exports.candidates', [
            'candidates' => $candidates
        ]);
    }
}
