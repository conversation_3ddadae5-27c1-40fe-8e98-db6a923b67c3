<?php

namespace App\Helpers;

use App\Models\Setting;
use Illuminate\Support\Facades\Cache;

class AdminPathHelper
{
    /**
     * Mendapatkan custom admin path dari setting
     *
     * @return string
     */
    public static function getAdminPath()
    {
        try {
            return Cache::remember('admin_path', 3600, function () {
                $setting = Setting::first();
                return $setting ? ($setting->admin_path ?? 'admin') : 'admin';
            });
        } catch (\Exception $e) {
            // If database is not available or any error occurs, use default
            return 'admin';
        }
    }
}
