<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Candidate;
use Illuminate\Http\Request;
use Yajra\DataTables\Facades\DataTables;

class CandidateDataTableController2 extends Controller
{
    /**
     * Handle the incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function __invoke(Request $request)
    {
        // Log untuk debugging pagination
        \Log::info('DataTables Request 2', [
            'start' => $request->start,
            'length' => $request->length,
            'draw' => $request->draw,
            'search' => $request->search,
            'order' => $request->order
        ]);

        // <PERSON>akan join untuk menghindari "unknown column" error
        $candidates = Candidate::join('users', 'candidates.user_id', '=', 'users.id')
            ->leftJoin('education', 'candidates.education_id', '=', 'education.id')
            ->leftJoin('education_translations', function($join) {
                $join->on('education.id', '=', 'education_translations.education_id')
                    ->where('education_translations.locale', app()->getLocale());
            })
            ->withCount('appliedJobs')
            ->select([
                'candidates.id',
                'candidates.user_id',
                'candidates.gender',
                'candidates.education_id',
                'candidates.created_at',
                'users.name as user_name',
                'users.email as user_email',
                'users.no_hp as user_phone',
                'users.nik as user_nik',
                'users.status as user_status',
                'education_translations.name as education_name'
            ]);

        // Filter berdasarkan kecamatan
        if ($request->has('kecamatan') && !empty($request->kecamatan)) {
            $candidates->where('users.kecamatan', $request->kecamatan);
        }

        // Filter berdasarkan kota/kabupaten
        if ($request->has('city') && !empty($request->city)) {
            $candidates->where('users.kabupaten_kota', $request->city);
        }

        // Filter berdasarkan jenis kelamin
        if ($request->has('gender') && !empty($request->gender)) {
            $candidates->where('candidates.gender', $request->gender);
        }

        // Filter berdasarkan pendidikan
        if ($request->has('education') && !empty($request->education)) {
            $candidates->where('candidates.education_id', $request->education);
        }

        // Tambahkan filter pencarian global untuk nama, email, no_hp, dan NIK
        if ($request->has('search') && !empty($request->search['value'])) {
            $searchValue = $request->search['value'];
            $candidates->where(function($query) use ($searchValue) {
                $query->where('users.name', 'LIKE', "%{$searchValue}%")
                    ->orWhere('users.email', 'LIKE', "%{$searchValue}%")
                    ->orWhere('users.no_hp', 'LIKE', "%{$searchValue}%")
                    ->orWhere('users.nik', 'LIKE', "%{$searchValue}%");
            });
        }

        // Urutkan berdasarkan waktu terbaru atau terlama
        if ($request->has('sort_by')) {
            if ($request->sort_by == 'oldest') {
                $candidates->orderBy('candidates.created_at', 'asc');
            } else {
                $candidates->orderBy('candidates.created_at', 'desc');
            }
        } else {
            $candidates->orderBy('candidates.created_at', 'desc');
        }

        // Tambahkan debugging untuk pagination
        \Log::info('Pagination Parameters', [
            'start' => (int)$request->start,
            'length' => (int)$request->length,
            'sql' => $candidates->toSql(),
            'bindings' => $candidates->getBindings()
        ]);

        // Penting: Jangan panggil count() atau get() sebelum DataTables::of()
        // karena akan mengeksekusi query dan menghilangkan pagination
        $dt = DataTables::of($candidates);

        $dt->addColumn('checkbox', function ($candidate) {
            return '<div class="form-check">
                <input class="form-check-input candidate-checkbox" type="checkbox" value="' . $candidate->id . '" data-user-id="' . $candidate->user_id . '">
                <label class="form-check-label"></label>
            </div>';
        });

        $dt->addColumn('name', function ($candidate) {
            $html = '<div class="candidate-info">';
            $html .= '<div class="candidate-name font-weight-bold">' . $candidate->user_name . '</div>';
            $html .= '<div class="candidate-details">';
            $html .= '<div><i class="ph ph-envelope-simple"></i> ' . $candidate->user_email . '</div>';
            $html .= '<div><i class="ph ph-phone"></i> ' . $candidate->user_phone . '</div>';
            if ($candidate->user_nik) {
                $html .= '<div><i class="ph ph-identification-card"></i> ' . $candidate->user_nik . '</div>';
            }
            $html .= '</div>';
            $html .= '</div>';

            return $html;
        });

        $dt->addColumn('education', function ($candidate) {
            return $candidate->education_name ?? '-';
        });

        $dt->addColumn('applied_jobs', function ($candidate) {
            if ($candidate->applied_jobs_count > 0) {
                return '<button type="button" class="btn btn-sm btn-info view-applied-jobs" data-id="' . $candidate->id . '">' . $candidate->applied_jobs_count . ' Pekerjaan</button>';
            } else {
                return '<span class="badge badge-secondary">Belum melamar</span>';
            }
        });

        $dt->addColumn('documents', function ($candidate) {
            return '<button type="button" class="btn btn-sm btn-primary view-documents" data-id="' . $candidate->id . '" data-toggle="tooltip" title="Lihat Dokumen"><i class="fas fa-file-alt"></i></button>';
        });

        $dt->addColumn('created_at', function ($candidate) {
            return $candidate->created_at;
        });

        $dt->addColumn('status', function ($candidate) {
            $checked = $candidate->user_status == 1 ? 'checked' : '';
            return '<div class="form-check form-switch">
                <input class="form-check-input candidate-status" type="checkbox" ' . $checked . ' data-id="' . $candidate->user_id . '" role="switch" id="candidate_status_' . $candidate->user_id . '">
                <label class="form-check-label" for="candidate_status_' . $candidate->user_id . '"></label>
            </div>';
        });

        $dt->addColumn('action', function ($candidate) {
            $viewBtn = '<button type="button" class="btn btn-sm btn-info view-profile mr-1" data-id="' . $candidate->id . '" data-toggle="tooltip" title="Lihat Profil"><i class="fas fa-eye"></i></button>';
            $editBtn = '<a href="' . route('candidate.edit', $candidate->id) . '" class="btn btn-sm btn-warning mr-1" data-toggle="tooltip" title="Edit"><i class="fas fa-edit"></i></a>';
            $deleteBtn = '<form action="' . route('candidate.destroy', $candidate->id) . '" method="POST" class="d-inline delete-form">
                ' . method_field('DELETE') . csrf_field() . '
                <button type="button" class="btn btn-sm btn-danger delete-btn" data-toggle="tooltip" title="Hapus"><i class="fas fa-trash"></i></button>
            </form>';

            return $viewBtn . $editBtn . $deleteBtn;
        });

        $dt->rawColumns(['checkbox', 'name', 'applied_jobs', 'documents', 'status', 'action']);

        return $dt->make(true);
    }
}
