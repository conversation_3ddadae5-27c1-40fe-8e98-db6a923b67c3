<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Candidate;
use App\Models\CandidateResume;
use Illuminate\Http\Request;

class CandidateDocumentController extends Controller
{
    /**
     * Get candidate documents
     *
     * @param Request $request
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function getDocuments(Request $request, $id)
    {
        try {
            $candidate = Candidate::with('user')->findOrFail($id);

            // Get CV
            $resume = CandidateResume::where('candidate_id', $id)->latest()->first();
            $cvPath = null;
            if ($resume && $resume->file) {
                $cvPath = asset($resume->file);
            }

            // Get AK1/KTP
            $ak1Path = null;
            if ($candidate->user && $candidate->user->ak1) {
                // Gunakan path public/storage/
                $ak1Path = asset('public/storage/' . $candidate->user->ak1);

                // Log untuk debugging
                \Log::info('AK1 Path', [
                    'original' => $candidate->user->ak1,
                    'full_path' => $ak1Path
                ]);
            }

            return response()->json([
                'success' => true,
                'data' => [
                    'cv' => $cvPath,
                    'ak1' => $ak1Path
                ]
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get candidate profile
     *
     * @param Request $request
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function getProfile(Request $request, $id)
    {
        try {
            $candidate = Candidate::with(['user', 'education', 'profession', 'skills', 'certifications'])->findOrFail($id);

            $data = [
                'name' => $candidate->user->name ?? '',
                'email' => $candidate->user->email ?? '',
                'phone' => $candidate->user->no_hp ?? '',
                'nik' => $candidate->user->nik ?? '',
                'gender' => $candidate->gender == 'male' ? 'Laki-laki' : 'Perempuan',
                'birth_date' => $candidate->birth_date ?? '',
                'address' => $candidate->user->alamat_ktp ?? '',
                'education' => $candidate->education->name ?? '',
                'profession' => $candidate->profession->name ?? '',
                'bio' => $candidate->bio ?? '',
                'photo' => $candidate->photo ? asset($candidate->photo) : null,
                'skills' => $candidate->skills->pluck('name')->toArray(),
                'detail_url' => route('candidate.show', $candidate->id),
                'edit_url' => route('candidate.edit', $candidate->id),
                'disabilitas' => $candidate->disabilitas ?? 0,
                'jenis_disabilitas' => $candidate->jenis_disabilitas ?? null,
                'candidate' => [
                    'certifications' => $candidate->certifications
                ]
            ];

            return response()->json([
                'success' => true,
                'data' => $data
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get candidate applied jobs
     *
     * @param Request $request
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function getAppliedJobs(Request $request, $id)
    {
        try {
            // Pastikan kandidat ada
            Candidate::findOrFail($id);

            // Gunakan model AppliedJob untuk mendapatkan data lamaran
            $appliedJobs = \App\Models\AppliedJob::with(['job', 'job.company.user'])
                ->where('candidate_id', $id)
                ->latest()
                ->get();

            $data = $appliedJobs->map(function($appliedJob) {
                // Default status
                $statusClass = 'secondary';
                $status = 'Dilamar';

                // Jika menggunakan application_group_id
                if ($appliedJob->applicationGroup) {
                    $groupName = $appliedJob->applicationGroup->name ?? '';

                    // Tentukan status berdasarkan nama grup
                    if (stripos($groupName, 'ditolak') !== false) {
                        $statusClass = 'danger';
                        $status = 'Ditolak';
                    } elseif (stripos($groupName, 'shortlist') !== false) {
                        $statusClass = 'primary';
                        $status = 'Shortlist';
                    } elseif (stripos($groupName, 'diterima') !== false || stripos($groupName, 'selected') !== false) {
                        $statusClass = 'success';
                        $status = 'Diterima';
                    } else {
                        $statusClass = 'info';
                        $status = $groupName;
                    }
                }

                // Periksa apakah job dan company masih ada
                $jobTitle = 'Tidak ada judul';
                $companyName = 'Tidak ada perusahaan';

                if ($appliedJob->job) {
                    $jobTitle = $appliedJob->job->title ?? 'Tidak ada judul';

                    if ($appliedJob->job->company && $appliedJob->job->company->user) {
                        $companyName = $appliedJob->job->company->user->name ?? 'Tidak ada perusahaan';
                    }
                }

                return [
                    'job_title' => $jobTitle,
                    'company_name' => $companyName,
                    'applied_date' => $appliedJob->created_at->format('d M Y'),
                    'status' => $status,
                    'status_class' => $statusClass
                ];
            });

            return response()->json([
                'success' => true,
                'data' => $data
            ]);
        } catch (\Exception $e) {
            // Log error untuk debugging
            \Log::error('Error in getAppliedJobs: ' . $e->getMessage());
            \Log::error('Error trace: ' . $e->getTraceAsString());

            return response()->json([
                'success' => false,
                'message' => 'Terjadi kesalahan saat memuat data pekerjaan: ' . $e->getMessage()
            ], 500);
        }
    }
}
