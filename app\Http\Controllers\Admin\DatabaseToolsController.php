<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Artisan;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\File;
use Carbon\Carbon;
use App\Services\LogService;

class DatabaseToolsController extends Controller
{
    /**
     * Display the database tools dashboard
     *
     * @return \Illuminate\View\View
     */
    public function index()
    {
        abort_if(!userCan('setting.view'), 403);

        // Get list of backups
        $backups = $this->getBackups();

        // Get cloud storage settings
        $cloudSettings = $this->getCloudSettings();

        // Get backup schedule settings
        $backupSchedule = $this->getBackupSchedule();

        return view('backend.settings.pages.database-tools.index', compact('backups', 'cloudSettings', 'backupSchedule'));
    }

    /**
     * Get list of backups
     *
     * @return array
     */
    private function getBackups()
    {
        $backups = [];
        $backupPath = storage_path('app/laravel-backup');

        // Create backup directory if it doesn't exist
        if (!File::exists($backupPath)) {
            File::makeDirectory($backupPath, 0755, true);
        }

        $files = File::files($backupPath);

        foreach ($files as $file) {
            // Only include zip files
            if (pathinfo($file->getFilename(), PATHINFO_EXTENSION) === 'zip') {
                $backups[] = [
                    'name' => $file->getFilename(),
                    'size' => $this->formatBytes($file->getSize()),
                    'date' => Carbon::createFromTimestamp($file->getMTime())->format('d M Y H:i:s'),
                    'path' => $file->getPathname(),
                ];
            }
        }

        // Sort by date (newest first)
        usort($backups, function($a, $b) {
            return strtotime($b['date']) - strtotime($a['date']);
        });

        return $backups;
    }

    /**
     * Get cloud storage settings
     *
     * @return array
     */
    private function getCloudSettings()
    {
        return [
            'google_drive' => [
                'enabled' => config('backup.cloud.google_drive.enabled', false),
                'client_id' => config('backup.cloud.google_drive.client_id', ''),
                'client_secret' => config('backup.cloud.google_drive.client_secret', ''),
                'refresh_token' => config('backup.cloud.google_drive.refresh_token', ''),
                'folder_id' => config('backup.cloud.google_drive.folder_id', ''),
            ],
            'amazon_s3' => [
                'enabled' => config('backup.cloud.amazon_s3.enabled', false),
                'key' => config('backup.cloud.amazon_s3.key', ''),
                'secret' => config('backup.cloud.amazon_s3.secret', ''),
                'region' => config('backup.cloud.amazon_s3.region', ''),
                'bucket' => config('backup.cloud.amazon_s3.bucket', ''),
            ],
            'backblaze' => [
                'enabled' => config('backup.cloud.backblaze.enabled', false),
                'key_id' => config('backup.cloud.backblaze.key_id', ''),
                'application_key' => config('backup.cloud.backblaze.application_key', ''),
                'bucket' => config('backup.cloud.backblaze.bucket', ''),
            ],
        ];
    }

    /**
     * Get backup schedule settings
     *
     * @return array
     */
    private function getBackupSchedule()
    {
        return [
            'enabled' => config('backup-cloud.schedule.enabled', false),
            'frequency' => config('backup-cloud.schedule.frequency', 'daily'),
            'time' => config('backup-cloud.schedule.time', '00:00'),
            'keep_days' => config('backup-cloud.schedule.keep_days', 7),
        ];
    }

    /**
     * Update backup schedule settings
     *
     * @param Request $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function updateBackupSchedule(Request $request)
    {
        abort_if(!userCan('setting.update'), 403);

        $request->validate([
            'frequency' => 'required|in:daily,weekly,monthly',
            'time' => 'required|date_format:H:i',
            'keep_days' => 'required|integer|min:1|max:365',
        ]);

        try {
            // Update environment variables
            $envPath = base_path('.env');
            $envContent = File::get($envPath);

            // Update or add backup schedule settings
            $settings = [
                'BACKUP_SCHEDULE_ENABLED' => $request->has('enable_schedule') ? 'true' : 'false',
                'BACKUP_SCHEDULE_FREQUENCY' => $request->frequency,
                'BACKUP_SCHEDULE_TIME' => $request->time,
                'BACKUP_SCHEDULE_KEEP_DAYS' => $request->keep_days,
            ];

            foreach ($settings as $key => $value) {
                if (preg_match("/^{$key}=.*$/m", $envContent)) {
                    $envContent = preg_replace("/^{$key}=.*$/m", "{$key}={$value}", $envContent);
                } else {
                    $envContent .= "\n{$key}={$value}";
                }
            }

            File::put($envPath, $envContent);

            // Clear config cache
            \Artisan::call('config:clear');
            \Artisan::call('config:cache');

            return redirect()->back()
                ->with('success', 'Pengaturan jadwal backup berhasil disimpan!');

        } catch (\Exception $e) {
            Log::error('Backup schedule update error: ' . $e->getMessage());
            return redirect()->back()
                ->with('error', 'Gagal menyimpan pengaturan: ' . $e->getMessage());
        }
    }

    /**
     * Create a new backup
     *
     * @param Request $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function createBackup(Request $request)
    {
        abort_if(!userCan('setting.update'), 403);

        try {
            // Determine backup type
            $backupType = $request->input('backup_type', 'db');

            // Create backup name with timestamp
            $timestamp = now()->format('Y-m-d-H-i-s');
            $backupName = "backup-{$timestamp}";

            // Ensure backup directory exists
            $backupPath = storage_path('app/laravel-backup');
            if (!File::exists($backupPath)) {
                File::makeDirectory($backupPath, 0755, true);
            }

            if ($backupType === 'db') {
                // Database only backup using Spatie DB Dumper
                $databaseName = config('database.connections.mysql.database');
                $username = config('database.connections.mysql.username');
                $password = config('database.connections.mysql.password');
                $host = config('database.connections.mysql.host');
                $port = config('database.connections.mysql.port', 3306);

                // Create dump file
                $dumpFile = storage_path("app/laravel-backup/{$backupName}.sql");

                // Use PHP's PDO to create a database dump
                try {
                    // Connect to the database
                    $dsn = "mysql:host={$host};port={$port};dbname={$databaseName}";
                    $pdo = new \PDO($dsn, $username, $password);
                    $pdo->setAttribute(\PDO::ATTR_ERRMODE, \PDO::ERRMODE_EXCEPTION);

                    // Get all tables
                    $tables = [];
                    $result = $pdo->query('SHOW TABLES');
                    while ($row = $result->fetch(\PDO::FETCH_NUM)) {
                        $tables[] = $row[0];
                    }

                    // Start the dump content
                    $dump = "-- Database dump created on " . date('Y-m-d H:i:s') . "\n";
                    $dump .= "-- Server: {$host}\n";
                    $dump .= "-- Database: {$databaseName}\n\n";
                    $dump .= "SET FOREIGN_KEY_CHECKS=0;\n\n";

                    // Process each table
                    foreach ($tables as $table) {
                        // Get create table statement
                        $stmt = $pdo->query("SHOW CREATE TABLE `{$table}`");
                        $row = $stmt->fetch(\PDO::FETCH_NUM);
                        $dump .= "DROP TABLE IF EXISTS `{$table}`;\n";
                        $dump .= $row[1] . ";\n\n";

                        // Get count of rows in table
                        $countStmt = $pdo->query("SELECT COUNT(*) FROM `{$table}`");
                        $rowCount = $countStmt->fetchColumn();

                        if ($rowCount > 0) {
                            // Get column names
                            $columnStmt = $pdo->query("SHOW COLUMNS FROM `{$table}`");
                            $columns = [];
                            while ($column = $columnStmt->fetch(\PDO::FETCH_ASSOC)) {
                                $columns[] = $column['Field'];
                            }

                            // Process in batches to avoid memory issues
                            $batchSize = 1000;
                            $batches = ceil($rowCount / $batchSize);

                            for ($batch = 0; $batch < $batches; $batch++) {
                                $offset = $batch * $batchSize;
                                $stmt = $pdo->query("SELECT * FROM `{$table}` LIMIT {$batchSize} OFFSET {$offset}");
                                $rows = $stmt->fetchAll(\PDO::FETCH_ASSOC);

                                if (count($rows) > 0) {
                                    $dump .= "INSERT INTO `{$table}` (`" . implode('`, `', $columns) . "`) VALUES\n";

                                    $values = [];
                                    foreach ($rows as $row) {
                                        $rowValues = [];
                                        foreach ($columns as $column) {
                                            $value = $row[$column] ?? null;
                                            if ($value === null) {
                                                $rowValues[] = 'NULL';
                                            } else {
                                                $rowValues[] = $pdo->quote($value);
                                            }
                                        }
                                        $values[] = "(" . implode(', ', $rowValues) . ")";
                                    }

                                    $dump .= implode(",\n", $values) . ";\n\n";
                                }

                                // Free up memory
                                unset($rows);
                                unset($values);
                                gc_collect_cycles();
                            }
                        }
                    }

                    $dump .= "SET FOREIGN_KEY_CHECKS=1;\n";

                    // Write the dump to file
                    File::put($dumpFile, $dump);

                    // Create zip file
                    $zipFile = storage_path("app/laravel-backup/{$backupName}.zip");
                    $zip = new \ZipArchive();
                    if ($zip->open($zipFile, \ZipArchive::CREATE) === TRUE) {
                        $zip->addFile($dumpFile, basename($dumpFile));
                        $zip->close();

                        // Remove the SQL file
                        File::delete($dumpFile);
                    } else {
                        throw new \Exception('Gagal membuat file zip');
                    }
                } catch (\Exception $e) {
                    Log::error('Database dump error: ' . $e->getMessage());
                    throw new \Exception('Backup database gagal: ' . $e->getMessage());
                }
            } else {
                // Full backup (database + files)
                try {
                    // Get database connection details
                    $databaseName = config('database.connections.mysql.database');
                    $username = config('database.connections.mysql.username');
                    $password = config('database.connections.mysql.password');
                    $host = config('database.connections.mysql.host');
                    $port = config('database.connections.mysql.port', 3306);

                    // First create database dump using PDO
                    $dsn = "mysql:host={$host};port={$port};dbname={$databaseName}";
                    $pdo = new \PDO($dsn, $username, $password);
                    $pdo->setAttribute(\PDO::ATTR_ERRMODE, \PDO::ERRMODE_EXCEPTION);

                    // Get all tables
                    $tables = [];
                    $result = $pdo->query('SHOW TABLES');
                    while ($row = $result->fetch(\PDO::FETCH_NUM)) {
                        $tables[] = $row[0];
                    }

                    // Start the dump content
                    $dump = "-- Database dump created on " . date('Y-m-d H:i:s') . "\n";
                    $dump .= "-- Server: {$host}\n";
                    $dump .= "-- Database: {$databaseName}\n\n";
                    $dump .= "SET FOREIGN_KEY_CHECKS=0;\n\n";

                    // Process each table
                    foreach ($tables as $table) {
                        // Get create table statement
                        $stmt = $pdo->query("SHOW CREATE TABLE `{$table}`");
                        $row = $stmt->fetch(\PDO::FETCH_NUM);
                        $dump .= "DROP TABLE IF EXISTS `{$table}`;\n";
                        $dump .= $row[1] . ";\n\n";

                        // Get count of rows in table
                        $countStmt = $pdo->query("SELECT COUNT(*) FROM `{$table}`");
                        $rowCount = $countStmt->fetchColumn();

                        if ($rowCount > 0) {
                            // Get column names
                            $columnStmt = $pdo->query("SHOW COLUMNS FROM `{$table}`");
                            $columns = [];
                            while ($column = $columnStmt->fetch(\PDO::FETCH_ASSOC)) {
                                $columns[] = $column['Field'];
                            }

                            // Process in batches to avoid memory issues
                            $batchSize = 1000;
                            $batches = ceil($rowCount / $batchSize);

                            for ($batch = 0; $batch < $batches; $batch++) {
                                $offset = $batch * $batchSize;
                                $stmt = $pdo->query("SELECT * FROM `{$table}` LIMIT {$batchSize} OFFSET {$offset}");
                                $rows = $stmt->fetchAll(\PDO::FETCH_ASSOC);

                                if (count($rows) > 0) {
                                    $dump .= "INSERT INTO `{$table}` (`" . implode('`, `', $columns) . "`) VALUES\n";

                                    $values = [];
                                    foreach ($rows as $row) {
                                        $rowValues = [];
                                        foreach ($columns as $column) {
                                            $value = $row[$column] ?? null;
                                            if ($value === null) {
                                                $rowValues[] = 'NULL';
                                            } else {
                                                $rowValues[] = $pdo->quote($value);
                                            }
                                        }
                                        $values[] = "(" . implode(', ', $rowValues) . ")";
                                    }

                                    $dump .= implode(",\n", $values) . ";\n\n";
                                }

                                // Free up memory
                                unset($rows);
                                unset($values);
                                gc_collect_cycles();
                            }
                        }
                    }

                    $dump .= "SET FOREIGN_KEY_CHECKS=1;\n";

                    // Write the dump to file
                    $dumpFile = storage_path("app/laravel-backup/{$backupName}.sql");
                    File::put($dumpFile, $dump);

                    // Create zip file for full backup
                    $zipFile = storage_path("app/laravel-backup/{$backupName}-full.zip");
                    $zip = new \ZipArchive();
                    if ($zip->open($zipFile, \ZipArchive::CREATE) === TRUE) {
                        // Add SQL file
                        $zip->addFile($dumpFile, basename($dumpFile));

                        // Add important directories
                        $this->addDirectoryToZip($zip, base_path('app'), 'app');
                        $this->addDirectoryToZip($zip, base_path('config'), 'config');
                        $this->addDirectoryToZip($zip, base_path('resources'), 'resources');
                        $this->addDirectoryToZip($zip, base_path('routes'), 'routes');
                        $this->addDirectoryToZip($zip, storage_path('app/public'), 'storage/app/public');

                        $zip->close();

                        // Remove the SQL file
                        File::delete($dumpFile);
                    } else {
                        throw new \Exception('Gagal membuat file zip');
                    }
                } catch (\Exception $e) {
                    Log::error('Full backup error: ' . $e->getMessage());
                    throw new \Exception('Backup penuh gagal: ' . $e->getMessage());
                }
            }

            // Log admin activity
            LogService::recordAdminActivity('backup', 'database', 'Created a new ' . ($backupType === 'db' ? 'database' : 'full') . ' backup');

            return redirect()->back()
                ->with('success', 'Backup berhasil dibuat!');
        } catch (\Exception $e) {
            Log::error('Backup error: ' . $e->getMessage());
            return redirect()->back()
                ->with('error', 'Backup gagal: ' . $e->getMessage());
        }
    }

    /**
     * Add directory to zip file
     *
     * @param \ZipArchive $zip
     * @param string $sourcePath
     * @param string $zipPath
     * @return void
     */
    private function addDirectoryToZip($zip, $sourcePath, $zipPath)
    {
        if (!File::exists($sourcePath)) {
            return;
        }

        // Get all files in the directory
        $files = File::allFiles($sourcePath);
        $totalFiles = count($files);
        $addedFiles = 0;

        // Set maximum file size to add (10MB)
        $maxFileSize = 10 * 1024 * 1024;

        // Add files to zip
        foreach ($files as $file) {
            try {
                // Skip files larger than max size
                if ($file->getSize() > $maxFileSize) {
                    Log::info("Skipping large file: {$file->getPathname()} ({$this->formatBytes($file->getSize())})");
                    continue;
                }

                // Skip files in vendor directory to avoid memory issues
                if (strpos($file->getPathname(), 'vendor') !== false) {
                    continue;
                }

                // Skip files in node_modules directory
                if (strpos($file->getPathname(), 'node_modules') !== false) {
                    continue;
                }

                // Skip log files
                if (pathinfo($file->getPathname(), PATHINFO_EXTENSION) === 'log') {
                    continue;
                }

                $relativePath = $zipPath . '/' . $file->getRelativePathname();
                $zip->addFile($file->getPathname(), $relativePath);
                $addedFiles++;

                // Free up memory
                if ($addedFiles % 100 === 0) {
                    gc_collect_cycles();
                }
            } catch (\Exception $e) {
                Log::warning("Failed to add file to zip: {$file->getPathname()} - {$e->getMessage()}");
            }
        }

        Log::info("Added {$addedFiles} of {$totalFiles} files from {$sourcePath} to backup");
    }

    /**
     * Download a backup
     *
     * @param Request $request
     * @return \Symfony\Component\HttpFoundation\BinaryFileResponse
     */
    public function downloadBackup(Request $request)
    {
        abort_if(!userCan('setting.view'), 403);

        $backupPath = $request->input('path');

        if (!File::exists($backupPath)) {
            return redirect()->back()
                ->with('error', 'File backup tidak ditemukan!');
        }

        // Log admin activity
        LogService::recordAdminActivity('download', 'database', 'Downloaded a backup file: ' . basename($backupPath));

        return response()->download($backupPath);
    }

    /**
     * Delete a backup
     *
     * @param Request $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function deleteBackup(Request $request)
    {
        abort_if(!userCan('setting.update'), 403);

        $backupPath = $request->input('path');

        if (!File::exists($backupPath)) {
            return redirect()->back()
                ->with('error', 'File backup tidak ditemukan!');
        }

        // Delete the file
        File::delete($backupPath);

        // Log admin activity
        LogService::recordAdminActivity('delete', 'database', 'Deleted a backup file: ' . basename($backupPath));

        return redirect()->back()
            ->with('success', 'Backup berhasil dihapus!');
    }

    /**
     * Restore a backup
     *
     * @param Request $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function restoreBackup(Request $request)
    {
        abort_if(!userCan('setting.update'), 403);

        // Validate confirmation
        $request->validate([
            'confirmation' => 'required|in:RESTORE',
            'accept_risks' => 'required|accepted',
        ]);

        $backupPath = $request->input('path');

        if (!File::exists($backupPath)) {
            return redirect()->back()
                ->with('error', 'File backup tidak ditemukan!');
        }

        try {
            // Create a temporary directory for extraction
            $tempDir = storage_path('app/temp-restore-' . time());
            if (!File::exists($tempDir)) {
                File::makeDirectory($tempDir, 0755, true);
            }

            // Extract the backup file
            $zip = new \ZipArchive();
            if ($zip->open($backupPath) === TRUE) {
                $zip->extractTo($tempDir);
                $zip->close();

                // Look for SQL files
                $sqlFiles = File::glob($tempDir . '/*.sql');

                if (count($sqlFiles) > 0) {
                    // Get database connection details
                    $databaseName = config('database.connections.mysql.database');
                    $username = config('database.connections.mysql.username');
                    $password = config('database.connections.mysql.password');
                    $host = config('database.connections.mysql.host');
                    $port = config('database.connections.mysql.port', 3306);

                    // Import the SQL file using mysql command
                    $sqlFile = $sqlFiles[0]; // Use the first SQL file found

                    // Use mysql command to import
                    $command = "mysql --host={$host} --port={$port} --user={$username} --password='{$password}' {$databaseName} < {$sqlFile}";
                    exec($command, $output, $returnVar);

                    if ($returnVar !== 0) {
                        throw new \Exception('Database restore failed: ' . implode("\n", $output));
                    }
                } else {
                    throw new \Exception('Tidak ada file SQL ditemukan dalam backup');
                }

                // Clean up
                File::deleteDirectory($tempDir);

                // Log admin activity
                LogService::recordAdminActivity('restore', 'database', 'Restored from backup: ' . basename($backupPath));

                return redirect()->back()
                    ->with('success', 'Restore berhasil dilakukan!');
            } else {
                throw new \Exception('Gagal membuka file backup');
            }
        } catch (\Exception $e) {
            // Clean up if temp directory exists
            if (isset($tempDir) && File::exists($tempDir)) {
                File::deleteDirectory($tempDir);
            }

            Log::error('Restore error: ' . $e->getMessage());
            return redirect()->back()
                ->with('error', 'Restore gagal: ' . $e->getMessage());
        }
    }

    /**
     * Reset database
     *
     * @param Request $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function resetDatabase(Request $request)
    {
        abort_if(!userCan('setting.update'), 403);

        // Validate confirmation
        $request->validate([
            'confirmation' => 'required|in:HAPUS',
            'accept_risks' => 'required|accepted',
        ]);

        try {
            // Run database migrations fresh with seed
            Artisan::call('migrate:fresh', ['--seed' => true]);

            // Log admin activity
            LogService::recordAdminActivity('reset', 'database', 'Reset database to default state');

            return redirect()->back()
                ->with('success', 'Database berhasil direset ke kondisi awal!');
        } catch (\Exception $e) {
            Log::error('Database reset error: ' . $e->getMessage());
            return redirect()->back()
                ->with('error', 'Reset database gagal: ' . $e->getMessage());
        }
    }

    /**
     * Optimize database
     *
     * @return \Illuminate\Http\RedirectResponse
     */
    public function optimizeDatabase()
    {
        abort_if(!userCan('setting.update'), 403);

        try {
            // Get all tables
            $tables = DB::select('SHOW TABLES');
            $dbName = 'Tables_in_' . config('database.connections.mysql.database');

            $optimizedTables = 0;
            $errorTables = [];

            // First analyze tables
            foreach ($tables as $table) {
                try {
                    $tableName = $table->$dbName;
                    DB::statement("ANALYZE TABLE `{$tableName}`");
                } catch (\Exception $e) {
                    Log::warning("Failed to analyze table {$tableName}: " . $e->getMessage());
                    $errorTables[] = $tableName;
                }
            }

            // Then optimize tables
            foreach ($tables as $table) {
                try {
                    $tableName = $table->$dbName;
                    DB::statement("OPTIMIZE TABLE `{$tableName}`");
                    $optimizedTables++;
                } catch (\Exception $e) {
                    Log::warning("Failed to optimize table {$tableName}: " . $e->getMessage());
                    if (!in_array($tableName, $errorTables)) {
                        $errorTables[] = $tableName;
                    }
                }
            }

            // Run Laravel's optimize commands
            Artisan::call('optimize:clear');
            Artisan::call('optimize');

            // Log admin activity
            LogService::recordAdminActivity('optimize', 'database', "Optimized {$optimizedTables} database tables");

            if (count($errorTables) > 0) {
                return redirect()->back()
                    ->with('warning', "Database dioptimasi sebagian! {$optimizedTables} tabel berhasil dioptimasi, tetapi " . count($errorTables) . " tabel gagal dioptimasi.");
            }

            return redirect()->back()
                ->with('success', 'Database berhasil dioptimasi!');
        } catch (\Exception $e) {
            Log::error('Database optimization error: ' . $e->getMessage());
            return redirect()->back()
                ->with('error', 'Optimasi database gagal: ' . $e->getMessage());
        }
    }

    /**
     * Restore from uploaded backup file
     *
     * @param Request $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function restoreUploadBackup(Request $request)
    {
        abort_if(!userCan('setting.update'), 403);

        try {
            // Validate file
            $request->validate([
                'backup_file' => 'required|file|mimes:zip,sql,plain|max:50000',
            ]);

            // Create a temporary directory for extraction
            $tempDir = storage_path('app/temp-restore-' . time());
            if (!File::exists($tempDir)) {
                File::makeDirectory($tempDir, 0755, true);
            }

            $file = $request->file('backup_file');
            $extension = $file->getClientOriginalExtension();

            // Get database connection details
            $databaseName = config('database.connections.mysql.database');
            $username = config('database.connections.mysql.username');
            $password = config('database.connections.mysql.password');
            $host = config('database.connections.mysql.host');
            $port = config('database.connections.mysql.port', 3306);

            if ($extension === 'zip') {
                // Handle zip file
                $zipPath = $tempDir . '/backup.zip';
                $file->move($tempDir, 'backup.zip');

                // Extract the backup file
                $zip = new \ZipArchive();
                if ($zip->open($zipPath) === TRUE) {
                    $zip->extractTo($tempDir);
                    $zip->close();

                    // Look for SQL files
                    $sqlFiles = File::glob($tempDir . '/*.sql');

                    if (count($sqlFiles) > 0) {
                        // Import the SQL file using mysql command
                        $sqlFile = $sqlFiles[0]; // Use the first SQL file found

                        // Use mysql command to import
                        $command = "mysql --host={$host} --port={$port} --user={$username} --password='{$password}' {$databaseName} < {$sqlFile}";
                        exec($command, $output, $returnVar);

                        if ($returnVar !== 0) {
                            throw new \Exception('Database restore failed: ' . implode("\n", $output));
                        }
                    } else {
                        throw new \Exception('Tidak ada file SQL ditemukan dalam backup');
                    }
                } else {
                    throw new \Exception('Gagal membuka file backup');
                }
            } else {
                // Handle SQL file directly
                $sqlPath = $tempDir . '/backup.sql';
                $file->move($tempDir, 'backup.sql');

                // Use mysql command to import
                $command = "mysql --host={$host} --port={$port} --user={$username} --password='{$password}' {$databaseName} < {$sqlPath}";
                exec($command, $output, $returnVar);

                if ($returnVar !== 0) {
                    throw new \Exception('Database restore failed: ' . implode("\n", $output));
                }
            }

            // Clean up
            File::deleteDirectory($tempDir);

            // Log admin activity
            LogService::recordAdminActivity('restore', 'database', 'Restored from uploaded backup file');

            return redirect()->back()
                ->with('success', 'Restore dari file berhasil dilakukan!');
        } catch (\Exception $e) {
            // Clean up if temp directory exists
            if (isset($tempDir) && File::exists($tempDir)) {
                File::deleteDirectory($tempDir);
            }

            Log::error('Restore upload error: ' . $e->getMessage());
            return redirect()->back()
                ->with('error', 'Restore gagal: ' . $e->getMessage());
        }
    }

    /**
     * Format bytes to human readable format
     *
     * @param int $bytes
     * @param int $precision
     * @return string
     */
    private function formatBytes($bytes, $precision = 2)
    {
        $units = ['B', 'KB', 'MB', 'GB', 'TB'];

        $bytes = max($bytes, 0);
        $pow = floor(($bytes ? log($bytes) : 0) / log(1024));
        $pow = min($pow, count($units) - 1);

        $bytes /= (1 << (10 * $pow));

        return round($bytes, $precision) . ' ' . $units[$pow];
    }

    /**
     * Delete all candidates and their related data
     *
     * @param Request $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function deleteCandidates(Request $request)
    {
        abort_if(!userCan('setting.update'), 403);

        try {
            // Validasi password admin
            if (!auth()->guard('admin')->attempt(['email' => auth()->guard('admin')->user()->email, 'password' => $request->admin_password])) {
                return back()->withErrors(['admin_password' => 'Password admin tidak valid.']);
            }

            DB::beginTransaction();

            // Hapus semua data terkait candidates
            $candidateIds = \App\Models\Candidate::pluck('id');
            $userIds = \App\Models\User::where('role', 'candidate')->pluck('id');

            // Hapus data terkait candidates
            \App\Models\CandidateExperience::whereIn('candidate_id', $candidateIds)->delete();
            \App\Models\CandidateEducation::whereIn('candidate_id', $candidateIds)->delete();
            \App\Models\CandidateCertification::whereIn('candidate_id', $candidateIds)->delete();
            \App\Models\CandidateResume::whereIn('candidate_id', $candidateIds)->delete();
            \App\Models\CandidateCvView::whereIn('candidate_id', $candidateIds)->delete();
            \App\Models\AppliedJob::whereIn('candidate_id', $candidateIds)->delete();

            // Hapus bookmark tables dengan pengecekan keberadaan tabel
            if (Schema::hasTable('bookmark_company')) {
                DB::table('bookmark_company')->whereIn('candidate_id', $candidateIds)->delete();
            }
            if (Schema::hasTable('bookmark_candidate_job')) {
                DB::table('bookmark_candidate_job')->whereIn('candidate_id', $candidateIds)->delete();
            }
            if (Schema::hasTable('bookmark_candidate_company')) {
                DB::table('bookmark_candidate_company')->whereIn('candidate_id', $candidateIds)->delete();
            }

            // Hapus pivot tables dengan pengecekan keberadaan tabel
            if (Schema::hasTable('candidate_skill')) {
                DB::table('candidate_skill')->whereIn('candidate_id', $candidateIds)->delete();
            }
            if (Schema::hasTable('candidate_language')) {
                DB::table('candidate_language')->whereIn('candidate_id', $candidateIds)->delete();
            }

            // Hapus candidates
            \App\Models\Candidate::whereIn('id', $candidateIds)->delete();

            // Hapus users dengan role candidate
            \App\Models\User::where('role', 'candidate')->delete();

            DB::commit();

            // Log aktivitas
            LogService::recordAdminActivity('Hapus Semua Pencaker', 'Database Tools', 'Menghapus semua data pencaker');

            flashSuccess('Semua data pencaker berhasil dihapus.');
            return back();

        } catch (\Exception $e) {
            DB::rollback();
            Log::error('Error deleting candidates: ' . $e->getMessage());
            flashError('Terjadi kesalahan: ' . $e->getMessage());
            return back();
        }
    }

    /**
     * Delete all companies and their related data
     *
     * @param Request $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function deleteCompanies(Request $request)
    {
        abort_if(!userCan('setting.update'), 403);

        try {
            // Validasi password admin
            if (!auth()->guard('admin')->attempt(['email' => auth()->guard('admin')->user()->email, 'password' => $request->admin_password])) {
                return back()->withErrors(['admin_password' => 'Password admin tidak valid.']);
            }

            DB::beginTransaction();

            // Hapus semua data terkait companies
            $companyIds = \App\Models\Company::pluck('id');
            $userIds = \App\Models\User::where('role', 'company')->pluck('id');
            $jobIds = \App\Models\Job::whereIn('company_id', $companyIds)->pluck('id');

            // Hapus data terkait jobs
            \App\Models\AppliedJob::whereIn('job_id', $jobIds)->delete();

            // Hapus pivot tables dengan pengecekan keberadaan tabel
            if (Schema::hasTable('bookmark_candidate_job')) {
                DB::table('bookmark_candidate_job')->whereIn('job_id', $jobIds)->delete();
            }
            if (Schema::hasTable('job_skills')) {
                DB::table('job_skills')->whereIn('job_id', $jobIds)->delete();
            }
            if (Schema::hasTable('job_benefit')) {
                DB::table('job_benefit')->whereIn('job_id', $jobIds)->delete();
            }
            if (Schema::hasTable('job_tag')) {
                DB::table('job_tag')->whereIn('job_id', $jobIds)->delete();
            }
            if (Schema::hasTable('job_disability_type')) {
                DB::table('job_disability_type')->whereIn('job_id', $jobIds)->delete();
            }

            // Hapus jobs
            \App\Models\Job::whereIn('company_id', $companyIds)->delete();

            // Hapus data terkait companies dengan pengecekan keberadaan tabel
            if (Schema::hasTable('bookmark_company')) {
                DB::table('bookmark_company')->whereIn('company_id', $companyIds)->delete();
            }
            if (Schema::hasTable('bookmark_candidate_company')) {
                DB::table('bookmark_candidate_company')->whereIn('company_id', $companyIds)->delete();
            }
            if (Schema::hasTable('company_applied_job_rejected')) {
                DB::table('company_applied_job_rejected')->whereIn('company_id', $companyIds)->delete();
            }
            if (Schema::hasTable('company_questions')) {
                DB::table('company_questions')->whereIn('company_id', $companyIds)->delete();
            }
            if (Schema::hasTable('earnings')) {
                DB::table('earnings')->whereIn('company_id', $companyIds)->delete();
            }
            if (Schema::hasTable('messenger_users')) {
                DB::table('messenger_users')->whereIn('company_id', $companyIds)->delete();
            }

            \App\Models\CandidateCvView::whereIn('company_id', $companyIds)->delete();
            \App\Models\UserPlan::whereIn('company_id', $companyIds)->delete();

            // Hapus companies
            \App\Models\Company::whereIn('id', $companyIds)->delete();

            // Hapus users dengan role company
            \App\Models\User::where('role', 'company')->delete();

            DB::commit();

            // Log aktivitas
            LogService::recordAdminActivity('Hapus Semua Perusahaan', 'Database Tools', 'Menghapus semua data perusahaan');

            flashSuccess('Semua data perusahaan berhasil dihapus.');
            return back();

        } catch (\Exception $e) {
            DB::rollback();
            Log::error('Error deleting companies: ' . $e->getMessage());
            flashError('Terjadi kesalahan: ' . $e->getMessage());
            return back();
        }
    }
}
