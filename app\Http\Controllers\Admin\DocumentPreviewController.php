<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\User;
use Illuminate\Http\Request;

class DocumentPreviewController extends Controller
{
    /**
     * Preview dokumen perusahaan
     *
     * @param Request $request
     * @return \Illuminate\Http\Response
     */
    public function previewDocument(Request $request)
    {
        try {
            // Validasi request
            $request->validate([
                'user_id' => 'required|exists:users,id',
            ]);

            $user = User::findOrFail($request->user_id);

            if (!$user->ak1) {
                return response()->json(['error' => 'Dokumen tidak ditemukan'], 404);
            }

            // Dapatkan path file
            $filePath = $user->ak1;
            $fullPath = storage_path('app/public/' . $filePath);

            // Log untuk debugging
            \Log::info('Document Preview Request', [
                'user_id' => $user->id,
                'ak1_path' => $filePath,
                'full_path' => $fullPath,
                'file_exists' => file_exists($fullPath)
            ]);

            // Periksa apakah file ada
            if (!file_exists($fullPath)) {
                return response()->json(['error' => 'File tidak ditemukan di server: ' . $fullPath], 404);
            }

            // Dapatkan konten file
            $fileContent = file_get_contents($fullPath);

            // Tentukan jenis konten berdasarkan ekstensi file
            $extension = strtolower(pathinfo($user->ak1, PATHINFO_EXTENSION));
            $contentType = $this->getContentType($extension);

            // Kembalikan file sebagai respons
            return response($fileContent)
                ->header('Content-Type', $contentType)
                ->header('Content-Disposition', 'inline; filename="document.' . $extension . '"');
        } catch (\Exception $e) {
            return response()->json(['error' => $e->getMessage()], 500);
        }
    }

    /**
     * Download dokumen perusahaan
     *
     * @param Request $request
     * @return \Illuminate\Http\Response
     */
    public function downloadDocument(Request $request)
    {
        try {
            // Validasi request
            $request->validate([
                'user_id' => 'required|exists:users,id',
            ]);

            $user = User::findOrFail($request->user_id);

            if (!$user->ak1) {
                return response()->json(['error' => 'Dokumen tidak ditemukan'], 404);
            }

            // Dapatkan path file
            $filePath = $user->ak1;
            $fullPath = public_path('storage/' . $filePath);

            // Log untuk debugging
            \Log::info('Document Download Request', [
                'user_id' => $user->id,
                'ak1_path' => $filePath,
                'full_path' => $fullPath,
                'file_exists' => file_exists($fullPath)
            ]);

            // Periksa apakah file ada
            if (!file_exists($fullPath)) {
                return response()->json(['error' => 'File tidak ditemukan di server: ' . $fullPath], 404);
            }

            // Tentukan nama file untuk download
            $extension = strtolower(pathinfo($user->ak1, PATHINFO_EXTENSION));
            $fileName = 'document_' . $user->id . '.' . $extension;

            // Download file
            return response()->download($fullPath, $fileName);
        } catch (\Exception $e) {
            return response()->json(['error' => $e->getMessage()], 500);
        }
    }

    /**
     * Dapatkan jenis konten berdasarkan ekstensi file
     *
     * @param string $extension
     * @return string
     */
    private function getContentType($extension)
    {
        switch ($extension) {
            case 'pdf':
                return 'application/pdf';
            case 'jpg':
            case 'jpeg':
                return 'image/jpeg';
            case 'png':
                return 'image/png';
            default:
                return 'application/octet-stream';
        }
    }
}
