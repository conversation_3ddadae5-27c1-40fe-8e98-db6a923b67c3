<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;

class FlyerController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index(Request $request)
    {
        try {
            $query = \App\Models\Flyer::with('company')->latest();

            // Filter by status
            if ($request->has('status') && $request->status != 'all') {
                $query->where('status', $request->status);
            }

            // Filter by company name
            if ($request->has('company') && $request->company) {
                $query->where('company_name', 'like', '%' . $request->company . '%');
            }

            $flyers = $query->paginate(10);

            return view('backend.flyer.index', compact('flyers'));
        } catch (\Exception $e) {
            flashError('Terjadi kesalahan: ' . $e->getMessage());
            return back();
        }
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function edit($id)
    {
        try {
            $flyer = \App\Models\Flyer::findOrFail($id);
            return view('backend.flyer.edit', compact('flyer'));
        } catch (\Exception $e) {
            flashError('Terjadi kesalahan: ' . $e->getMessage());
            return back();
        }
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, $id)
    {
        try {
            $request->validate([
                'status' => 'required|in:active,rejected,revision,processing',
                'rejection_reason' => 'required_if:status,rejected,revision',
                'job_id' => 'nullable|exists:jobs,id',
            ]);

            $flyer = \App\Models\Flyer::findOrFail($id);
            $flyer->status = $request->status;

            if ($request->status == 'rejected' || $request->status == 'revision') {
                $flyer->rejection_reason = $request->rejection_reason;
            } else {
                $flyer->rejection_reason = null;
            }

            // Handle job_id for active status
            if ($request->status == 'active' && $request->has('job_id')) {
                $flyer->job_id = $request->job_id;
            }

            // Set processing status
            if ($request->status == 'processing') {
                // Update flyer status to processing
                $flyer->status = 'processing';
            }

            $flyer->save();

            flashSuccess('Status flyer berhasil diperbarui.');
            return redirect()->route('admin.flyer.index');
        } catch (\Exception $e) {
            flashError('Terjadi kesalahan: ' . $e->getMessage());
            return back();
        }
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function destroy($id)
    {
        try {
            $flyer = \App\Models\Flyer::findOrFail($id);

            // Delete image
            if ($flyer->image && file_exists(public_path('uploads/flyers/' . $flyer->image))) {
                unlink(public_path('uploads/flyers/' . $flyer->image));
            }

            $flyer->delete();

            flashSuccess('Flyer berhasil dihapus.');
            return redirect()->route('admin.flyer.index');
        } catch (\Exception $e) {
            flashError('Terjadi kesalahan: ' . $e->getMessage());
            return back();
        }
    }

    /**
     * Set flyer status to processing.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function setProcessing($id)
    {
        try {
            $flyer = \App\Models\Flyer::findOrFail($id);
            $flyer->status = 'processing';
            $flyer->save();

            flashSuccess('Status flyer berhasil diubah menjadi Diproses.');
            return redirect()->route('job.create', ['company_id' => $flyer->company_id, 'flyer_id' => $flyer->id]);
        } catch (\Exception $e) {
            flashError('Terjadi kesalahan: ' . $e->getMessage());
            return back();
        }
    }
}
