<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Candidate;
use App\Models\Company;
use App\Models\Message;
use App\Models\MessageThread;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class InboxController extends Controller
{
    /**
     * Display a listing of the messages.
     *
     * @return \Illuminate\Http\Response
     */
    public function index(Request $request)
    {
        // Get filter parameters
        $filter = $request->query('filter', 'all');
        $search = $request->query('search');
        $page = $request->query('page', 1);
        $perPage = $request->query('per_page', 10);
        $threadId = $request->query('pesan_id');

        // Build query
        $query = MessageThread::query();

        // Apply filters
        if ($filter === 'company') {
            $query->whereNotNull('company_id');
        } elseif ($filter === 'candidate') {
            $query->whereNotNull('candidate_id');
        } elseif ($filter === 'admin') {
            $query->where('is_admin_thread', true);
        }

        // Apply search
        if ($search) {
            $query->where(function ($q) use ($search) {
                $q->where('subject', 'like', "%{$search}%")
                  ->orWhereHas('company.user', function ($q) use ($search) {
                      $q->where('name', 'like', "%{$search}%");
                  })
                  ->orWhereHas('candidate.user', function ($q) use ($search) {
                      $q->where('name', 'like', "%{$search}%");
                  })
                  ->orWhereHas('job', function ($q) use ($search) {
                      $q->where('title', 'like', "%{$search}%");
                  });
            });
        }

        // Get thread details if viewing a specific thread
        $thread = null;
        $messages = null;
        $hasMoreMessages = false;
        $totalMessages = 0;

        if ($threadId) {
            $thread = MessageThread::with(['company.user', 'candidate.user', 'job'])->find($threadId);

            if ($thread) {
                // Get only the 5 most recent messages
                $messages = $thread->messages()
                    ->with(['sender', 'receiver'])
                    ->orderBy('created_at', 'desc')
                    ->take(5)
                    ->get()
                    ->sortBy('created_at');

                // Count total messages for "load more" button
                $totalMessages = $thread->messages()->count();
                $hasMoreMessages = $totalMessages > 5;

                // Mark messages as read
                $thread->messages()
                    ->where('receiver_id', Auth::guard('admin')->id())
                    ->where('read', false)
                    ->update(['read' => true]);
            }
        }

        // Get threads with pagination
        $threads = $query->with([
            'company.user',
            'candidate.user',
            'job',
            'latestMessage',
            'unreadMessages' => function ($q) {
                $q->where('receiver_id', Auth::guard('admin')->id());
            }
        ])->latest()->paginate($perPage);

        // Get stats for cards
        $stats = [
            'total' => MessageThread::count(),
            'company' => MessageThread::whereNotNull('company_id')->count(),
            'candidate' => MessageThread::whereNotNull('candidate_id')->count(),
            'admin' => MessageThread::where('is_admin_thread', true)->count(),
            'read' => Message::where('read', true)->count(),
            'unread' => Message::where('read', false)->count(),
        ];

        return view('admin.inbox.index_no_ajax', compact(
            'threads',
            'thread',
            'messages',
            'threadId',
            'filter',
            'search',
            'stats',
            'hasMoreMessages',
            'totalMessages'
        ));
    }

    /**
     * Show the form for creating a new message.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        $companies = Company::with('user')->get();
        $candidates = Candidate::with('user')->get();

        return view('admin.inbox.create_no_ajax', compact('companies', 'candidates'));
    }

    /**
     * Store a newly created message in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        $request->validate([
            'recipient_type' => 'required|in:specific_company,specific_candidate',
            'recipient_id' => 'required',
            'subject' => 'required|string|max:255',
            'message' => 'required|string',
        ]);

        $admin = Auth::guard('admin')->user();

        if ($request->recipient_type === 'specific_company') {
            $company = Company::findOrFail($request->recipient_id);

            $thread = MessageThread::create([
                'company_id' => $company->id,
                'initiator_id' => $admin->id,
                'subject' => $request->subject,
                'is_admin_thread' => true,
            ]);

            Message::create([
                'message_thread_id' => $thread->id,
                'sender_id' => $admin->id,
                'sender_type' => 'admin',
                'receiver_id' => $company->user_id,
                'body' => $request->message,
                'read' => false,
            ]);

            return redirect()->route('admin.inbox.index', ['pesan_id' => $thread->id])
                ->with('success', 'Pesan berhasil dikirim ke perusahaan');
        }
        elseif ($request->recipient_type === 'specific_candidate') {
            $candidate = Candidate::findOrFail($request->recipient_id);

            $thread = MessageThread::create([
                'candidate_id' => $candidate->id,
                'initiator_id' => $admin->id,
                'subject' => $request->subject,
                'is_admin_thread' => true,
            ]);

            Message::create([
                'message_thread_id' => $thread->id,
                'sender_id' => $admin->id,
                'sender_type' => 'admin',
                'receiver_id' => $candidate->user_id,
                'body' => $request->message,
                'read' => false,
            ]);

            return redirect()->route('admin.inbox.index', ['pesan_id' => $thread->id])
                ->with('success', 'Pesan berhasil dikirim ke pencaker');
        }

        return redirect()->back()->with('error', 'Parameter tidak valid');
    }

    /**
     * Show the form for sending broadcast messages.
     *
     * @return \Illuminate\Http\Response
     */
    public function broadcast()
    {
        return view('admin.inbox.broadcast_no_ajax');
    }

    /**
     * Send broadcast messages.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function sendBroadcast(Request $request)
    {
        $request->validate([
            'recipient_type' => 'required|in:all_companies,all_candidates',
            'subject' => 'required|string|max:255',
            'message' => 'required|string',
        ]);

        $admin = Auth::guard('admin')->user();
        $canReply = $request->has('can_reply') ? 1 : 0;
        $successMessage = '';

        if ($request->recipient_type === 'all_companies') {
            $companies = Company::all();

            foreach ($companies as $company) {
                $thread = MessageThread::create([
                    'company_id' => $company->id,
                    'initiator_id' => $admin->id,
                    'subject' => $request->subject,
                    'is_admin_thread' => true,
                ]);

                Message::create([
                    'message_thread_id' => $thread->id,
                    'sender_id' => $admin->id,
                    'sender_type' => 'admin',
                    'receiver_id' => $company->user_id,
                    'body' => $request->message,
                    'read' => false,
                    'can_reply' => $canReply,
                ]);
            }

            $successMessage = 'Pesan broadcast berhasil dikirim ke semua perusahaan';
        }
        elseif ($request->recipient_type === 'all_candidates') {
            $candidates = Candidate::all();

            foreach ($candidates as $candidate) {
                $thread = MessageThread::create([
                    'candidate_id' => $candidate->id,
                    'initiator_id' => $admin->id,
                    'subject' => $request->subject,
                    'is_admin_thread' => true,
                ]);

                Message::create([
                    'message_thread_id' => $thread->id,
                    'sender_id' => $admin->id,
                    'sender_type' => 'admin',
                    'receiver_id' => $candidate->user_id,
                    'body' => $request->message,
                    'read' => false,
                    'can_reply' => $canReply,
                ]);
            }

            $successMessage = 'Pesan broadcast berhasil dikirim ke semua pencaker';
        }

        return redirect()->route('admin.inbox.index')->with('success', $successMessage);
    }

    /**
     * Reply to a message thread.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function reply(Request $request)
    {
        $request->validate([
            'thread_id' => 'required|exists:message_threads,id',
            'message' => 'nullable|string',
        ]);

        // Validasi tambahan: harus ada pesan atau lampiran
        if (empty($request->message) && !$request->hasFile('document_attachment') && !$request->hasFile('image_attachment')) {
            if ($request->ajax()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Silakan ketik pesan atau lampirkan file.'
                ], 422);
            }
            return redirect()->back()->withErrors(['message' => 'Silakan ketik pesan atau lampirkan file.']);
        }

        $admin = Auth::guard('admin')->user();
        $thread = MessageThread::findOrFail($request->thread_id);

        // Determine receiver
        $receiverId = null;
        if ($thread->company_id) {
            $receiverId = $thread->company->user_id;
        } elseif ($thread->candidate_id) {
            $receiverId = $thread->candidate->user_id;
        } else {
            if ($request->ajax()) {
                return response()->json(['success' => false, 'message' => 'Tidak dapat menentukan penerima pesan']);
            }
            return redirect()->back()->with('error', 'Tidak dapat menentukan penerima pesan');
        }

        // Check if file was uploaded
        $attachment = null;
        if ($request->hasFile('document_attachment')) {
            $file = $request->file('document_attachment');
            $fileName = time() . '_' . $file->getClientOriginalName();
            $path = $file->storeAs('attachments', $fileName, 'public');

            $attachment = [
                'url' => asset('public/storage/' . $path),
                'name' => $file->getClientOriginalName(),
                'type' => 'document',
                'size' => $file->getSize(),
            ];
        } elseif ($request->hasFile('image_attachment')) {
            $file = $request->file('image_attachment');
            $fileName = time() . '_' . $file->getClientOriginalName();
            $path = $file->storeAs('attachments', $fileName, 'public');

            $attachment = [
                'url' => asset('public/storage/' . $path),
                'name' => $file->getClientOriginalName(),
                'type' => 'image',
                'size' => $file->getSize(),
            ];
        }

        // Siapkan body pesan
        $messageBody = $request->message;

        // Jika tidak ada pesan tapi ada lampiran, buat pesan default
        if (empty($messageBody) && $attachment) {
            if ($attachment['type'] === 'document') {
                $messageBody = "[Dokumen] " . $attachment['name'];
            } elseif ($attachment['type'] === 'image') {
                $messageBody = "[Gambar] " . $attachment['name'];
            } else {
                $messageBody = "[Lampiran] " . $attachment['name'];
            }
        }

        // Create message
        $message = Message::create([
            'message_thread_id' => $thread->id,
            'sender_id' => $admin->id,
            'sender_type' => 'admin', // Penting: menandai bahwa pengirim adalah admin
            'receiver_id' => $receiverId,
            'body' => $messageBody,
            'read' => false,
            'can_reply' => $request->has('can_reply') ? 1 : 0,
            'attachment' => $attachment,
        ]);

        // If AJAX request, return JSON response
        if ($request->ajax()) {
            $senderName = 'Admin';
            $avatar = null;
            $isSender = true;

            // Build message HTML
            $messageHtml = view('admin.inbox.partials.message-item', compact(
                'message', 'isSender', 'senderName', 'avatar'
            ))->render();

            return response()->json([
                'success' => true,
                'message' => 'Balasan berhasil dikirim',
                'html' => $messageHtml
            ]);
        }

        return redirect()->route('admin.inbox.index', ['pesan_id' => $thread->id])
            ->with('success', 'Balasan berhasil dikirim');
    }

    /**
     * Load more messages for a thread
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function loadMoreMessages(Request $request)
    {
        $request->validate([
            'thread_id' => 'required|exists:message_threads,id',
            'skip' => 'required|integer|min:0',
        ]);

        $threadId = $request->input('thread_id');
        $skip = $request->input('skip', 5); // Skip the 5 most recent messages already loaded
        $take = 10; // Load 10 more messages

        $thread = MessageThread::findOrFail($threadId);

        $messages = $thread->messages()
            ->with(['sender', 'receiver'])
            ->orderBy('created_at', 'desc')
            ->skip($skip)
            ->take($take)
            ->get()
            ->sortBy('created_at');

        $remainingCount = $thread->messages()->count() - ($skip + $take);
        $hasMoreMessages = $remainingCount > 0;

        $html = '';
        foreach ($messages as $message) {
            // Determine if the current user is the sender
            $isSender = $message->sender_id == Auth::guard('admin')->id() && $message->sender_type == 'admin';

            // Get sender name and avatar
            $senderName = 'Unknown';
            $avatar = null;

            if ($message->sender_type == 'admin') {
                $senderName = 'Admin';
                // You can set a default admin avatar here
            } else {
                if ($thread->company_id && $message->sender_id == $thread->company->user_id) {
                    $senderName = $thread->company->user->name;
                    $avatar = $thread->company->logo ? asset('public/storage/' . $thread->company->logo) : null;
                } elseif ($thread->candidate_id && $message->sender_id == $thread->candidate->user_id) {
                    $senderName = $thread->candidate->user->name;
                    $avatar = $thread->candidate->photo ? asset('public/storage/' . $thread->candidate->photo) : null;
                }
            }

            // Build message HTML
            $messageHtml = view('admin.inbox.partials.message-item', compact(
                'message', 'isSender', 'senderName', 'avatar'
            ))->render();

            $html .= $messageHtml;
        }

        return response()->json([
            'success' => true,
            'html' => $html,
            'hasMoreMessages' => $hasMoreMessages,
            'remainingCount' => $remainingCount,
            'newSkip' => $skip + $take,
        ]);
    }
}
