<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\User;

class LocationController extends Controller
{
    public function getDistricts(Request $request)
    {
        $districts = User::where('role', 'candidate')
            ->where('provinsi', $request->province_id)
            ->whereNotNull('kecamatan')
            ->select('kecamatan')
            ->distinct()
            ->orderBy('kecamatan')
            ->pluck('kecamatan')
            ->map(function($item) {
                return [
                    'id' => $item,
                    'name' => $item
                ];
            });

        return response()->json($districts);
    }

    public function getVillages(Request $request)
    {
        $villages = User::where('role', 'candidate')
            ->where('kecamatan', $request->district_id)
            ->whereNotNull('kelurahan')
            ->select('kelurahan')
            ->distinct()
            ->orderBy('kelurahan')
            ->pluck('kelurahan')
            ->map(function($item) {
                return [
                    'id' => $item,
                    'name' => $item
                ];
            });

        return response()->json($villages);
    }
}
