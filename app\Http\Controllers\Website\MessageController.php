<?php

namespace App\Http\Controllers\Website;

use App\Http\Controllers\Controller;
use App\Models\AppliedJob;
use App\Models\Message;
use App\Models\MessageThread;
use App\Models\User;
use App\Notifications\BroadcastMessageNotification;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Notification;

class MessageController extends Controller
{
    public function companyInbox(Request $request)
    {
        $user = auth()->user();
        $threadId = $request->query('pesan_id');
        $page = $request->query('page', 1);
        $unreadOnly = $request->query('unread') === '1';
        $perPage = 10;
        $search = $request->query('cari');

        // Get threads based on user role
        $query = MessageThread::query();
        $query->where('company_id', $user->company->id);

        // Apply unread filter if needed
        if ($unreadOnly) {
            $query->whereHas('messages', function ($q) use ($user) {
                $q->where('read', false)
                  ->where('receiver_id', $user->id);
            });
        }

        // Apply search filter if provided
        if ($search) {
            $query->where(function($q) use ($search) {
                $q->where('subject', 'like', "%{$search}%")
                  ->orWhereHas('messages', function($mq) use ($search) {
                      $mq->where('body', 'like', "%{$search}%");
                  })
                  ->orWhereHas('candidate.user', function($cq) use ($search) {
                      $cq->where('name', 'like', "%{$search}%");
                  });
            });
        }

        // Get total count for pagination
        $totalThreads = $query->count();
        $totalPages = ceil($totalThreads / $perPage);

        // Get threads with relationships and order by latest message
        $threads = $query->with([
            'company.user',
            'candidate.user',
            'job',
            'latestMessage',
            'unreadMessages' => function ($q) use ($user) {
                $q->where('receiver_id', $user->id);
            }
        ])
        ->select('message_threads.*')
        ->leftJoin('messages', function($join) {
            $join->on('message_threads.id', '=', 'messages.message_thread_id')
                ->whereRaw('messages.id = (SELECT MAX(id) FROM messages WHERE message_thread_id = message_threads.id)');
        })
        ->orderBy('messages.created_at', 'desc')
        ->skip(($page - 1) * $perPage)
        ->take($perPage)
        ->get();

        // Get selected thread if provided
        $selectedThread = null;
        $messages = [];
        $hasMoreMessages = false;
        $totalMessages = 0;

        if ($threadId) {
            $selectedThread = MessageThread::with(['company.user', 'candidate.user', 'job'])->find($threadId);

            if ($selectedThread) {
                // Get the 5 most recent messages
                $messages = Message::where('message_thread_id', $threadId)
                    ->with([
                        'sender.company',
                        'sender.candidate',
                        'receiver.company',
                        'receiver.candidate'
                    ])
                    ->orderBy('created_at', 'desc')
                    ->take(5)
                    ->get()
                    ->sortBy('created_at');

                // Count total messages for "load more" button
                $totalMessages = Message::where('message_thread_id', $threadId)->count();
                $hasMoreMessages = $totalMessages > 5;

                // Mark messages as read
                Message::where('message_thread_id', $threadId)
                    ->where('receiver_id', $user->id)
                    ->where('read', false)
                    ->update(['read' => true]);
            }
        }

        // Group messages by date
        $groupedMessages = [];
        foreach ($messages as $message) {
            $date = $message->created_at->format('Y-m-d');
            if (!isset($groupedMessages[$date])) {
                $groupedMessages[$date] = [];
            }
            $groupedMessages[$date][] = $message;
        }

        // Definisikan variabel currentPage untuk pagination
        $currentPage = $page;

        return view('frontend.pages.company.inbox-no-ajax', compact(
            'threads',
            'selectedThread',
            'groupedMessages',
            'hasMoreMessages',
            'totalMessages',
            'currentPage',
            'totalPages',
            'unreadOnly',
            'perPage',
            'search'
        ));
    }

    public function candidateInbox(Request $request)
    {
        $user = auth()->user();
        $threadId = $request->query('pesan_id');
        $page = $request->query('page', 1);
        $unreadOnly = $request->query('unread') === '1';
        $perPage = 10;
        $search = $request->query('cari');

        // Get threads based on user role
        $query = MessageThread::query();
        $query->where('candidate_id', $user->candidate->id);

        // Apply unread filter if needed
        if ($unreadOnly) {
            $query->whereHas('messages', function ($q) use ($user) {
                $q->where('read', false)
                  ->where('receiver_id', $user->id);
            });
        }

        // Apply search filter if provided
        if ($search) {
            $query->where(function($q) use ($search) {
                $q->where('subject', 'like', "%{$search}%")
                  ->orWhereHas('messages', function($mq) use ($search) {
                      $mq->where('body', 'like', "%{$search}%");
                  })
                  ->orWhereHas('company.user', function($cq) use ($search) {
                      $cq->where('name', 'like', "%{$search}%");
                  });
            });
        }

        // Get total count for pagination
        $totalThreads = $query->count();
        $totalPages = ceil($totalThreads / $perPage);

        // Get threads with relationships and order by latest message
        $threads = $query->with([
            'company.user',
            'candidate.user',
            'job',
            'latestMessage',
            'unreadMessages' => function ($q) use ($user) {
                $q->where('receiver_id', $user->id);
            }
        ])
        ->select('message_threads.*')
        ->leftJoin('messages', function($join) {
            $join->on('message_threads.id', '=', 'messages.message_thread_id')
                ->whereRaw('messages.id = (SELECT MAX(id) FROM messages WHERE message_thread_id = message_threads.id)');
        })
        ->orderBy('messages.created_at', 'desc')
        ->skip(($page - 1) * $perPage)
        ->take($perPage)
        ->get();

        // Get selected thread if provided
        $selectedThread = null;
        $messages = [];
        $hasMoreMessages = false;
        $totalMessages = 0;

        if ($threadId) {
            $selectedThread = MessageThread::with(['company.user', 'candidate.user', 'job'])->find($threadId);

            if ($selectedThread) {
                // Get the 5 most recent messages
                $messages = Message::where('message_thread_id', $threadId)
                    ->with([
                        'sender.company',
                        'sender.candidate',
                        'receiver.company',
                        'receiver.candidate'
                    ])
                    ->orderBy('created_at', 'desc')
                    ->take(5)
                    ->get()
                    ->sortBy('created_at');

                // Count total messages for "load more" button
                $totalMessages = Message::where('message_thread_id', $threadId)->count();
                $hasMoreMessages = $totalMessages > 5;

                // Mark messages as read
                Message::where('message_thread_id', $threadId)
                    ->where('receiver_id', $user->id)
                    ->where('read', false)
                    ->update(['read' => true]);
            }
        }

        // Group messages by date
        $groupedMessages = [];
        foreach ($messages as $message) {
            $date = $message->created_at->format('Y-m-d');
            if (!isset($groupedMessages[$date])) {
                $groupedMessages[$date] = [];
            }
            $groupedMessages[$date][] = $message;
        }

        // Definisikan variabel currentPage untuk pagination
        $currentPage = $page;

        return view('frontend.pages.candidate.inbox-no-ajax', compact(
            'threads',
            'selectedThread',
            'groupedMessages',
            'hasMoreMessages',
            'totalMessages',
            'currentPage',
            'totalPages',
            'unreadOnly',
            'perPage',
            'search'
        ));
    }

    public function adminInbox(Request $request)
    {
        $threadId = $request->query('pesan_id');
        return view('admin.inbox.index', compact('threadId'));
    }

    public function getThreads(Request $request)
    {
        $user = Auth::user();
        $query = MessageThread::query();

        if ($user->role == 'company') {
            $query->where('company_id', currentCompany()->id);

            if ($request->has('job_id') && $request->job_id) {
                $query->where('job_id', $request->job_id);
            }
        } elseif ($user->role == 'candidate') {
            $query->where('candidate_id', currentCandidate()->id);
        } elseif ($user->role == 'admin') {
            // Admin dapat melihat semua thread
        }

        if ($request->has('unread') && $request->unread) {
            $query->whereHas('messages', function ($q) use ($user) {
                $q->where('read', false)
                  ->where('receiver_id', $user->id);
            });
        }

        $threads = $query->with([
            'company.user',
            'candidate.user',
            'job',
            'latestMessage',
            'unreadMessages' => function ($q) use ($user) {
                $q->where('receiver_id', $user->id);
            }
        ])->latest()->get();

        return response()->json([
            'success' => true,
            'threads' => $threads
        ]);
    }

    public function getMessages($threadId)
    {
        $user = Auth::user();
        $thread = MessageThread::with(['company.user', 'candidate.user', 'job'])->findOrFail($threadId);

        // Periksa apakah pengguna memiliki akses ke thread ini
        if ($user->role == 'company' && $thread->company_id != currentCompany()->id) {
            return response()->json(['success' => false, 'message' => 'Anda tidak memiliki akses ke percakapan ini'], 403);
        } elseif ($user->role == 'candidate' && $thread->candidate_id != currentCandidate()->id) {
            return response()->json(['success' => false, 'message' => 'Anda tidak memiliki akses ke percakapan ini'], 403);
        }

        $messages = $thread->messages()->with(['sender', 'receiver'])->orderBy('created_at')->get();

        // Tandai pesan sebagai telah dibaca
        $thread->messages()
            ->where('receiver_id', $user->id)
            ->where('read', false)
            ->update(['read' => true]);

        return response()->json([
            'success' => true,
            'thread' => $thread,
            'messages' => $messages
        ]);
    }

    public function sendMessage(Request $request)
    {
        $request->validate([
            'thread_id' => 'required|exists:message_threads,id',
            'message' => 'required_without:attachment',
            'attachment' => 'nullable|file|max:5120', // Maksimal 5MB
        ]);

        $user = Auth::user();
        $thread = MessageThread::findOrFail($request->thread_id);

        // Periksa apakah pengguna memiliki akses ke thread ini
        if ($user->role == 'company' && $thread->company_id != currentCompany()->id) {
            return response()->json(['success' => false, 'message' => 'Anda tidak memiliki akses ke percakapan ini'], 403);
        } elseif ($user->role == 'candidate' && $thread->candidate_id != currentCandidate()->id) {
            return response()->json(['success' => false, 'message' => 'Anda tidak memiliki akses ke percakapan ini'], 403);
        }

        // Periksa apakah pesan terakhir dapat dibalas
        $lastMessage = $thread->messages()->latest()->first();
        if ($lastMessage && !$lastMessage->can_reply && $user->role != 'admin') {
            return response()->json(['success' => false, 'message' => 'Pesan ini tidak dapat dibalas'], 403);
        }

        // Tentukan penerima pesan
        $receiverId = null;
        if ($user->role == 'company') {
            $receiverId = $thread->candidate->user_id;

            // Perusahaan tidak bisa mengirim pesan ke pencaker kecuali pencaker melamar pekerjaannya
            if (!AppliedJob::where('candidate_id', $thread->candidate_id)
                ->where('company_id', currentCompany()->id)
                ->exists()) {
                return response()->json(['success' => false, 'message' => 'Anda hanya dapat mengirim pesan ke pencaker yang telah melamar pekerjaan Anda'], 403);
            }
        } elseif ($user->role == 'candidate') {
            $receiverId = $thread->company->user_id;

            // Pencaker tidak bisa kirim pesan ke perusahaan, kecuali perusahaan mengirim pesan duluan
            if (!$thread->messages()->where('sender_id', $thread->company->user_id)->exists()) {
                return response()->json(['success' => false, 'message' => 'Anda hanya dapat membalas pesan dari perusahaan'], 403);
            }
        } elseif ($user->role == 'admin') {
            // Admin dapat mengirim pesan ke siapa saja
            if ($thread->company_id) {
                $receiverId = $thread->company->user_id;
            } elseif ($thread->candidate_id) {
                $receiverId = $thread->candidate->user_id;
            }
        }

        $messageData = [
            'message_thread_id' => $thread->id,
            'sender_id' => $user->id,
            'receiver_id' => $receiverId,
            'body' => $request->message ?? '',
            'type' => $request->type ?? 'umum',
            'can_reply' => $request->has('can_reply') ? $request->can_reply : true,
            'read' => false,
        ];

        // Proses lampiran jika ada
        if ($request->hasFile('attachment')) {
            $file = $request->file('attachment');
            $fileName = time() . '_' . $file->getClientOriginalName();
            $path = $file->storeAs('message_attachments', $fileName, 'public');
            $fileUrl = asset('public/storage/' . $path);

            // Tentukan jenis file
            $fileType = $this->getFileType($file->getClientOriginalExtension());

            $messageData['attachment'] = [
                'url' => $fileUrl,
                'type' => $fileType,
                'name' => $file->getClientOriginalName(),
                'size' => $file->getSize(),
            ];

            if (empty($messageData['body'])) {
                $messageData['body'] = "[{$fileType}] " . $file->getClientOriginalName();
            }
        }

        $message = Message::create($messageData);
        $message->load('sender', 'receiver');

        return response()->json([
            'success' => true,
            'message' => $message
        ]);
    }

    public function createThread(Request $request)
    {
        $request->validate([
            'subject' => 'required|string|max:255',
            'message' => 'required|string',
            'type' => 'required|in:umum,informasi',
            'can_reply' => 'required|boolean',
            'priority' => 'nullable|in:low,medium,high,urgent',
            'category' => 'nullable|in:general,technical,billing,other',
        ]);

        // Periksa apakah pengguna adalah admin
        if (!auth('admin')->check()) {
            if (request()->wantsJson()) {
                return response()->json(['success' => false, 'message' => 'Hanya admin yang dapat membuat percakapan baru'], 403);
            } else {
                return redirect()->back()->with('error', 'Hanya admin yang dapat membuat percakapan baru');
            }
        }

        // Log untuk debugging
        \Log::info('Admin mengirim pesan', [
            'admin_id' => auth('admin')->id(),
            'recipient_type' => $request->recipient_type,
            'subject' => $request->subject
        ]);

        $user = auth('admin')->user();

        // Buat thread untuk semua perusahaan atau pencaker
        if ($request->recipient_type == 'all_companies') {
            $companies = \App\Models\Company::all();
            $threads = [];

            foreach ($companies as $company) {
                $thread = MessageThread::create([
                    'company_id' => $company->id,
                    'initiator_id' => $user->id,
                    'subject' => $request->subject,
                    'is_admin_thread' => true,
                    'priority' => $request->priority ?? 'medium',
                    'category' => $request->category ?? 'general',
                    'ticket_number' => MessageThread::generateTicketNumber(),
                ]);

                Message::create([
                    'message_thread_id' => $thread->id,
                    'sender_id' => $user->id,
                    'receiver_id' => $company->user_id,
                    'body' => $request->message,
                    'type' => $request->type,
                    'can_reply' => $request->can_reply,
                    'read' => false,
                ]);

                $threads[] = $thread->id;
            }

            if (request()->wantsJson()) {
                return response()->json([
                    'success' => true,
                    'message' => 'Pesan berhasil dikirim ke semua perusahaan',
                    'thread_ids' => $threads
                ]);
            } else {
                return redirect()->route('admin.inbox.index')->with('success', 'Pesan berhasil dikirim ke semua perusahaan');
            }
        } elseif ($request->recipient_type == 'all_candidates') {
            $candidates = \App\Models\Candidate::all();
            $threads = [];

            foreach ($candidates as $candidate) {
                $thread = MessageThread::create([
                    'candidate_id' => $candidate->id,
                    'initiator_id' => $user->id,
                    'subject' => $request->subject,
                    'is_admin_thread' => true,
                    'priority' => $request->priority ?? 'medium',
                    'category' => $request->category ?? 'general',
                    'ticket_number' => MessageThread::generateTicketNumber(),
                ]);

                Message::create([
                    'message_thread_id' => $thread->id,
                    'sender_id' => $user->id,
                    'receiver_id' => $candidate->user_id,
                    'body' => $request->message,
                    'type' => $request->type,
                    'can_reply' => $request->can_reply,
                    'read' => false,
                ]);

                $threads[] = $thread->id;
            }

            if (request()->wantsJson()) {
                return response()->json([
                    'success' => true,
                    'message' => 'Pesan berhasil dikirim ke semua pencaker',
                    'thread_ids' => $threads
                ]);
            } else {
                return redirect()->route('admin.inbox.index')->with('success', 'Pesan berhasil dikirim ke semua pencaker');
            }
        } elseif ($request->recipient_type == 'specific_company' && $request->company_id) {
            $company = \App\Models\Company::findOrFail($request->company_id);

            $thread = MessageThread::create([
                'company_id' => $company->id,
                'initiator_id' => $user->id,
                'subject' => $request->subject,
                'is_admin_thread' => true,
                'priority' => $request->priority ?? 'medium',
                'category' => $request->category ?? 'general',
                'ticket_number' => MessageThread::generateTicketNumber(),
            ]);

            Message::create([
                'message_thread_id' => $thread->id,
                'sender_id' => $user->id,
                'receiver_id' => $company->user_id,
                'body' => $request->message,
                'type' => $request->type,
                'can_reply' => $request->can_reply,
                'read' => false,
            ]);

            if (request()->wantsJson()) {
                return response()->json([
                    'success' => true,
                    'message' => 'Pesan berhasil dikirim ke perusahaan',
                    'thread_id' => $thread->id
                ]);
            } else {
                return redirect()->route('admin.inbox.index')->with('success', 'Pesan berhasil dikirim ke perusahaan');
            }
        } elseif ($request->recipient_type == 'specific_candidate' && $request->candidate_id) {
            $candidate = \App\Models\Candidate::findOrFail($request->candidate_id);

            $thread = MessageThread::create([
                'candidate_id' => $candidate->id,
                'initiator_id' => $user->id,
                'subject' => $request->subject,
                'is_admin_thread' => true,
                'priority' => $request->priority ?? 'medium',
                'category' => $request->category ?? 'general',
                'ticket_number' => MessageThread::generateTicketNumber(),
            ]);

            Message::create([
                'message_thread_id' => $thread->id,
                'sender_id' => $user->id,
                'receiver_id' => $candidate->user_id,
                'body' => $request->message,
                'type' => $request->type,
                'can_reply' => $request->can_reply,
                'read' => false,
            ]);

            if (request()->wantsJson()) {
                return response()->json([
                    'success' => true,
                    'message' => 'Pesan berhasil dikirim ke pencaker',
                    'thread_id' => $thread->id
                ]);
            } else {
                return redirect()->route('admin.inbox.index')->with('success', 'Pesan berhasil dikirim ke pencaker');
            }
        }

        if (request()->wantsJson()) {
            return response()->json(['success' => false, 'message' => 'Parameter tidak valid'], 400);
        } else {
            return redirect()->back()->with('error', 'Parameter tidak valid');
        }
    }

    public function markAsRead(Request $request)
    {
        $request->validate([
            'message_id' => 'required|exists:messages,id',
        ]);

        $user = Auth::user();
        $message = Message::findOrFail($request->message_id);

        // Periksa apakah pengguna adalah penerima pesan
        if ($message->receiver_id != $user->id) {
            return response()->json(['success' => false, 'message' => 'Anda tidak memiliki akses untuk menandai pesan ini'], 403);
        }

        $message->update(['read' => true]);

        return response()->json([
            'success' => true,
            'message' => 'Pesan ditandai sebagai telah dibaca'
        ]);
    }

    public function getUnreadCount()
    {
        $user = Auth::user();
        $count = 0;

        if ($user->role == 'company') {
            $count = Message::whereHas('thread', function ($q) {
                $q->where('company_id', currentCompany()->id);
            })->where('receiver_id', $user->id)
              ->where('read', false)
              ->count();
        } elseif ($user->role == 'candidate') {
            $count = Message::whereHas('thread', function ($q) {
                $q->where('candidate_id', currentCandidate()->id);
            })->where('receiver_id', $user->id)
              ->where('read', false)
              ->count();
        } elseif ($user->role == 'admin') {
            $count = Message::where('receiver_id', $user->id)
                          ->where('read', false)
                          ->count();
        }

        // Untuk kompatibilitas dengan kode lama, kita perlu mengembalikan count langsung
        // bukan dalam format JSON
        if (request()->wantsJson()) {
            return response()->json([
                'success' => true,
                'count' => $count
            ]);
        }

        return $count;
    }

    /**
     * Update status tiket
     */
    public function updateTicketStatus(Request $request)
    {
        $request->validate([
            'thread_id' => 'required|exists:message_threads,id',
            'status' => 'required|in:open,pending,closed',
        ]);

        $user = Auth::user();
        $thread = MessageThread::findOrFail($request->thread_id);

        // Periksa apakah pengguna memiliki akses ke thread ini
        if ($user->role == 'company' && $thread->company_id != currentCompany()->id) {
            return response()->json(['success' => false, 'message' => 'Anda tidak memiliki akses ke tiket ini'], 403);
        } elseif ($user->role == 'candidate' && $thread->candidate_id != currentCandidate()->id) {
            return response()->json(['success' => false, 'message' => 'Anda tidak memiliki akses ke tiket ini'], 403);
        }

        // Update status tiket
        $thread->status = $request->status;

        // Jika status closed, set closed_at dan closed_by
        if ($request->status == 'closed') {
            $thread->closed_at = now();
            $thread->closed_by = $user->id;

            // Tambahkan pesan sistem bahwa tiket telah ditutup
            Message::create([
                'message_thread_id' => $thread->id,
                'sender_id' => $user->id,
                'receiver_id' => null,
                'body' => 'Tiket ditutup oleh ' . $user->name,
                'type' => 'informasi',
                'can_reply' => false,
                'read' => true,
            ]);
        } elseif ($thread->status == 'closed' && in_array($request->status, ['open', 'pending'])) {
            // Jika tiket dibuka kembali, reset closed_at dan closed_by
            $thread->closed_at = null;
            $thread->closed_by = null;

            // Tambahkan pesan sistem bahwa tiket telah dibuka kembali
            Message::create([
                'message_thread_id' => $thread->id,
                'sender_id' => $user->id,
                'receiver_id' => null,
                'body' => 'Tiket dibuka kembali oleh ' . $user->name,
                'type' => 'informasi',
                'can_reply' => true,
                'read' => true,
            ]);
        }

        $thread->save();

        return response()->json([
            'success' => true,
            'message' => 'Status tiket berhasil diperbarui',
            'thread' => $thread
        ]);
    }

    /**
     * Update prioritas tiket
     */
    public function updateTicketPriority(Request $request)
    {
        $request->validate([
            'thread_id' => 'required|exists:message_threads,id',
            'priority' => 'required|in:low,medium,high,urgent',
        ]);

        $user = Auth::user();
        $thread = MessageThread::findOrFail($request->thread_id);

        // Periksa apakah pengguna memiliki akses ke thread ini
        if ($user->role == 'company' && $thread->company_id != currentCompany()->id) {
            return response()->json(['success' => false, 'message' => 'Anda tidak memiliki akses ke tiket ini'], 403);
        } elseif ($user->role == 'candidate' && $thread->candidate_id != currentCandidate()->id) {
            return response()->json(['success' => false, 'message' => 'Anda tidak memiliki akses ke tiket ini'], 403);
        }

        // Hanya admin yang dapat mengubah prioritas
        if ($user->role != 'admin') {
            return response()->json(['success' => false, 'message' => 'Hanya admin yang dapat mengubah prioritas tiket'], 403);
        }

        // Update prioritas tiket
        $oldPriority = $thread->priority;
        $thread->priority = $request->priority;
        $thread->save();

        // Tambahkan pesan sistem bahwa prioritas tiket telah diubah
        Message::create([
            'message_thread_id' => $thread->id,
            'sender_id' => $user->id,
            'receiver_id' => null,
            'body' => 'Prioritas tiket diubah dari ' . ucfirst($oldPriority) . ' menjadi ' . ucfirst($request->priority) . ' oleh ' . $user->name,
            'type' => 'informasi',
            'can_reply' => true,
            'read' => true,
        ]);

        return response()->json([
            'success' => true,
            'message' => 'Prioritas tiket berhasil diperbarui',
            'thread' => $thread
        ]);
    }

    /**
     * Update kategori tiket
     */
    public function updateTicketCategory(Request $request)
    {
        $request->validate([
            'thread_id' => 'required|exists:message_threads,id',
            'category' => 'required|in:general,technical,billing,other',
        ]);

        $user = Auth::user();
        $thread = MessageThread::findOrFail($request->thread_id);

        // Periksa apakah pengguna memiliki akses ke thread ini
        if ($user->role == 'company' && $thread->company_id != currentCompany()->id) {
            return response()->json(['success' => false, 'message' => 'Anda tidak memiliki akses ke tiket ini'], 403);
        } elseif ($user->role == 'candidate' && $thread->candidate_id != currentCandidate()->id) {
            return response()->json(['success' => false, 'message' => 'Anda tidak memiliki akses ke tiket ini'], 403);
        }

        // Hanya admin yang dapat mengubah kategori
        if ($user->role != 'admin') {
            return response()->json(['success' => false, 'message' => 'Hanya admin yang dapat mengubah kategori tiket'], 403);
        }

        // Update kategori tiket
        $oldCategory = $thread->category;
        $thread->category = $request->category;
        $thread->save();

        // Tambahkan pesan sistem bahwa kategori tiket telah diubah
        Message::create([
            'message_thread_id' => $thread->id,
            'sender_id' => $user->id,
            'receiver_id' => null,
            'body' => 'Kategori tiket diubah dari ' . ucfirst($oldCategory) . ' menjadi ' . ucfirst($request->category) . ' oleh ' . $user->name,
            'type' => 'informasi',
            'can_reply' => true,
            'read' => true,
        ]);

        return response()->json([
            'success' => true,
            'message' => 'Kategori tiket berhasil diperbarui',
            'thread' => $thread
        ]);
    }

    public function sendBroadcast(Request $request)
    {
        $request->validate([
            'recipient_type' => 'required|string',
            'subject' => 'required|string|max:255',
            'message' => 'required|string',
            'type' => 'required|in:umum,informasi',
            'can_reply' => 'required|boolean',
            'send_method' => 'required|in:inbox,email,both',
            'send_notification' => 'nullable|boolean',
            'attachment' => 'nullable|file|max:5120', // Maksimal 5MB
        ]);

        // Periksa apakah pengguna adalah admin
        if (!auth('admin')->check()) {
            \Log::error('Bukan admin yang mencoba mengirim broadcast', [
                'user' => auth()->user() ? auth()->user()->id : 'tidak login',
                'admin' => auth('admin')->user() ? auth('admin')->user()->id : 'tidak login sebagai admin'
            ]);
            flashError('Hanya admin yang dapat mengirim pesan broadcast');
            return back();
        }

        // Log untuk debugging
        \Log::info('Admin mengirim broadcast', [
            'admin_id' => auth('admin')->id(),
            'recipient_type' => $request->recipient_type,
            'subject' => $request->subject
        ]);

        $user = auth('admin')->user();

        // Proses lampiran jika ada
        $attachmentData = null;
        if ($request->hasFile('attachment')) {
            $file = $request->file('attachment');
            $fileName = time() . '_' . $file->getClientOriginalName();
            $path = $file->storeAs('message_attachments', $fileName, 'public');
            $fileUrl = asset('public/storage/' . $path);

            // Tentukan jenis file
            $fileType = $this->getFileType($file->getClientOriginalExtension());

            $attachmentData = [
                'url' => $fileUrl,
                'type' => $fileType,
                'name' => $file->getClientOriginalName(),
                'size' => $file->getSize(),
            ];
        }

        // Ambil pengguna berdasarkan jenis penerima
        $users = [];
        switch ($request->recipient_type) {
            case 'all_companies':
                $companies = \App\Models\Company::with('user')->get();
                foreach ($companies as $company) {
                    $users[] = $company->user;
                }
                break;
            case 'all_candidates':
                $candidates = \App\Models\Candidate::with('user')->get();
                foreach ($candidates as $candidate) {
                    $users[] = $candidate->user;
                }
                break;
            case 'all_users':
                $users = User::where('role', '!=', 'admin')->get();
                break;
            case 'verified_companies':
                $companies = \App\Models\Company::with('user')->where('verified', true)->get();
                foreach ($companies as $company) {
                    $users[] = $company->user;
                }
                break;
            case 'unverified_companies':
                $companies = \App\Models\Company::with('user')->where('verified', false)->get();
                foreach ($companies as $company) {
                    $users[] = $company->user;
                }
                break;
            case 'verified_candidates':
                $candidates = \App\Models\Candidate::with('user')->where('verified', true)->get();
                foreach ($candidates as $candidate) {
                    $users[] = $candidate->user;
                }
                break;
            case 'unverified_candidates':
                $candidates = \App\Models\Candidate::with('user')->where('verified', false)->get();
                foreach ($candidates as $candidate) {
                    $users[] = $candidate->user;
                }
                break;
            default:
                flashError('Jenis penerima tidak valid');
                return back();
        }

        // Kirim pesan ke semua pengguna
        $threadCount = 0;
        $emailCount = 0;
        $notificationCount = 0;

        foreach ($users as $recipient) {
            // Kirim pesan ke inbox
            if ($request->send_method == 'inbox' || $request->send_method == 'both') {
                // Buat thread baru
                $thread = null;
                if ($recipient->role == 'company') {
                    $company = \App\Models\Company::where('user_id', $recipient->id)->first();
                    if ($company) {
                        $thread = MessageThread::create([
                            'company_id' => $company->id,
                            'initiator_id' => $user->id,
                            'subject' => $request->subject,
                            'is_admin_thread' => true,
                        ]);
                    }
                } elseif ($recipient->role == 'candidate') {
                    $candidate = \App\Models\Candidate::where('user_id', $recipient->id)->first();
                    if ($candidate) {
                        $thread = MessageThread::create([
                            'candidate_id' => $candidate->id,
                            'initiator_id' => $user->id,
                            'subject' => $request->subject,
                            'is_admin_thread' => true,
                        ]);
                    }
                }

                if ($thread) {
                    // Buat pesan
                    $messageData = [
                        'message_thread_id' => $thread->id,
                        'sender_id' => $user->id,
                        'receiver_id' => $recipient->id,
                        'body' => $request->message,
                        'type' => $request->type,
                        'can_reply' => $request->can_reply,
                        'read' => false,
                    ];

                    if ($attachmentData) {
                        $messageData['attachment'] = $attachmentData;
                    }

                    Message::create($messageData);
                    $threadCount++;
                }
            }

            // Kirim email
            if ($request->send_method == 'email' || $request->send_method == 'both') {
                try {
                    Mail::send('emails.broadcast', [
                        'subject' => $request->subject,
                        'content' => $request->message,
                        'user' => $recipient,
                    ], function ($message) use ($recipient, $request, $attachmentData) {
                        $message->to($recipient->email)
                            ->subject($request->subject);

                        if ($attachmentData) {
                            $message->attach(public_path('public/storage/' . str_replace(asset('public/storage/'), '', $attachmentData['url'])));
                        }
                    });
                    $emailCount++;
                } catch (\Exception $e) {
                    // Log error
                    \Log::error('Failed to send email to ' . $recipient->email . ': ' . $e->getMessage());
                }
            }

            // Kirim notifikasi
            if ($request->send_notification) {
                try {
                    $recipient->notify(new BroadcastMessageNotification($request->subject, $request->message));
                    $notificationCount++;
                } catch (\Exception $e) {
                    // Log error
                    \Log::error('Failed to send notification to ' . $recipient->id . ': ' . $e->getMessage());
                }
            }
        }

        // Tampilkan pesan sukses
        $successMessage = 'Pesan broadcast berhasil dikirim: ';
        if ($request->send_method == 'inbox' || $request->send_method == 'both') {
            $successMessage .= $threadCount . ' pesan inbox, ';
        }
        if ($request->send_method == 'email' || $request->send_method == 'both') {
            $successMessage .= $emailCount . ' email, ';
        }
        if ($request->send_notification) {
            $successMessage .= $notificationCount . ' notifikasi';
        }

        flashSuccess($successMessage);
        return redirect()->route('admin.inbox.index');
    }

    /**
     * Mengirim pesan dari perusahaan ke kandidat
     */
    public function companyMessageCandidate(Request $request)
    {
        try {
            $request->validate([
                'candidate_id' => 'required|exists:candidates,id',
                'message' => 'required|string',
                'job_id' => 'nullable|exists:jobs,id',
                'subject' => 'nullable|string|max:255',
            ]);

            $user = auth()->user();

            // Pastikan pengguna adalah perusahaan
            if ($user->role != 'company') {
                return response()->json([
                    'success' => false,
                    'message' => 'Hanya perusahaan yang dapat mengirim pesan ke kandidat'
                ], 403);
            }

            $company = currentCompany();
            $candidate = \App\Models\Candidate::with('user')->findOrFail($request->candidate_id);

            // Periksa apakah kandidat telah melamar pekerjaan di perusahaan ini
            $hasApplied = AppliedJob::where('candidate_id', $candidate->id)
                ->whereHas('job', function($query) use ($company) {
                    $query->where('company_id', $company->id);
                })
                ->exists();

            if (!$hasApplied) {
                return response()->json([
                    'success' => false,
                    'message' => 'Anda hanya dapat mengirim pesan ke kandidat yang telah melamar pekerjaan Anda'
                ], 403);
            }

            // Cari thread yang sudah ada atau buat yang baru
            $thread = MessageThread::where('company_id', $company->id)
                ->where('candidate_id', $candidate->id)
                ->first();

            if (!$thread) {
                $subject = $request->subject ?? 'Pesan dari ' . $company->user->name;

                $thread = MessageThread::create([
                    'company_id' => $company->id,
                    'candidate_id' => $candidate->id,
                    'job_id' => $request->job_id,
                    'initiator_id' => $user->id,
                    'subject' => $subject,
                    'is_admin_thread' => false,
                ]);
            }

            // Buat pesan
            Message::create([
                'message_thread_id' => $thread->id,
                'sender_id' => $user->id,
                'receiver_id' => $candidate->user_id,
                'body' => $request->message,
                'type' => 'umum',
                'can_reply' => true,
                'read' => false,
            ]);

            // Log untuk debugging
            \Log::info('Message sent successfully', [
                'thread_id' => $thread->id,
                'sender_id' => $user->id,
                'receiver_id' => $candidate->user_id,
                'message' => $request->message
            ]);

            return response()->json([
                'success' => true,
                'message' => 'Pesan berhasil dikirim',
                'thread_id' => $thread->id
            ]);
        } catch (\Exception $e) {
            // Log error untuk debugging
            \Log::error('Error sending message: ' . $e->getMessage(), [
                'exception' => $e,
                'request' => $request->all()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Terjadi kesalahan: ' . $e->getMessage()
            ], 500);
        }
    }

    private function getFileType($extension)
    {
        $imageExtensions = ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'svg', 'webp'];
        $documentExtensions = ['pdf', 'doc', 'docx', 'xls', 'xlsx', 'ppt', 'pptx', 'txt', 'csv'];

        if (in_array(strtolower($extension), $imageExtensions)) {
            return 'image';
        } elseif (in_array(strtolower($extension), $documentExtensions)) {
            return 'document';
        } else {
            return 'file';
        }
    }
}
