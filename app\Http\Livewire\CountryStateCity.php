<?php

namespace App\Http\Livewire;

use App\Models\City;
use App\Models\SearchCountry;
use App\Models\State;
use App\Models\Kecamatan;
use App\Models\Kelurahan;
use Livewire\Component;

class CountryStateCity extends Component
{
    public $countries = [];
    public $states = [];
    public $cities = [];
    public $kecamatans = [];
    public $kelurahans = [];

    public $row = false;
    public $selectedCountryId = 'Indonesia'; // Set default country
    public $selectedStateId = null; // No default province
    public $selectedCityId = null; // No default city
    public $selectedKecamatanId;
    public $selectedKelurahanId;



    protected $listeners = [
        'getStateByCountryId' => 'getStateByCountryId',
        'getCityByStateId' => 'getCityByStateId',
        'getKecamatanByCityId' => 'getKecamatanByCityId',
        'getKelurahanByKecamatanId' => 'getKelurahanByKecamatanId',
    ];

    public function hydrate()
    {
        $this->dispatchBrowserEvent('render-select2');
    }

    public function render()
    {
        // Load states (provinces)
        $this->states = State::select('id', 'name')->get()->toArray();

        // Load cities if state (province) is selected
        if ($this->selectedStateId) {
            $selectedState = State::where('name', $this->selectedStateId)->first();
            if ($selectedState) {
                $stateId = $selectedState->id;
                $this->cities = City::select('id', 'name', 'state_id')
                    ->where('state_id', $stateId)
                    ->get()
                    ->toArray();
            }
        }

        // Load kecamatans (districts) if city is selected
        if ($this->selectedCityId) {
            $selectedCity = City::where('name', $this->selectedCityId)->first();
            if ($selectedCity) {
                $cityId = $selectedCity->id;
                $this->kecamatans = Kecamatan::select('id', 'name', 'city_id')
                    ->where('city_id', $cityId)
                    ->get()
                    ->toArray();

                // Debug: Log jumlah kecamatan yang dimuat
                \Log::info('Loaded Kecamatans: ' . count($this->kecamatans));
            }
        }

        // Jika kecamatans kosong tapi selectedKecamatanId ada, coba muat kecamatan berdasarkan ID kota
        if (empty($this->kecamatans) && $this->selectedKecamatanId) {
            // Cari kecamatan berdasarkan nama
            $kecamatan = Kecamatan::where('name', $this->selectedKecamatanId)->first();
            if ($kecamatan) {
                // Dapatkan city_id dari kecamatan
                $cityId = $kecamatan->city_id;

                // Muat semua kecamatan dengan city_id yang sama
                $this->kecamatans = Kecamatan::select('id', 'name', 'city_id')
                    ->where('city_id', $cityId)
                    ->get()
                    ->toArray();

                // Debug: Log jumlah kecamatan yang dimuat (fallback)
                \Log::info('Fallback Loaded Kecamatans: ' . count($this->kecamatans));
            }
        }

        // Load kelurahans (villages) if kecamatan is selected
        if ($this->selectedKecamatanId) {
            // Cek apakah selectedKecamatanId adalah ID atau nama
            if (is_numeric($this->selectedKecamatanId)) {
                $selectedKecamatan = Kecamatan::find($this->selectedKecamatanId);
            } else {
                $selectedKecamatan = Kecamatan::where('name', $this->selectedKecamatanId)->first();
            }

            if ($selectedKecamatan) {
                $kecamatanId = $selectedKecamatan->id;
                $this->kelurahans = Kelurahan::select('id', 'name', 'kecamatan_id')
                    ->where('kecamatan_id', $kecamatanId)
                    ->get()
                    ->toArray();

                // Debug: Log jumlah kelurahan yang dimuat
                \Log::info('render - Loaded Kelurahans: ' . count($this->kelurahans));

                // Jika ada kelurahan yang tersimpan di database, pilih kelurahan tersebut
                $company = auth()->user()->company ?? null;
                if ($company && $company->neighborhood) {
                    // Cek apakah kelurahan yang tersimpan ada di daftar kelurahan yang dimuat
                    $kelurahanExists = collect($this->kelurahans)->pluck('name')->contains($company->neighborhood);
                    if ($kelurahanExists) {
                        $this->selectedKelurahanId = $company->neighborhood;
                        \Log::info('render - Selected Kelurahan: ' . $this->selectedKelurahanId);
                    }
                }
            }
        }

        // Jika kelurahans kosong tapi selectedKelurahanId ada, coba muat kelurahan berdasarkan ID kecamatan
        if (empty($this->kelurahans) && $this->selectedKelurahanId) {
            // Cari kelurahan berdasarkan nama
            $kelurahan = Kelurahan::where('name', $this->selectedKelurahanId)->first();
            if ($kelurahan) {
                // Dapatkan kecamatan_id dari kelurahan
                $kecamatanId = $kelurahan->kecamatan_id;

                // Muat semua kelurahan dengan kecamatan_id yang sama
                $this->kelurahans = Kelurahan::select('id', 'name', 'kecamatan_id')
                    ->where('kecamatan_id', $kecamatanId)
                    ->get()
                    ->toArray();

                // Debug: Log jumlah kelurahan yang dimuat (fallback)
                \Log::info('render - Fallback Loaded Kelurahans: ' . count($this->kelurahans));
            }
        }

        return view('livewire.country-state-city');
    }

    public function getStateByCountryId()
    {
        $selectedCountry = SearchCountry::where('name', $this->selectedCountryId)->first();
        if ($selectedCountry) {
            $countryId = $selectedCountry->id;
            $this->states = State::select('id', 'name', 'country_id')
                ->where('country_id', $countryId)
                ->get()
                ->toArray();
        } else {
            $this->states = [];
        }
    }

    public function getCityByStateId()
    {
        $selectedState = State::where('name', $this->selectedStateId)->first();
        if ($selectedState) {
            $stateId = $selectedState->id;
            $this->cities = City::select('id', 'name', 'state_id')
                ->where('state_id', $stateId)
                ->get()
                ->toArray();
        } else {
            $this->cities = [];
        }

        // Emit event bahwa data kota telah diperbarui
        $this->emit('citiesUpdated');
    }

    public function getKecamatanByCityId()
    {
        $selectedCity = City::where('name', $this->selectedCityId)->first();
        if ($selectedCity) {
            $cityId = $selectedCity->id;
            $this->kecamatans = Kecamatan::where('city_id', $cityId)->get()->toArray();

            // Debug: Log jumlah kecamatan yang dimuat
            \Log::info('getKecamatanByCityId - Loaded Kecamatans: ' . count($this->kecamatans));

            // Jika ada kecamatan yang tersimpan di database, pilih kecamatan tersebut
            $company = auth()->user()->company ?? null;
            if ($company && $company->locality) {
                // Cek apakah kecamatan yang tersimpan ada di daftar kecamatan yang dimuat
                $kecamatanExists = collect($this->kecamatans)->pluck('name')->contains($company->locality);
                if ($kecamatanExists) {
                    $this->selectedKecamatanId = $company->locality;
                    \Log::info('getKecamatanByCityId - Selected Kecamatan: ' . $this->selectedKecamatanId);
                }
            }
        } else {
            $this->kecamatans = [];
        }

        // Emit event bahwa data kecamatan telah diperbarui
        $this->emit('kecamatansUpdated');
    }

    public function getKelurahanByKecamatanId()
    {
        // Cek apakah selectedKecamatanId adalah ID atau nama
        if (is_numeric($this->selectedKecamatanId)) {
            $selectedKecamatan = Kecamatan::find($this->selectedKecamatanId);
        } else {
            $selectedKecamatan = Kecamatan::where('name', $this->selectedKecamatanId)->first();
        }

        if ($selectedKecamatan) {
            $kecamatanId = $selectedKecamatan->id;
            $this->kelurahans = Kelurahan::where('kecamatan_id', $kecamatanId)->get()->toArray();

            // Debug: Log jumlah kelurahan yang dimuat
            \Log::info('getKelurahanByKecamatanId - Loaded Kelurahans: ' . count($this->kelurahans));

            // Jika ada kelurahan yang tersimpan di database, pilih kelurahan tersebut
            $company = auth()->user()->company ?? null;
            if ($company && $company->neighborhood) {
                // Cek apakah kelurahan yang tersimpan ada di daftar kelurahan yang dimuat
                $kelurahanExists = collect($this->kelurahans)->pluck('name')->contains($company->neighborhood);
                if ($kelurahanExists) {
                    $this->selectedKelurahanId = $company->neighborhood;
                    \Log::info('getKelurahanByKecamatanId - Selected Kelurahan: ' . $this->selectedKelurahanId);
                }
            }
        } else {
            $this->kelurahans = [];
        }

        // Emit event bahwa data kelurahan telah diperbarui
        $this->emit('kelurahansUpdated');
    }

    public function mount($selectedStateId = null, $selectedCityId = null, $selectedKecamatanId = null, $selectedKelurahanId = null)
    {
        // Ambil data user, company, dan candidate
        $user = auth()->user();
        $company = $user && $user->role == 'company' ? $user->company : null;
        $candidate = $user && $user->role == 'candidate' ? $user->candidate : null;

        // Jika ada parameter yang dikirim dari component, gunakan itu sebagai prioritas tertinggi
        if ($selectedStateId) {
            $this->selectedStateId = $selectedStateId;
        }
        if ($selectedCityId) {
            $this->selectedCityId = $selectedCityId;
        }
        if ($selectedKecamatanId) {
            $this->selectedKecamatanId = $selectedKecamatanId;
        }
        if ($selectedKelurahanId) {
            $this->selectedKelurahanId = $selectedKelurahanId;
        }

        // Debug: Log old values
        \Log::info('CountryStateCity mount - Old values', [
            'old_kecamatan' => old('kecamatan'),
            'old_kelurahan' => old('kelurahan'),
            'old_kecamatan_id' => old('kecamatan_id'),
            'old_kelurahan_id' => old('kelurahan_id'),
            'user_role' => $user ? $user->role : 'guest',
            'company_data' => $company ? ['region' => $company->region, 'district' => $company->district] : null,
            'candidate_data' => $candidate ? ['region' => $candidate->region, 'district' => $candidate->district, 'locality' => $candidate->locality, 'neighborhood' => $candidate->neighborhood] : null,
        ]);

        // Set default values dari database atau session
        $this->selectedCountryId = session('selectedCountryId', 'Indonesia');

        // Prioritas untuk State/Province: 1. Parameter, 2. Session, 3. Database, 4. Default
        if (!$this->selectedStateId) {
            if (session('selectedStateId')) {
                $this->selectedStateId = session('selectedStateId');
            } elseif ($company && $company->region) {
                $this->selectedStateId = $company->region;
            } elseif ($candidate && $candidate->region) {
                $this->selectedStateId = $candidate->region;
            } else {
                $this->selectedStateId = 'Banten'; // Default
            }
        }

        // Prioritas untuk City: 1. Parameter, 2. Session, 3. Database, 4. Default
        if (!$this->selectedCityId) {
            if (session('selectedCityId')) {
                $this->selectedCityId = session('selectedCityId');
            } elseif ($company && $company->district) {
                $this->selectedCityId = $company->district;
            } elseif ($candidate && $candidate->district) {
                $this->selectedCityId = $candidate->district;
            } else {
                $this->selectedCityId = 'Tangerang Selatan'; // Default
            }
        }

        // Prioritas untuk Kecamatan: 1. Parameter, 2. Old values, 3. Database, 4. Session data
        if (!$this->selectedKecamatanId) {
            if (old('kecamatan')) {
                // Jika ada old value untuk kecamatan, gunakan itu
                $this->selectedKecamatanId = old('kecamatan');
                \Log::info('Using old kecamatan value: ' . $this->selectedKecamatanId);
            } elseif (old('kecamatan_id')) {
                // Jika ada old value untuk kecamatan_id, cari nama kecamatan
                $kecamatan = Kecamatan::find(old('kecamatan_id'));
                if ($kecamatan) {
                    $this->selectedKecamatanId = $kecamatan->name;
                    \Log::info('Using old kecamatan_id value: ' . old('kecamatan_id') . ' => ' . $this->selectedKecamatanId);
                }
            } elseif ($candidate && $candidate->locality) {
                // Untuk candidate, gunakan data dari candidate table
                $this->selectedKecamatanId = $candidate->locality;
                \Log::info('Using candidate locality value: ' . $this->selectedKecamatanId);
            } elseif ($company && $company->locality) {
                // Untuk company, gunakan data dari company table (jika ada)
                $this->selectedKecamatanId = $company->locality;
                \Log::info('Using company locality value: ' . $this->selectedKecamatanId);
            } else {
                // Jika tidak ada data dari database, gunakan data dari session
                $this->selectedKecamatanId = session('selectedKecamatanId', null);
                \Log::info('Using session kecamatan value: ' . $this->selectedKecamatanId);
            }
        }

        // Simpan ke session untuk digunakan di tempat lain
        session(['selectedKecamatanId' => $this->selectedKecamatanId]);

        // Load kecamatans berdasarkan city jika ada city yang dipilih
        if ($this->selectedCityId) {
            $selectedCity = City::where('name', $this->selectedCityId)->first();
            if ($selectedCity) {
                $this->kecamatans = Kecamatan::where('city_id', $selectedCity->id)->get()->toArray();
                \Log::info('Loaded kecamatans for city: ' . $this->selectedCityId . ', count: ' . count($this->kecamatans));

                // Trigger event untuk memastikan dropdown kecamatan diperbarui
                $this->emit('kecamatansUpdated');
            }
        }

        // Prioritas untuk Kelurahan: 1. Old values, 2. Database, 3. Session data
        if (old('kelurahan')) {
            // Jika ada old value untuk kelurahan, gunakan itu
            $this->selectedKelurahanId = old('kelurahan');
            \Log::info('Using old kelurahan value: ' . $this->selectedKelurahanId);
        } elseif (old('kelurahan_id')) {
            // Jika ada old value untuk kelurahan_id, cari nama kelurahan
            $kelurahan = Kelurahan::find(old('kelurahan_id'));
            if ($kelurahan) {
                $this->selectedKelurahanId = $kelurahan->name;
                \Log::info('Using old kelurahan_id value: ' . old('kelurahan_id') . ' => ' . $this->selectedKelurahanId);
            }
        } elseif ($candidate && $candidate->neighborhood) {
            // Untuk candidate, gunakan data dari candidate table
            $this->selectedKelurahanId = $candidate->neighborhood;
            \Log::info('Using candidate neighborhood value: ' . $this->selectedKelurahanId);
        } elseif ($company && $company->neighborhood) {
            // Untuk company, gunakan data dari company table (jika ada)
            $this->selectedKelurahanId = $company->neighborhood;
            \Log::info('Using company neighborhood value: ' . $this->selectedKelurahanId);
        } else {
            // Jika tidak ada data dari database, gunakan data dari session
            $this->selectedKelurahanId = session('selectedKelurahanId', null);
            \Log::info('Using session kelurahan value: ' . $this->selectedKelurahanId);
        }

        // Simpan ke session untuk digunakan di tempat lain
        session(['selectedKelurahanId' => $this->selectedKelurahanId]);

        // Load kelurahans berdasarkan kecamatan jika ada kecamatan yang dipilih
        if ($this->selectedKecamatanId) {
            $selectedKecamatan = Kecamatan::where('name', $this->selectedKecamatanId)->first();
            if ($selectedKecamatan) {
                $this->kelurahans = Kelurahan::where('kecamatan_id', $selectedKecamatan->id)->get()->toArray();
                \Log::info('Loaded kelurahans for kecamatan: ' . $this->selectedKecamatanId . ', count: ' . count($this->kelurahans));

                // Trigger event untuk memastikan dropdown kelurahan diperbarui
                $this->emit('kelurahansUpdated');
            }
        }
    }

    public function updatedselectedCountryId($value)
    {
        session(['selectedCountryId' => $value]);
        $selectedCountry = SearchCountry::where('name', $value)->first();
        if ($selectedCountry) {
            session(['selectedCountryLong' => $selectedCountry->long]);
            session(['selectedCountryLat' => $selectedCountry->lat]);
        }

        // Reset dependent fields
        $this->selectedStateId = null;
        $this->selectedCityId = null;
        $this->selectedKecamatanId = null;
        $this->selectedKelurahanId = null;

        $this->getStateByCountryId();
    }

    public function updatedselectedStateId($value)
    {
        session(['selectedStateId' => $value]);
        $selectedState = State::where('name', $value)->first();
        if ($selectedState) {
            session(['selectedStateLong' => $selectedState->long]);
            session(['selectedStateLat' => $selectedState->lat]);
        }

        // Reset dependent fields
        $this->selectedCityId = null;
        $this->selectedKecamatanId = null;
        $this->selectedKelurahanId = null;
        $this->cities = [];
        $this->kecamatans = [];
        $this->kelurahans = [];

        $this->getCityByStateId();
    }

    public function updatedselectedCityId($value)
    {
        session(['selectedCityId' => $value]);
        $selectedCity = City::where('name', $value)->first();
        if ($selectedCity) {
            session(['selectedCityLong' => $selectedCity->long]);
            session(['selectedCityLat' => $selectedCity->lat]);
        }

        // Reset dependent fields
        $this->selectedKecamatanId = null;
        $this->selectedKelurahanId = null;
        $this->kecamatans = [];
        $this->kelurahans = [];

        $this->getKecamatanByCityId();
    }

    public function updatedselectedKecamatanId($value)
    {
        session(['selectedKecamatanId' => $value]);
        $selectedKecamatan = Kecamatan::where('name', $value)->first();
        if ($selectedKecamatan) {
            session(['selectedKecamatanLong' => $selectedKecamatan->long]);
            session(['selectedKecamatanLat' => $selectedKecamatan->lat]);
        }

        // Reset dependent fields
        $this->selectedKelurahanId = null;
        $this->kelurahans = [];

        $this->getKelurahanByKecamatanId();
    }

    public function updatedselectedKelurahanId($value)
    {
        session(['selectedKelurahanId' => $value]);
        $selectedKelurahan = Kelurahan::where('name', $value)->first();
        if ($selectedKelurahan) {
            session(['selectedKelurahanLong' => $selectedKelurahan->long]);
            session(['selectedKelurahanLat' => $selectedKelurahan->lat]);
        }
    }

    public function updated($field)
    {
        if (in_array($field, ['selectedCountryId', 'selectedStateId', 'selectedCityId', 'selectedKecamatanId', 'selectedKelurahanId'])) {
            $this->updateLocationSession();
        }
    }

    private function updateLocationSession()
    {
        if (empty(config('templatecookie.map_show'))) {
            session()->put('location', [
                'country' => session('selectedCountryId'),
                'region' => session('selectedStateId'),
                'district' => session('selectedCityId'), // Gunakan district untuk konsistensi dengan updateMap
                'locality' => session('selectedKecamatanId'), // Gunakan locality untuk konsistensi dengan updateMap
                'neighborhood' => session('selectedKelurahanId') // Gunakan neighborhood untuk konsistensi dengan updateMap
            ]);

            // Simpan ID kecamatan dan kelurahan ke session
            if ($this->selectedKecamatanId) {
                // Jika ID numerik, simpan langsung
                if (is_numeric($this->selectedKecamatanId)) {
                    session(['selectedKecamatanId' => $this->selectedKecamatanId]);
                }
                // Jika nama, cari ID-nya
                else {
                    $kecamatan = \App\Models\Kecamatan::where('name', $this->selectedKecamatanId)->first();
                    if ($kecamatan) {
                        session(['selectedKecamatanId' => $kecamatan->id]);
                    }
                }
            }

            if ($this->selectedKelurahanId) {
                // Jika ID numerik, simpan langsung
                if (is_numeric($this->selectedKelurahanId)) {
                    session(['selectedKelurahanId' => $this->selectedKelurahanId]);
                }
                // Jika nama, cari ID-nya
                else {
                    $kelurahan = \App\Models\Kelurahan::where('name', $this->selectedKelurahanId)->first();
                    if ($kelurahan) {
                        session(['selectedKelurahanId' => $kelurahan->id]);
                    }
                }
            }
        }
    }
}
