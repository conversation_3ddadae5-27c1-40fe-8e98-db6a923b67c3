<?php

namespace App\Http\Livewire\Message;

use App\Models\MessageThread;
use Livewire\Component;

class MessageList extends Component
{
    public $threads = [];
    public $selectedThread = null;
    public $unreadFilter = false;
    public $jobFilter = null;
    public $jobs = [];
    public $initialThreadId = null;
    public $selectedThreads = [];
    public $selectAll = false;
    public $bulkAction = '';
    public $isCheckboxClick = false;
    public $searchTerm = '';
    public $filterType = 'all';
    public $perPage = 10;
    public $currentPage = 1;
    public $totalPages = 1;

    protected $listeners = [
        'threadSelected' => 'selectThread',
        'messageSent' => 'refreshList',
        'refreshMessageList' => 'refreshList',
        'filterByType' => 'filterByType',
        'searchMessages' => 'searchMessages',
        'gotoPage' => 'gotoPage',
        'toggleUnreadFilter' => 'toggleUnreadFilter'
    ];

    public $totalThreads = 0;

    public function mount($initialThreadId = null)
    {
        $this->initialThreadId = $initialThreadId;

        // Get filter parameters from URL
        $queryParams = request()->query();
        if (isset($queryParams['filter'])) {
            $this->filterType = $queryParams['filter'];
        }

        if (isset($queryParams['search'])) {
            $this->searchTerm = $queryParams['search'];
        }

        if (isset($queryParams['page'])) {
            $this->currentPage = (int)$queryParams['page'];
        }

        if (isset($queryParams['per_page'])) {
            $this->perPage = (int)$queryParams['per_page'];
        }

        if (isset($queryParams['unread']) && $queryParams['unread'] === '1') {
            $this->unreadFilter = true;
        }

        $this->loadThreads();
        $this->loadJobs();

        if ($this->initialThreadId) {
            $this->selectedThread = $this->initialThreadId;
            $this->emit('threadSelected', $this->initialThreadId);
        }
    }

    public function loadThreads()
    {
        $query = MessageThread::query();

        // Apply role-based filters
        if (auth()->user()->role == 'company') {
            $query->where('company_id', currentCompany()->id);

            if ($this->jobFilter) {
                $query->where('job_id', $this->jobFilter);
            }
        } elseif (auth()->user()->role == 'candidate') {
            $query->where('candidate_id', currentCandidate()->id);
        } elseif (auth()->user()->role == 'admin') {
            // Apply admin-specific filters
            if ($this->filterType == 'company') {
                $query->whereNotNull('company_id');
            } elseif ($this->filterType == 'candidate') {
                $query->whereNotNull('candidate_id');
            } elseif ($this->filterType == 'admin') {
                $query->where('is_admin_thread', true);
            }
        }

        // Apply unread filter
        if ($this->unreadFilter) {
            $query->whereHas('messages', function ($q) {
                $q->where('read', false)
                  ->where('receiver_id', auth()->id());
            });
        }

        // Apply search term
        if (!empty($this->searchTerm)) {
            $query->where(function ($q) {
                // Search in subject
                $q->where('subject', 'like', '%' . $this->searchTerm . '%');

                // Search in company name
                $q->orWhereHas('company.user', function ($sq) {
                    $sq->where('name', 'like', '%' . $this->searchTerm . '%');
                });

                // Search in candidate name
                $q->orWhereHas('candidate.user', function ($sq) {
                    $sq->where('name', 'like', '%' . $this->searchTerm . '%');
                });

                // Search in job title
                $q->orWhereHas('job', function ($sq) {
                    $sq->where('title', 'like', '%' . $this->searchTerm . '%');
                });
            });
        }

        // Get total count for pagination
        $this->totalThreads = $query->count();
        $this->totalPages = ceil($this->totalThreads / $this->perPage);

        // Apply pagination
        $offset = ($this->currentPage - 1) * $this->perPage;

        // Get threads with relationships
        $this->threads = $query->with([
            'company.user',
            'candidate.user',
            'job',
            'latestMessage',
            'unreadMessages' => function ($q) {
                $q->where('receiver_id', auth()->id());
            }
        ])->latest()->skip($offset)->take($this->perPage)->get();

        // Dispatch event to notify JavaScript that pagination has been updated
        $this->dispatchBrowserEvent('paginationUpdated');
    }

    public function loadJobs()
    {
        if (auth()->user()->role == 'company') {
            $this->jobs = currentCompany()->jobs()->active()->get();
        }
    }

    public function selectThread($threadId)
    {
        // If we're clicking on a checkbox, don't select the thread
        if ($this->isCheckboxClick) {
            $this->isCheckboxClick = false;
            return;
        }

        $this->selectedThread = $threadId;
        $this->emit('threadSelected', $threadId);

        // Show message details section
        $this->dispatchBrowserEvent('openMessageModal');
    }

    public function toggleThreadSelection($threadId)
    {
        $this->isCheckboxClick = true;

        if (in_array($threadId, $this->selectedThreads)) {
            $this->selectedThreads = array_diff($this->selectedThreads, [$threadId]);
        } else {
            $this->selectedThreads[] = $threadId;
        }

        $this->updatedSelectedThreads();
    }

    public function toggleUnreadFilter()
    {
        $this->unreadFilter = !$this->unreadFilter;
        $this->currentPage = 1;
        $this->loadThreads();
        $this->updateUrlParameters();
    }

    public function filterByJob($jobId)
    {
        $this->jobFilter = $jobId;
        $this->currentPage = 1;
        $this->loadThreads();
        $this->updateUrlParameters();

        // Dispatch event to notify JavaScript about job filter change
        $this->dispatchBrowserEvent('jobFilterUpdated', ['jobId' => $jobId]);
    }

    public function refreshList()
    {
        $this->loadThreads();
    }

    public function updatedSelectAll($value)
    {
        if ($value) {
            $threadIds = [];
            foreach ($this->threads as $thread) {
                $threadIds[] = (string) $thread->id;
            }
            $this->selectedThreads = $threadIds;
        } else {
            $this->selectedThreads = [];
        }
    }

    public function updatedSelectedThreads()
    {
        $this->selectAll = count($this->selectedThreads) === count($this->threads);
    }

    public function executeBulkAction()
    {
        if (empty($this->selectedThreads)) {
            $this->dispatchBrowserEvent('show-toast', [
                'type' => 'error',
                'message' => __('Pilih setidaknya satu pesan')
            ]);
            return;
        }

        switch ($this->bulkAction) {
            case 'mark_read':
                $this->markAsRead();
                break;
            case 'mark_unread':
                $this->markAsUnread();
                break;
            case 'delete':
                $this->deleteThreads();
                break;
            default:
                $this->dispatchBrowserEvent('show-toast', [
                    'type' => 'error',
                    'message' => __('Pilih tindakan yang valid')
                ]);
                break;
        }

        $this->bulkAction = '';
    }

    public function markAsRead()
    {
        foreach ($this->selectedThreads as $threadId) {
            $thread = MessageThread::find($threadId);
            if ($thread) {
                $thread->messages()
                    ->where('receiver_id', auth()->id())
                    ->where('read', false)
                    ->update(['read' => true]);
            }
        }

        $this->dispatchBrowserEvent('show-toast', [
            'type' => 'success',
            'message' => __('Pesan ditandai sebagai telah dibaca')
        ]);

        $this->selectedThreads = [];
        $this->selectAll = false;
        $this->loadThreads();
    }

    public function markAsUnread()
    {
        foreach ($this->selectedThreads as $threadId) {
            $thread = MessageThread::find($threadId);
            if ($thread) {
                $thread->messages()
                    ->where('receiver_id', auth()->id())
                    ->where('read', true)
                    ->update(['read' => false]);
            }
        }

        $this->dispatchBrowserEvent('show-toast', [
            'type' => 'success',
            'message' => __('Pesan ditandai sebagai belum dibaca')
        ]);

        $this->selectedThreads = [];
        $this->selectAll = false;
        $this->loadThreads();
    }

    public function deleteThreads()
    {
        foreach ($this->selectedThreads as $threadId) {
            $thread = MessageThread::find($threadId);
            if ($thread) {
                // Soft delete thread and messages
                $thread->messages()->delete();
                $thread->delete();
            }
        }

        $this->dispatchBrowserEvent('show-toast', [
            'type' => 'success',
            'message' => __('Pesan berhasil dihapus')
        ]);

        $this->selectedThreads = [];
        $this->selectAll = false;
        $this->loadThreads();

        if ($this->selectedThread && in_array($this->selectedThread, $this->selectedThreads)) {
            $this->selectedThread = null;
            $this->emit('threadSelected', null);
        }
    }

    /**
     * Filter messages by type (all, company, candidate, admin)
     */
    public function filterByType($type)
    {
        $this->filterType = $type;
        $this->currentPage = 1;
        $this->loadThreads();
        $this->updateUrlParameters();
    }

    /**
     * Search messages by term
     */
    public function searchMessages($term = null)
    {
        if ($term !== null) {
            $this->searchTerm = $term;
        }

        $this->currentPage = 1;
        $this->loadThreads();
        $this->updateUrlParameters();

        // Dispatch event to notify JavaScript that search has been performed
        $this->dispatchBrowserEvent('searchPerformed', ['term' => $this->searchTerm]);
    }

    /**
     * Go to specific page
     */
    public function gotoPage($page)
    {
        $this->currentPage = $page;
        $this->loadThreads();
        $this->updateUrlParameters();
    }

    /**
     * Go to next page
     */
    public function nextPage()
    {
        if ($this->currentPage < $this->totalPages) {
            $this->currentPage++;
            $this->loadThreads();
            $this->updateUrlParameters();
        }
    }

    /**
     * Go to previous page
     */
    public function prevPage()
    {
        if ($this->currentPage > 1) {
            $this->currentPage--;
            $this->loadThreads();
            $this->updateUrlParameters();
        }
    }

    /**
     * Change per page count
     */
    public function changePerPage($count)
    {
        $this->perPage = $count;
        $this->currentPage = 1;
        $this->loadThreads();
        $this->updateUrlParameters();
    }



    /**
     * Update URL parameters to reflect current filters
     */
    private function updateUrlParameters()
    {
        $params = [];

        if ($this->filterType !== 'all') {
            $params['filter'] = $this->filterType;
        }

        if (!empty($this->searchTerm)) {
            $params['search'] = $this->searchTerm;
        }

        if ($this->currentPage > 1) {
            $params['page'] = $this->currentPage;
        }

        if ($this->perPage !== 10) {
            $params['per_page'] = $this->perPage;
        }

        if ($this->unreadFilter) {
            $params['unread'] = '1';
        }

        // If there's a thread ID in the URL, keep it
        if (request()->has('pesan_id')) {
            $params['pesan_id'] = request()->query('pesan_id');
        }

        $url = url()->current();
        if (!empty($params)) {
            $url .= '?' . http_build_query($params);
        }

        $this->dispatchBrowserEvent('updateUrlWithoutReload', ['url' => $url]);
    }

    public function render()
    {
        return view('livewire.message.message-list');
    }
}
