<?php

namespace App\Http\Livewire\Messenger;

use App\Models\MessengerUser;
use App\Models\User;
use Livewire\Component;

class ChatList extends Component
{
    public $users = [];
    public $selectedUser = null;
    public $unreadFilter = false;
    public $jobFilter = null;
    public $jobs = [];

    protected $listeners = [
        'userSelected' => 'selectUser',
        'messageSent' => 'refreshList',
        'refreshChatList' => 'refreshList'
    ];

    public function mount()
    {
        $this->loadUsers();
        $this->loadJobs();
    }

    public function loadUsers()
    {
        $query = MessengerUser::query();

        if (auth()->user()->role == 'company') {
            $query->where('company_id', currentCompany()->id);

            if ($this->jobFilter) {
                $query->where('job_id', $this->jobFilter);
            }
        } else {
            $query->where('candidate_id', currentCandidate()->id);
        }

        $users = $query->with([
            'company.user',
            'candidate.user',
            'latestMessage'
        ])->get();

        foreach ($users as $user) {
            $user->unread_count = $this->getUnreadMessageCount($user->id);

            if (auth()->user()->role == 'company') {
                $user->latest_message = $user->latestMessage ? $user->latestMessage->body : null;
                $user->latest_message_humans_time = $user->latestMessage ? $user->latestMessage->created_at->diffForHumans() : null;
            } else {
                $user->latest_message = $user->latestMessage ? $user->latestMessage->body : null;
                $user->latest_message_humans_time = $user->latestMessage ? $user->latestMessage->created_at->diffForHumans() : null;
            }
        }

        if ($this->unreadFilter) {
            $users = $users->filter(function ($user) {
                return $user->unread_count > 0;
            });
        }

        $this->users = $users;
    }

    public function loadJobs()
    {
        if (auth()->user()->role == 'company') {
            $this->jobs = currentCompany()->jobs()->active()->get();
        }
    }

    public function selectUser($userId)
    {
        $this->selectedUser = $userId;
        $this->emit('userSelected', $userId);
    }

    public function toggleUnreadFilter()
    {
        $this->unreadFilter = !$this->unreadFilter;
        $this->loadUsers();
    }

    public function filterByJob($jobId)
    {
        $this->jobFilter = $jobId;
        $this->loadUsers();
    }

    public function refreshList()
    {
        $this->loadUsers();
    }

    protected function getUnreadMessageCount($id)
    {
        $auth_user = auth()->user();

        if ($auth_user->role == 'company') {
            $user_messenger_id = MessengerUser::where('company_id', currentCompany()->id)->where('id', $id)->value('id');

            $unread_count = \App\Models\Messenger::where('messenger_user_id', $user_messenger_id)
                ->where('to', auth()->id())
                ->where('read', '!=', 1)
                ->count();
        } else {
            $user_messenger_id = MessengerUser::where('id', $id)->where('candidate_id', currentCandidate()->id)->value('id');

            $unread_count = \App\Models\Messenger::where('messenger_user_id', $user_messenger_id)
                ->where('to', auth()->id())
                ->where('read', '!=', 1)
                ->count();
        }

        return $unread_count ?? 0;
    }

    public function render()
    {
        return view('livewire.messenger.chat-list');
    }
}
