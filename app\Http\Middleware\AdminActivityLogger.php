<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;
use App\Services\LogService;
use Illuminate\Support\Facades\Auth;

class AdminActivityLogger
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        $response = $next($request);

        // Only log for authenticated admin users
        if (Auth::guard('admin')->check()) {
            $this->logActivity($request, $response);
        }

        return $response;
    }

    /**
     * Log admin activity based on request
     */
    private function logActivity(Request $request, Response $response)
    {
        // Only log successful requests (2xx status codes)
        if ($response->getStatusCode() < 200 || $response->getStatusCode() >= 300) {
            return;
        }

        $method = $request->method();
        $route = $request->route();

        if (!$route) {
            return;
        }

        $routeName = $route->getName();
        $action = $route->getActionName();

        // Skip logging for certain routes
        if ($this->shouldSkipLogging($routeName, $method)) {
            return;
        }

        // Determine action type and description
        $actionData = $this->determineAction($request, $routeName, $method);

        if ($actionData) {
            LogService::recordAdminActivity(
                $actionData['action'],
                $actionData['module'],
                $actionData['description']
            );
        }
    }

    /**
     * Determine if we should skip logging for this route
     */
    private function shouldSkipLogging($routeName, $method)
    {
        // Skip GET requests for viewing pages
        if ($method === 'GET') {
            return true;
        }

        // Skip certain routes
        $skipRoutes = [
            'admin.logout',
            'admin.login',
            // Add other routes to skip
        ];

        return in_array($routeName, $skipRoutes);
    }

    /**
     * Determine action type and description based on route
     */
    private function determineAction(Request $request, $routeName, $method)
    {
        // Map of route patterns to actions
        $routePatterns = [
            // Job routes
            'job.store' => ['action' => 'create', 'module' => 'job', 'description' => 'Membuat lowongan kerja'],
            'job.update' => ['action' => 'update', 'module' => 'job', 'description' => 'Mengupdate lowongan kerja'],
            'job.destroy' => ['action' => 'delete', 'module' => 'job', 'description' => 'Menghapus lowongan kerja'],

            // Company routes
            'company.store' => ['action' => 'create', 'module' => 'company', 'description' => 'Membuat perusahaan'],
            'company.update' => ['action' => 'update', 'module' => 'company', 'description' => 'Mengupdate perusahaan'],
            'company.destroy' => ['action' => 'delete', 'module' => 'company', 'description' => 'Menghapus perusahaan'],

            // Candidate routes
            'candidate.store' => ['action' => 'create', 'module' => 'candidate', 'description' => 'Membuat kandidat'],
            'candidate.update' => ['action' => 'update', 'module' => 'candidate', 'description' => 'Mengupdate kandidat'],
            'candidate.destroy' => ['action' => 'delete', 'module' => 'candidate', 'description' => 'Menghapus kandidat'],

            // Job Category routes
            'jobCategory.store' => ['action' => 'create', 'module' => 'job_category', 'description' => 'Membuat kategori lowongan'],
            'jobCategory.update' => ['action' => 'update', 'module' => 'job_category', 'description' => 'Mengupdate kategori lowongan'],
            'jobCategory.destroy' => ['action' => 'delete', 'module' => 'job_category', 'description' => 'Menghapus kategori lowongan'],

            // User routes
            'user.store' => ['action' => 'create', 'module' => 'user', 'description' => 'Membuat user admin'],
            'user.update' => ['action' => 'update', 'module' => 'user', 'description' => 'Mengupdate user admin'],
            'user.destroy' => ['action' => 'delete', 'module' => 'user', 'description' => 'Menghapus user admin'],

            // Role routes
            'role.store' => ['action' => 'create', 'module' => 'role', 'description' => 'Membuat role'],
            'role.update' => ['action' => 'update', 'module' => 'role', 'description' => 'Mengupdate role'],
            'role.destroy' => ['action' => 'delete', 'module' => 'role', 'description' => 'Menghapus role'],

            // Skill routes
            'skill.store' => ['action' => 'create', 'module' => 'skill', 'description' => 'Membuat skill'],
            'skill.update' => ['action' => 'update', 'module' => 'skill', 'description' => 'Mengupdate skill'],
            'skill.destroy' => ['action' => 'delete', 'module' => 'skill', 'description' => 'Menghapus skill'],

            // Settings routes
            'settings.general.update' => ['action' => 'update', 'module' => 'settings', 'description' => 'Mengupdate pengaturan umum'],
            'settings.email.update' => ['action' => 'update', 'module' => 'settings', 'description' => 'Mengupdate pengaturan email'],
            'settings.system.update' => ['action' => 'update', 'module' => 'settings', 'description' => 'Mengupdate pengaturan sistem'],
        ];

        // Check exact route name match
        if (isset($routePatterns[$routeName])) {
            return $routePatterns[$routeName];
        }

        // Check for pattern matches
        foreach ($routePatterns as $pattern => $actionData) {
            if (str_contains($routeName, $pattern)) {
                return $actionData;
            }
        }

        // Generic fallback based on HTTP method
        if ($method === 'POST') {
            return ['action' => 'create', 'module' => 'admin', 'description' => 'Membuat data baru'];
        } elseif ($method === 'PUT' || $method === 'PATCH') {
            return ['action' => 'update', 'module' => 'admin', 'description' => 'Mengupdate data'];
        } elseif ($method === 'DELETE') {
            return ['action' => 'delete', 'module' => 'admin', 'description' => 'Menghapus data'];
        }

        return null;
    }
}
