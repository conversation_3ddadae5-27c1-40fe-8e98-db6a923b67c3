<?php

namespace App\Listeners;

use App\Events\NewFeedbackEvent;
use App\Models\Admin;
use App\Notifications\NewFeedbackNotification;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;

class SendNewFeedbackNotification
{
    /**
     * Create the event listener.
     *
     * @return void
     */
    public function __construct()
    {
        //
    }

    /**
     * Handle the event.
     *
     * @param  \App\Events\NewFeedbackEvent  $event
     * @return void
     */
    public function handle(NewFeedbackEvent $event)
    {
        $admins = Admin::all();
        
        foreach ($admins as $admin) {
            $admin->notify(new NewFeedbackNotification($event->feedback));
        }
    }
}
