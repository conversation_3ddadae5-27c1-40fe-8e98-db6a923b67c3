<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class CandidateCertification extends Model
{
    use HasFactory;

    protected $fillable = [
        'candidate_id',
        'name',
        'organization',
        'issue_date',
        'expiration_date',
        'credential_id',
        'credential_url',
    ];

    protected $appends = ['formatted_issue_date', 'formatted_expiration_date'];

    public function getFormattedIssueDateAttribute()
    {
        return $this->issue_date ? formatTime($this->issue_date, 'd M Y') : null;
    }

    public function getFormattedExpirationDateAttribute()
    {
        return $this->expiration_date ? formatTime($this->expiration_date, 'd M Y') : null;
    }

    public function candidate()
    {
        return $this->belongsTo(Candidate::class);
    }
}
