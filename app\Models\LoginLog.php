<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class LoginLog extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'user_type',
        'ip_address',
        'device',
        'browser',
        'location',
        'login_at',
        'logout_at',
        'session_id',
        'is_active'
    ];

    protected $casts = [
        'login_at' => 'datetime',
        'logout_at' => 'datetime',
        'is_active' => 'boolean'
    ];

    public function user()
    {
        return $this->morphTo('user', 'user_type', 'user_id');
    }
}
