<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class Message extends Model
{
    use HasFactory;
    use SoftDeletes;

    protected $fillable = [
        'message_thread_id',
        'sender_id',
        'sender_type',
        'receiver_id',
        'body',
        'type',
        'can_reply',
        'read',
        'attachment',
    ];

    protected $appends = ['created_time'];

    protected $casts = [
        'attachment' => 'array',
        'read' => 'boolean',
        'can_reply' => 'boolean',
    ];

    public function getCreatedTimeAttribute()
    {
        return $this->created_at->diffForHumans();
    }

    public function thread()
    {
        return $this->belongsTo(MessageThread::class, 'message_thread_id');
    }

    public function sender()
    {
        if ($this->sender_type == 'admin') {
            return $this->belongsTo(\App\Models\Admin::class, 'sender_id');
        } else {
            return $this->belongsTo(User::class, 'sender_id');
        }
    }

    /**
     * Get the sender name attribute
     */
    public function getSenderNameAttribute()
    {
        if ($this->sender_type == 'admin') {
            return 'Admin';
        } elseif ($this->sender) {
            return $this->sender->name;
        }

        return 'Unknown';
    }

    /**
     * Get the sender image attribute
     */
    public function getSenderImageAttribute()
    {
        if ($this->sender_type == 'admin') {
            return null; // Will use admin icon
        } elseif ($this->sender && $this->sender->role == 'company') {
            $logo = $this->sender->company->logo ?? 'frontend/assets/images/default-company.png';
            return str_replace('/company/uploads/', '/uploads/', $logo);
        } elseif ($this->sender && $this->sender->role == 'candidate') {
            $photo = $this->sender->candidate->photo ?? 'frontend/assets/images/default-user.png';
            return str_replace('/public/storage/', '/uploads/', str_replace('/storage/', '/uploads/', $photo));
        }

        return 'frontend/assets/images/default-user.png';
    }

    public function receiver()
    {
        return $this->belongsTo(User::class, 'receiver_id');
    }

    public function scopeUnread($query)
    {
        return $query->where('read', false);
    }

    public function scopeForUser($query, $userId)
    {
        return $query->where(function ($q) use ($userId) {
            $q->where('sender_id', $userId)
                ->orWhere('receiver_id', $userId);
        });
    }
}
