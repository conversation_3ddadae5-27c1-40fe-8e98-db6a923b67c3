<?php

namespace App\Notifications;

use App\Models\Feedback;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;

class FeedbackReadNotification extends Notification
{
    use Queueable;

    protected $feedback;

    /**
     * Create a new notification instance.
     *
     * @param  \App\Models\Feedback  $feedback
     * @return void
     */
    public function __construct(Feedback $feedback)
    {
        $this->feedback = $feedback;
    }

    /**
     * Get the notification's delivery channels.
     *
     * @param  mixed  $notifiable
     * @return array
     */
    public function via($notifiable)
    {
        return ['database'];
    }

    /**
     * Get the mail representation of the notification.
     *
     * @param  mixed  $notifiable
     * @return \Illuminate\Notifications\Messages\MailMessage
     */
    public function toMail($notifiable)
    {
        return (new MailMessage)
                    ->subject('Saran Anda Telah Dibaca')
                    ->line('Saran dan masukan Anda telah dibaca oleh admin.')
                    ->line('Terima kasih atas kontribusi Anda untuk meningkatkan layanan kami!');
    }

    /**
     * Get the array representation of the notification.
     *
     * @param  mixed  $notifiable
     * @return array
     */
    public function toArray($notifiable)
    {
        return [
            'feedback_id' => $this->feedback->id,
            'message' => 'Saran dan masukan Anda telah dibaca oleh admin.',
            'time' => now()->diffForHumans(),
            'title' => 'Saran Anda Telah Dibaca',
        ];
    }
}
