<?php

namespace App\Notifications;

use App\Models\Feedback;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;

class NewFeedbackNotification extends Notification
{
    use Queueable;

    protected $feedback;

    /**
     * Create a new notification instance.
     *
     * @param  \App\Models\Feedback  $feedback
     * @return void
     */
    public function __construct(Feedback $feedback)
    {
        $this->feedback = $feedback;
    }

    /**
     * Get the notification's delivery channels.
     *
     * @param  mixed  $notifiable
     * @return array
     */
    public function via($notifiable)
    {
        return ['database'];
    }

    /**
     * Get the mail representation of the notification.
     *
     * @param  mixed  $notifiable
     * @return \Illuminate\Notifications\Messages\MailMessage
     */
    public function toMail($notifiable)
    {
        return (new MailMessage)
                    ->subject('Saran Baru dari ' . $this->feedback->name)
                    ->line('Ada saran baru dari ' . $this->feedback->name)
                    ->line('Rating: ' . $this->feedback->rating . '/5')
                    ->line('Pesan: ' . $this->feedback->message)
                    ->action('Lihat Saran', url('/admin/feedback/' . $this->feedback->id))
                    ->line('Terima kasih telah menggunakan aplikasi kami!');
    }

    /**
     * Get the array representation of the notification.
     *
     * @param  mixed  $notifiable
     * @return array
     */
    public function toArray($notifiable)
    {
        return [
            'feedback_id' => $this->feedback->id,
            'name' => $this->feedback->name,
            'rating' => $this->feedback->rating,
            'message' => substr($this->feedback->message, 0, 100) . (strlen($this->feedback->message) > 100 ? '...' : ''),
            'url' => url('/admin/feedback?feedback_id=' . $this->feedback->id),
            'time' => $this->feedback->created_at->diffForHumans(),
            'title' => 'Saran baru dari ' . $this->feedback->name,
        ];
    }
}
