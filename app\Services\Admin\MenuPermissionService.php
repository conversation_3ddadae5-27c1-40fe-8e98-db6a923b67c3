<?php

namespace App\Services\Admin;

class MenuPermissionService
{
    /**
     * Get organized menu structure with permissions
     *
     * @return array
     */
    public static function getOrganizedMenus()
    {
        return [
            'dashboard' => [
                'title' => 'Dashboard & Laporan',
                'icon' => 'fas fa-tachometer-alt',
                'permissions' => [
                    'admin.view' => 'Akses Dashboard Admin',
                ]
            ],
            'user_management' => [
                'title' => 'Manajemen Pengguna',
                'icon' => 'fas fa-users',
                'permissions' => [
                    'company.view' => 'Lihat Perusahaan',
                    'company.create' => 'Tambah Perusahaan',
                    'company.update' => 'Edit Perusahaan',
                    'company.delete' => 'Hapus Perusahaan',
                    'candidate.view' => 'Lihat Pencaker',
                    'candidate.create' => 'Tambah Pencaker',
                    'candidate.update' => 'Edit Pencaker',
                    'candidate.delete' => 'Hapus Pencaker',
                ]
            ],
            'job_management' => [
                'title' => 'Manajemen <PERSON>ja',
                'icon' => 'fas fa-briefcase',
                'permissions' => [
                    'job.view' => 'Lihat Lowong<PERSON> Kerja',
                    'job.create' => 'Tambah Lowongan Kerja',
                    'job.update' => 'Edit Lowongan Kerja',
                    'job.delete' => 'Hapus Lowongan Kerja',
                    'job_category.view' => 'Lihat Kategori Lowongan',
                    'job_category.create' => 'Tambah Kategori Lowongan',
                    'job_category.update' => 'Edit Kategori Lowongan',
                    'job_category.delete' => 'Hapus Kategori Lowongan',
                    'job_role.view' => 'Lihat Bidang Pekerjaan',
                    'job_role.create' => 'Tambah Bidang Pekerjaan',
                    'job_role.update' => 'Edit Bidang Pekerjaan',
                    'job_role.delete' => 'Hapus Bidang Pekerjaan',
                ]
            ],
            'attributes' => [
                'title' => 'Atribut & Master Data',
                'icon' => 'fas fa-cogs',
                'permissions' => [
                    'industry_types.view' => 'Lihat Jenis Industri',
                    'industry_types.create' => 'Tambah Jenis Industri',
                    'industry_types.update' => 'Edit Jenis Industri',
                    'industry_types.delete' => 'Hapus Jenis Industri',
                    'professions.view' => 'Lihat Profesi',
                    'professions.create' => 'Tambah Profesi',
                    'professions.update' => 'Edit Profesi',
                    'professions.delete' => 'Hapus Profesi',
                    'skills.view' => 'Lihat Keterampilan',
                    'skills.create' => 'Tambah Keterampilan',
                    'skills.update' => 'Edit Keterampilan',
                    'skills.delete' => 'Hapus Keterampilan',
                    'tags.view' => 'Lihat Tag',
                    'tags.create' => 'Tambah Tag',
                    'tags.update' => 'Edit Tag',
                    'tags.delete' => 'Hapus Tag',
                    'benefits.view' => 'Lihat Benefit',
                    'benefits.create' => 'Tambah Benefit',
                    'benefits.update' => 'Edit Benefit',
                    'benefits.delete' => 'Hapus Benefit',
                    'candidate-language.view' => 'Lihat Bahasa Kandidat',
                    'candidate-language.create' => 'Tambah Bahasa Kandidat',
                    'candidate-language.update' => 'Edit Bahasa Kandidat',
                    'candidate-language.delete' => 'Hapus Bahasa Kandidat',
                ]
            ],
            'communication' => [
                'title' => 'Komunikasi',
                'icon' => 'fas fa-comments',
                'permissions' => [
                    'feedback.view' => 'Lihat Saran dan Masukan',
                    'feedback.reply' => 'Balas Saran dan Masukan',
                    'feedback.delete' => 'Hapus Saran dan Masukan',
                    'message.view' => 'Lihat Pesan',
                    'message.send' => 'Kirim Pesan',
                    'message.broadcast' => 'Kirim Broadcast',
                ]
            ],
            'content_management' => [
                'title' => 'Manajemen Konten',
                'icon' => 'fas fa-file-alt',
                'permissions' => [
                    'post.view' => 'Lihat Blog Post',
                    'post.create' => 'Tambah Blog Post',
                    'post.update' => 'Edit Blog Post',
                    'post.delete' => 'Hapus Blog Post',
                    'faq.view' => 'Lihat FAQ',
                    'faq.create' => 'Tambah FAQ',
                    'faq.update' => 'Edit FAQ',
                    'faq.delete' => 'Hapus FAQ',
                    'newsletter.view' => 'Lihat Newsletter',
                    'newsletter.sendmail' => 'Kirim Newsletter',
                ]
            ],
            'system_management' => [
                'title' => 'Manajemen Sistem',
                'icon' => 'fas fa-cog',
                'permissions' => [
                    'role.view' => 'Lihat Role',
                    'role.create' => 'Tambah Role',
                    'role.edit' => 'Edit Role',
                    'role.delete' => 'Hapus Role',
                    'setting.view' => 'Lihat Pengaturan',
                    'setting.update' => 'Update Pengaturan',
                    'menu-setting.index' => 'Lihat Pengaturan Menu',
                    'menu-setting.update' => 'Update Pengaturan Menu',
                ]
            ],
            'location_management' => [
                'title' => 'Manajemen Lokasi',
                'icon' => 'fas fa-map-marker-alt',
                'permissions' => [
                    'state.view' => 'Lihat Provinsi',
                    'state.create' => 'Tambah Provinsi',
                    'state.update' => 'Edit Provinsi',
                    'state.delete' => 'Hapus Provinsi',
                    'city.view' => 'Lihat Kota/Kabupaten',
                    'city.create' => 'Tambah Kota/Kabupaten',
                    'city.update' => 'Edit Kota/Kabupaten',
                    'city.delete' => 'Hapus Kota/Kabupaten',
                ]
            ]
        ];
    }

    /**
     * Get all permissions grouped by category
     *
     * @return array
     */
    public static function getAllPermissions()
    {
        $menus = self::getOrganizedMenus();
        $permissions = [];
        
        foreach ($menus as $category => $menu) {
            $permissions[$category] = [
                'title' => $menu['title'],
                'icon' => $menu['icon'],
                'permissions' => $menu['permissions']
            ];
        }
        
        return $permissions;
    }

    /**
     * Get permission names only
     *
     * @return array
     */
    public static function getPermissionNames()
    {
        $menus = self::getOrganizedMenus();
        $permissionNames = [];
        
        foreach ($menus as $menu) {
            $permissionNames = array_merge($permissionNames, array_keys($menu['permissions']));
        }
        
        return $permissionNames;
    }
}
