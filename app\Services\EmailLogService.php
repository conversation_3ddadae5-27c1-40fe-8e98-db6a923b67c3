<?php

namespace App\Services;

use App\Models\EmailLog;
use Illuminate\Support\Facades\Log;

class EmailLogService
{
    /**
     * Log an email
     *
     * @param string $from
     * @param string $to
     * @param string $subject
     * @param string $body
     * @param string $status
     * @param string|null $errorMessage
     * @return EmailLog
     */
    public static function log($from, $to, $subject, $body, $status = 'sent', $errorMessage = null)
    {
        try {
            return EmailLog::create([
                'from' => $from,
                'to' => $to,
                'subject' => $subject,
                'body' => $body,
                'status' => $status,
                'error_message' => $errorMessage,
                'sent_at' => now(),
            ]);
        } catch (\Exception $e) {
            Log::error('Failed to log email: ' . $e->getMessage());
            return null;
        }
    }
}
