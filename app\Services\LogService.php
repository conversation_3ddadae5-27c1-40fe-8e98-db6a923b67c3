<?php

namespace App\Services;

use App\Models\AdminLog;
use App\Models\LoginLog;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Session;
use Jen<PERSON><PERSON>\Agent\Agent;

class LogService
{
    /**
     * Get real IP address from request headers
     *
     * @return string
     */
    public static function getRealIpAddress()
    {
        $request = request();

        // Check for IP from various headers in order of preference
        $headers = [
            'HTTP_CF_CONNECTING_IP',     // Cloudflare
            'HTTP_CLIENT_IP',            // Proxy
            'HTTP_X_FORWARDED_FOR',      // Load balancer/proxy
            'HTTP_X_FORWARDED',          // Proxy
            'HTTP_X_CLUSTER_CLIENT_IP',  // Cluster
            'HTTP_FORWARDED_FOR',        // Proxy
            'HTTP_FORWARDED',            // Proxy
            'HTTP_X_REAL_IP',            // Nginx proxy
            'REMOTE_ADDR'                // Standard
        ];

        foreach ($headers as $header) {
            if ($request->server($header)) {
                $ips = explode(',', $request->server($header));
                $ip = trim($ips[0]);

                // Validate IP address
                if (filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_NO_PRIV_RANGE | FILTER_FLAG_NO_RES_RANGE)) {
                    return $ip;
                }
            }
        }

        // Fallback to request IP
        return $request->ip();
    }

    /**
     * Record login activity
     *
     * @param mixed $user
     * @param string $userType
     * @return LoginLog
     */
    public static function recordLogin($user, $userType = 'admin')
    {
        $ip = self::getRealIpAddress();
        $location = self::getLocationFromIp($ip);
        $sessionId = Session::getId();

        // Check if Agent class exists
        if (class_exists('Jenssegers\Agent\Agent')) {
            $agent = new Agent();
            $device = $agent->device();
            $browser = $agent->browser();
        } else {
            // Fallback if Agent class is not available
            $device = 'Unknown';
            $browser = 'Unknown';
        }

        // Deactivate any active sessions for this user
        LoginLog::where('user_id', $user->id)
            ->where('user_type', $userType)
            ->where('is_active', true)
            ->update([
                'is_active' => false,
                'logout_at' => now()
            ]);

        // Create new login log
        return LoginLog::create([
            'user_id' => $user->id,
            'user_type' => $userType,
            'ip_address' => $ip,
            'device' => $device,
            'browser' => $browser,
            'location' => $location,
            'login_at' => now(),
            'session_id' => $sessionId,
            'is_active' => true
        ]);
    }

    /**
     * Record logout activity
     *
     * @param mixed $user
     * @param string $userType
     * @return bool
     */
    public static function recordLogout($user = null, $userType = 'admin')
    {
        if (!$user && Auth::guard('admin')->check()) {
            $user = Auth::guard('admin')->user();
        } elseif (!$user) {
            return false;
        }

        $sessionId = Session::getId();

        return LoginLog::where('user_id', $user->id)
            ->where('user_type', $userType)
            ->where('session_id', $sessionId)
            ->where('is_active', true)
            ->update([
                'is_active' => false,
                'logout_at' => now()
            ]);
    }

    /**
     * Record admin activity
     *
     * @param string $action
     * @param string $module
     * @param string $description
     * @return AdminLog|null
     */
    public static function recordAdminActivity($action, $module, $description)
    {
        if (!Auth::guard('admin')->check()) {
            return null;
        }

        $admin = Auth::guard('admin')->user();
        $ip = self::getRealIpAddress();
        $location = self::getLocationFromIp($ip);

        // Check if Agent class exists and combine device + browser info
        if (class_exists('Jenssegers\Agent\Agent')) {
            $agent = new Agent();
            $device = $agent->device() ?: 'Unknown Device';
            $browser = $agent->browser() ?: 'Unknown Browser';
            $deviceInfo = $device . ' / ' . $browser;
        } else {
            // Fallback if Agent class is not available
            $deviceInfo = 'Unknown Device / Unknown Browser';
        }

        return AdminLog::create([
            'admin_id' => $admin->id,
            'action' => $action,
            'module' => $module,
            'description' => $description,
            'ip_address' => $ip,
            'location' => $location,
            'device' => $deviceInfo,
            'browser' => null // We'll keep this for backward compatibility but won't use it
        ]);
    }

    /**
     * Get location from IP address
     *
     * @param string $ip
     * @return string
     */
    private static function getLocationFromIp($ip)
    {
        try {
            // Use free IP geolocation API
            $response = file_get_contents("http://ip-api.com/json/{$ip}");
            $data = json_decode($response, true);

            if ($data && $data['status'] === 'success') {
                return $data['city'] . ', ' . $data['country'];
            }
        } catch (\Exception $e) {
            // Silently fail and return unknown
        }

        return 'Unknown';
    }

    /**
     * Check if user has another active session
     *
     * @param mixed $user
     * @param string $userType
     * @return bool
     */
    public static function hasAnotherActiveSession($user, $userType = 'admin')
    {
        $sessionId = Session::getId();

        return LoginLog::where('user_id', $user->id)
            ->where('user_type', $userType)
            ->where('session_id', '!=', $sessionId)
            ->where('is_active', true)
            ->exists();
    }

    /**
     * Force logout from all other sessions
     *
     * @param mixed $user
     * @param string $userType
     * @return bool
     */
    public static function forceLogoutOtherSessions($user, $userType = 'admin')
    {
        $sessionId = Session::getId();

        return LoginLog::where('user_id', $user->id)
            ->where('user_type', $userType)
            ->where('session_id', '!=', $sessionId)
            ->where('is_active', true)
            ->update([
                'is_active' => false,
                'logout_at' => now()
            ]);
    }
}
