<?php

namespace App\Services\Website\Company;

use App\Models\Company;
use App\Models\ContactInfo;
use App\Models\IndustryType;
use App\Models\IndustryTypeTranslation;
use App\Models\OrganizationType;
use App\Models\OrganizationTypeTranslation;
use App\Models\User;
use Illuminate\Support\Str;

class CompanyAccountProgressService
{
    /**
     * Get company account progress
     *
     * @return void
     */
    public function execute($request)
    {
        $company = currentCompany();

        switch ($request->field) {
            case 'personal':
                $image_validation = $company->logo ? 'sometimes|image|mimes:jpeg,png,jpg|max:10240' : 'required|image|mimes:jpeg,png,jpg|max:10240';
                $banner_validation = 'nullable|image|mimes:jpeg,png,jpg|max:10240'; // Set banner menjadi nullable

                $request->validate([
                    'image' => $image_validation,
                    'banner' => $banner_validation,
                    'name' => 'nullable|max:255',
                    'bio' => 'required',
                ], [
                    'image.required' => 'The logo field is required.',
                ]);

                $update = $this->personalProfileUpdate($request);
                if ($update) {
                    return redirect('company/account-progress?profile');
                }

                return back();
                break;

            case 'profile':
                $request->validate([
                    'organization_type_id' => 'required|string',
                    'industry_type_id' => 'required|string',
                    'establishment_date' => 'nullable',
                    'website' => 'nullable|url',
                    'vision' => 'required',
                ]);

                $update = $this->companyProfileUpdate($request);
                if ($update) {
                    return redirect('company/account-progress?social')->send();
                }

                return back()->send();
                break;

            case 'social':
                $update = $this->socialProfileUpdate($request);
                if ($update) {
                    return redirect('company/account-progress?contact')->send();
                }

                return back()->send();
                break;

            case 'contact':
                $request->validate([
                    'email' => 'required|email',
                    'phone' => 'required|min:4|max:16',
                ]);

                $location = session()->get('location');
                if (!$location) {
                    $request->validate([
                        'location' => 'required',
                    ]);
                }

                $update = $this->contactProfileUpdate($request);
                if ($update) {
                    return redirect('company/account-progress?complete')->send();
                }

                return back()->send();
                break;

            case 'complete':
                return view('frontend.pages.company.account-progress.complete');
                break;

            default:
                return back()->send();
        }
    }

    /**
     * Personal Profile Update
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    // public function personalProfileUpdate($request)
    // {
    //     $user = User::findOrFail(auth()->user()->id);
    //     $company = Company::where('user_id', $user->id)->firstOrFail();
    //     $name = $request->name ?? fake()->name();
    //     $newUsername = Str::slug($name);
    //     $user->update(['name' => $name,  'username' => $newUsername]);

    //     if ($request->hasFile('image')) {
    //         $image = uploadImage($request->image, 'images/company');
    //         $company->logo = $image;
    //     } else {
    //         if (! $company->logo) {
    //             $company->logo = createAvatar($name, 'uploads/images/company');
    //         }
    //     }

    //     if ($request->hasFile('banner')) {
    //         $banner = uploadImage($request->banner, 'images/company');
    //         $company->banner = $banner;
    //     } else {
    //         if (! $company->banner) {
    //             $company->banner = createAvatar($name, 'uploads/images/company');
    //         }
    //     }

    //     $company->bio = $request->bio;
    //     $company->save();

    //     return true;
    // }
    public function personalProfileUpdate($request)
    {
        $user = User::findOrFail(auth()->user()->id);
        $company = Company::where('user_id', $user->id)->firstOrFail();

        // Nama perusahaan tidak diubah di step ini karena sudah disabled
        // Tapi kita perlu memastikan username dibuat dengan format yang benar
        $name = $user->name; // Gunakan nama yang sudah ada

        // Buat username dengan format (prefix)-(nama-perusahaan)
        $organizationType = null;
        if ($company->organization_type_id) {
            $organizationType = \App\Models\OrganizationTypeTranslation::where('organization_type_id', $company->organization_type_id)
                ->where('locale', 'id')
                ->first();
        }

        if ($organizationType && $organizationType->prefix) {
            $prefix = Str::slug($organizationType->prefix);
            $companySlug = Str::slug($name);
            $newUsername = $prefix . '-' . $companySlug;
        } else {
            $newUsername = Str::slug($name);
        }

        // Pastikan username unik
        $originalUsername = $newUsername;
        $counter = 1;
        while (User::where('username', $newUsername)->where('id', '!=', $user->id)->exists()) {
            $newUsername = $originalUsername . '-' . $counter;
            $counter++;
        }

        $user->update(['username' => $newUsername]);

        if ($request->hasFile('image')) {
            // $image = uploadImage($request->image, 'images/company');

            $path = 'uploads/images/company';

            $image = uploadImage($request->image, $path, [68, 68]);

            $company->logo = $image;
        } else {
            if (! $company->logo) {
                // $company->logo = createAvatar($name, 'uploads/images/company');

                $setDimension = [100, 100]; //Here needs to be [68, 68] but avatar image not looks good in view that's why increase value 100 from 68
                $path = 'uploads/images/company';
                $image = createAvatar($name, $path, $setDimension);
            }
        }

        if ($request->hasFile('banner')) {
            // $banner = uploadImage($request->banner, 'images/company');

            $path = 'uploads/images/company';
            $banner = uploadImage($request->banner, $path, [1920, 312]);

            $company->banner = $banner;
        } else {
            if (! $company->banner) {
                // $company->banner = createAvatar($name, 'uploads/images/company');
                $setDimension = [1920, 312];
                $path = 'uploads/images/company';
                $banner = createAvatar($name, $path, $setDimension);
            }
        }

        $company->bio = $request->bio;
        $company->save();

        return true;
    }

    /**
     * Contact Profile Update
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function companyProfileUpdate($request)
    {
        // Organization Type
        $organization_request = $request->organization_type_id;
        $organization_type = OrganizationTypeTranslation::where('organization_type_id', $organization_request)->orWhere('name', $organization_request)->first();

        if (! $organization_type) {
            $new_organization_type = new OrganizationType();

            $languages = loadLanguage();
            foreach ($languages as $language) {
                $new_organization_type->translateOrNew($language->code)->name = $organization_type;
            }
            $new_organization_type->save();

            $organization_type_id = $new_organization_type->id;
        } else {
            $organization_type_id = $organization_type->organization_type_id;
        }

        // Industry Type
        $industry_request = $request->industry_type_id;
        $industry_type = IndustryTypeTranslation::where('industry_type_id', $industry_request)->orWhere('name', $industry_request)->first();

        if (! $industry_type) {
            // Jika jenis industri baru, buat record baru
            $new_industry_type = new IndustryType();
            $new_industry_type->save();

            // Tambahkan terjemahan untuk semua bahasa yang tersedia
            $languages = loadLanguage();
            foreach ($languages as $language) {
                // Pastikan nama tidak null
                $industry_name = $industry_request ?: 'Industri Baru';

                // Buat terjemahan untuk setiap bahasa
                $new_industry_type->translateOrNew($language->code)->name = $industry_name;
                $new_industry_type->save();
            }

            $industry_type_id = $new_industry_type->id;
        } else {
            $industry_type_id = $industry_type->industry_type_id;
        }

        // Format website URL
        $website = $request->website;
        if ($website && !str_starts_with($website, 'http://') && !str_starts_with($website, 'https://')) {
            $website = 'https://' . $website;
        }

        $company = Company::where('user_id', auth()->user()->id);
        $company->update([
            'organization_type_id' => $organization_type_id,
            'industry_type_id' => $industry_type_id,
            'establishment_date' => $request->establishment_date ? date('Y-m-d', strtotime($request->establishment_date)) : null,
            'team_size_id' => $request->team_size_id,
            'website' => $website,
            'vision' => $request->vision,
        ]);

        return $company;
    }

    /**
     * Social Profile Update
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function socialProfileUpdate($request)
    {
        $social_medias = $request->social_media;
        $urls = $request->url;

        $user = User::find(auth()->id());
        $user->socialInfo()->delete();

        if ($social_medias && $urls) {

            foreach ($social_medias as $key => $value) {
                if ($value && $urls[$key]) {
                    // Generate full URL based on platform
                    $fullUrl = $this->generateSocialMediaUrl($value, $urls[$key]);

                    $user->socialInfo()->create([
                        'social_media' => $value,
                        'url' => $fullUrl,
                    ]);
                }
            }
        }

        return true;
    }

    /**
     * Generate full social media URL based on platform and username
     */
    private function generateSocialMediaUrl($platform, $input)
    {
        // If it's 'other' or already a full URL, return as is
        if ($platform === 'other' || str_starts_with($input, 'http://') || str_starts_with($input, 'https://')) {
            return $input;
        }

        // Remove @ symbol if present
        $username = ltrim($input, '@');

        // Generate URL based on platform
        switch ($platform) {
            case 'facebook':
                return 'https://www.facebook.com/' . $username;
            case 'twitter':
                return 'https://twitter.com/' . $username;
            case 'instagram':
                return 'https://www.instagram.com/' . $username;
            case 'youtube':
                return 'https://www.youtube.com/@' . $username;
            case 'linkedin':
                return 'https://www.linkedin.com/in/' . $username;
            case 'pinterest':
                return 'https://www.pinterest.com/' . $username;
            case 'reddit':
                return 'https://www.reddit.com/user/' . $username;
            case 'github':
                return 'https://github.com/' . $username;
            default:
                return $input;
        }
    }

    /**
     * Contact Profile Update
     *
     * @param  \Illuminate\Http\Request  $request
     */
    public function contactProfileUpdate($request): mixed
    {
        $user = User::findOrFail(auth()->user()->id);

        // Format phone number
        $phone = $request->phone;
        if ($phone) {
            $phone = $this->formatPhoneNumber($phone);
        }

        $contact = ContactInfo::where('user_id', $user->id)->update([
            'phone' => $phone,
            'email' => $request->email
        ]);

        // Update company coordinates if provided
        if ($request->lat && $request->long) {
            $company = Company::where('user_id', $user->id)->first();
            if ($company) {
                $company->update([
                    'lat' => $request->lat,
                    'long' => $request->long,
                ]);
            }
        }

        // =========== Location ===========
        // Ambil objek Company terlebih dahulu, bukan relasi HasOne
        $company = Company::where('user_id', $user->id)->first();
        if ($company) {
            updateMap($company);
        }

        if ($contact) {
            Company::where('user_id', $user->id)->update([
                'profile_completion' => 1,
            ]);

            return $contact;
        }

        return false;
    }

    /**
     * Format phone number to Indonesian format
     */
    private function formatPhoneNumber($phone)
    {
        // Remove any non-digit characters except +
        $phone = preg_replace('/[^\d+]/', '', $phone);

        // Format phone number
        if (str_starts_with($phone, '08')) {
            // Replace 08 with 628
            $phone = '628' . substr($phone, 2);
        } elseif (str_starts_with($phone, '02')) {
            // Replace 02 with 622
            $phone = '622' . substr($phone, 2);
        } elseif (str_starts_with($phone, '0')) {
            // Replace other 0 prefixes with 62
            $phone = '62' . substr($phone, 1);
        } elseif (!str_starts_with($phone, '62') && !str_starts_with($phone, '+62')) {
            // If doesn't start with 62 or +62, assume it's local number starting with 8
            if (str_starts_with($phone, '8')) {
                $phone = '62' . $phone;
            }
        }

        // Ensure it starts with +62
        if (str_starts_with($phone, '62') && !str_starts_with($phone, '+62')) {
            $phone = '+' . $phone;
        }

        return $phone;
    }
}
