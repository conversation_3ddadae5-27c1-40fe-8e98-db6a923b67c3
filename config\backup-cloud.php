<?php

return [
    /*
    |--------------------------------------------------------------------------
    | Cloud Storage Settings
    |--------------------------------------------------------------------------
    |
    | This file contains the configuration for cloud storage services
    | used for backup uploads.
    |
    */

    'cloud' => [
        'google_drive' => [
            'enabled' => env('BACKUP_GOOGLE_DRIVE_ENABLED', false),
            'client_id' => env('GOOGLE_DRIVE_CLIENT_ID', ''),
            'client_secret' => env('GOOGLE_DRIVE_CLIENT_SECRET', ''),
            'refresh_token' => env('GOOGLE_DRIVE_REFRESH_TOKEN', ''),
            'folder_id' => env('GOOGLE_DRIVE_FOLDER_ID', ''),
        ],
        
        'amazon_s3' => [
            'enabled' => env('BACKUP_AMAZON_S3_ENABLED', false),
            'key' => env('AWS_ACCESS_KEY_ID', ''),
            'secret' => env('AWS_SECRET_ACCESS_KEY', ''),
            'region' => env('AWS_DEFAULT_REGION', 'us-east-1'),
            'bucket' => env('AWS_BUCKET', ''),
        ],
        
        'backblaze' => [
            'enabled' => env('BACKUP_BACKBLAZE_ENABLED', false),
            'key_id' => env('BACKBLAZE_KEY_ID', ''),
            'application_key' => env('BACKBLAZE_APPLICATION_KEY', ''),
            'bucket' => env('BACKBLAZE_BUCKET', ''),
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Backup Schedule Settings
    |--------------------------------------------------------------------------
    |
    | This section contains the configuration for automatic backup scheduling.
    |
    */

    'schedule' => [
        'enabled' => env('BACKUP_SCHEDULE_ENABLED', false),
        'frequency' => env('BACKUP_SCHEDULE_FREQUENCY', 'daily'), // daily, weekly, monthly
        'time' => env('BACKUP_SCHEDULE_TIME', '00:00'), // Format: HH:MM
        'keep_days' => env('BACKUP_SCHEDULE_KEEP_DAYS', 7), // Number of days to keep backups
    ],
];
