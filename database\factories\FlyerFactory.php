<?php

namespace Database\Factories;

use App\Models\Company;
use App\Models\Flyer;
use App\Models\Job;
use Illuminate\Database\Eloquent\Factories\Factory;

class FlyerFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = Flyer::class;

    /**
     * Define the model's default state.
     *
     * @return array
     */
    public function definition()
    {
        $company = Company::inRandomOrder()->first() ?? Company::factory()->create();
        
        return [
            'company_id' => $company->id,
            'company_name' => $company->user->name ?? $this->faker->company,
            'hrd_name' => $this->faker->name,
            'phone_number' => $this->faker->phoneNumber,
            'additional_info' => $this->faker->paragraph,
            'image' => 'test.jpg',
            'status' => $this->faker->randomElement(['pending', 'active', 'rejected', 'revision']),
            'rejection_reason' => null,
            'job_id' => null,
        ];
    }

    /**
     * Indicate that the flyer is pending.
     *
     * @return \Illuminate\Database\Eloquent\Factories\Factory
     */
    public function pending()
    {
        return $this->state(function (array $attributes) {
            return [
                'status' => 'pending',
                'rejection_reason' => null,
            ];
        });
    }

    /**
     * Indicate that the flyer is active.
     *
     * @return \Illuminate\Database\Eloquent\Factories\Factory
     */
    public function active()
    {
        return $this->state(function (array $attributes) {
            $job = Job::factory()->create([
                'company_id' => $attributes['company_id'],
            ]);
            
            return [
                'status' => 'active',
                'rejection_reason' => null,
                'job_id' => $job->id,
            ];
        });
    }

    /**
     * Indicate that the flyer is rejected.
     *
     * @return \Illuminate\Database\Eloquent\Factories\Factory
     */
    public function rejected()
    {
        return $this->state(function (array $attributes) {
            return [
                'status' => 'rejected',
                'rejection_reason' => $this->faker->sentence,
            ];
        });
    }

    /**
     * Indicate that the flyer needs revision.
     *
     * @return \Illuminate\Database\Eloquent\Factories\Factory
     */
    public function revision()
    {
        return $this->state(function (array $attributes) {
            return [
                'status' => 'revision',
                'rejection_reason' => $this->faker->sentence,
            ];
        });
    }
}
