<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Add backup cloud settings to settings table
        if (Schema::hasTable('settings')) {
            Schema::table('settings', function (Blueprint $table) {
                if (!Schema::hasColumn('settings', 'backup_schedule_enabled')) {
                    $table->boolean('backup_schedule_enabled')->default(false);
                }
                if (!Schema::hasColumn('settings', 'backup_schedule_frequency')) {
                    $table->string('backup_schedule_frequency')->default('daily');
                }
                if (!Schema::hasColumn('settings', 'backup_schedule_time')) {
                    $table->string('backup_schedule_time')->default('00:00');
                }
                if (!Schema::hasColumn('settings', 'backup_schedule_keep_days')) {
                    $table->integer('backup_schedule_keep_days')->default(7);
                }
                if (!Schema::hasColumn('settings', 'backup_cloud_google_drive_enabled')) {
                    $table->boolean('backup_cloud_google_drive_enabled')->default(false);
                }
                if (!Schema::hasColumn('settings', 'backup_cloud_amazon_s3_enabled')) {
                    $table->boolean('backup_cloud_amazon_s3_enabled')->default(false);
                }
                if (!Schema::hasColumn('settings', 'backup_cloud_backblaze_enabled')) {
                    $table->boolean('backup_cloud_backblaze_enabled')->default(false);
                }
            });
        }
        
        // Create email_logs table if it doesn't exist
        if (!Schema::hasTable('email_logs')) {
            Schema::create('email_logs', function (Blueprint $table) {
                $table->id();
                $table->string('from');
                $table->string('to');
                $table->string('subject');
                $table->text('body');
                $table->string('status')->default('sent'); // sent, failed
                $table->text('error_message')->nullable();
                $table->timestamp('sent_at')->nullable();
                $table->timestamps();
            });
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Remove backup cloud settings from settings table
        if (Schema::hasTable('settings')) {
            Schema::table('settings', function (Blueprint $table) {
                $columns = [
                    'backup_schedule_enabled',
                    'backup_schedule_frequency',
                    'backup_schedule_time',
                    'backup_schedule_keep_days',
                    'backup_cloud_google_drive_enabled',
                    'backup_cloud_amazon_s3_enabled',
                    'backup_cloud_backblaze_enabled',
                ];
                
                foreach ($columns as $column) {
                    if (Schema::hasColumn('settings', $column)) {
                        $table->dropColumn($column);
                    }
                }
            });
        }
        
        // Drop email_logs table if it exists
        Schema::dropIfExists('email_logs');
    }
};
