<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddAdditionalFieldsToUsersTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('users', function (Blueprint $table) {
            // Data Diri
            $table->string('nik')->nullable(); // NIK
            $table->string('provinsi')->nullable(); // Provinsi
            $table->string('kabupaten_kota')->nullable(); // Kab/Kota
            $table->string('kecamatan')->nullable(); // Kecamatan
            $table->string('kelurahan')->nullable(); // Kelurahan
            $table->text('alamat_ktp')->nullable(); // Alamat Sesuai KTP
            $table->string('tempat_lahir')->nullable(); // Tempat Lahir
            $table->date('tanggal_lahir')->nullable(); // Tanggal Lahir
            $table->enum('jenis_kelamin', ['Laki-laki', 'Perempuan'])->nullable(); // Jenis <PERSON>
            $table->enum('status_perkawinan', ['Belum Menikah', 'Menikah', 'Cerai'])->nullable(); // Status Perkawinan
            $table->enum('agama', ['Islam', 'Kristen', 'Hindu', 'Buddha', 'Konghucu', 'Lainnya'])->nullable(); // Agama
            $table->enum('pendidikan_terakhir', ['SD', 'SMP', 'SMA', 'Diploma', 'Sarjana', 'Magister', 'Doktor'])->nullable(); // Pendidikan Terakhir

            // Autentikasi
            $table->string('no_hp')->nullable(); // No HP
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('users', function (Blueprint $table) {
            $table->dropColumn([
                'nik', 'provinsi', 'kabupaten_kota', 'kecamatan', 'kelurahan', 'alamat_ktp', 'tempat_lahir',
                'tanggal_lahir', 'jenis_kelamin', 'status_perkawinan', 'agama', 'pendidikan_terakhir', 'no_hp'
            ]);
        });
    }
}
