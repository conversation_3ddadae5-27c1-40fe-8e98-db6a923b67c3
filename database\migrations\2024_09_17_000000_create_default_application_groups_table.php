<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateDefaultApplicationGroupsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('default_application_groups', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->smallInteger('order')->default(0);
            $table->boolean('is_deleteable')->default(true);
            $table->timestamps();
        });

        // Insert default application groups
        DB::table('default_application_groups')->insert([
            [
                'name' => 'Se<PERSON><PERSON>',
                'order' => 1,
                'is_deleteable' => false,
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'name' => 'Interview',
                'order' => 2,
                'is_deleteable' => true,
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'name' => 'Diterima',
                'order' => 3,
                'is_deleteable' => true,
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'name' => '<PERSON><PERSON><PERSON>',
                'order' => 4,
                'is_deleteable' => true,
                'created_at' => now(),
                'updated_at' => now(),
            ],
        ]);
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('default_application_groups');
    }
}
