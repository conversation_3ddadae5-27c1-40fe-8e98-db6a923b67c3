<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddDefaultApplicationGroupIdToAppliedJobsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('applied_jobs', function (Blueprint $table) {
            $table->foreignId('default_application_group_id')->nullable()->after('application_group_id');
        });

        // Migrate existing data
        DB::statement("
            UPDATE applied_jobs aj
            JOIN application_groups ag ON aj.application_group_id = ag.id
            JOIN default_application_groups dag ON ag.name = dag.name
            SET aj.default_application_group_id = dag.id
        ");
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('applied_jobs', function (Blueprint $table) {
            $table->dropColumn('default_application_group_id');
        });
    }
}
