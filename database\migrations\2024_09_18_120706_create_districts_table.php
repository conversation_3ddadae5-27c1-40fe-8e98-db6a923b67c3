<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateDistrictsTable extends Migration
{
    public function up()
    {
        Schema::create('districts', function (Blueprint $table) {
            $table->id();
            $table->string('name'); // Nama district
            $table->double('long')->nullable(); // Longitude
            $table->double('lat')->nullable(); // Latitude
            $table->unsignedBigInteger('city_id');
            $table->timestamps();

            // Menambahkan foreign key city_id dari tabel cities
            $table->foreign('city_id')->references('id')->on('cities')->onDelete('cascade');
        });
    }

    public function down()
    {
        Schema::dropIfExists('districts');
    }
}
