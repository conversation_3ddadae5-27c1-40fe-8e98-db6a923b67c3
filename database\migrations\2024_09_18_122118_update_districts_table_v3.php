<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class UpdateDistrictsTableV3 extends Migration
{
    public function up()
    {
        // Schema::create('districts', function (Blueprint $table) {
        //     $table->id();
        //     $table->string('name'); // Nama kecamatan
        //     $table->foreignId('state_id')->constrained()->onDelete('cascade'); // Foreign key ke tabel states
        //     $table->timestamps();
        // });
    }

    public function down()
    {
        // Schema::dropIfExists('districts');
    }
};
