php artisan make:migration update_districts_table

// File migration:
<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class ModifyDistrictsTable extends Migration
{
    public function up()
    {
        Schema::table('districts', function (Blueprint $table) {
            // Tambahkan kolom 'lat' jika belum ada
            if (!Schema::hasColumn('districts', 'lat')) {
                $table->decimal('lat', 10, 8)->nullable();
            }

            $table->bigInteger('city_id')->unsigned()->nullable();
        });
    }

    public function down()
    {
        Schema::table('villages', function (Blueprint $table) {
            $table->dropForeign(['district_id']);
            $table->dropColumn('district_id');
        });
    }
}
