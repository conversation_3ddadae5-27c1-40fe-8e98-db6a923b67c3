<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class UpdateVillagesTable extends Migration
{
    public function up()
    {
        Schema::table('villages', function (Blueprint $table) {
            if (!Schema::hasColumn('villages', 'district_id')) {
                $table->unsignedBigInteger('district_id')->nullable()->after('id');
            }

            $table->foreign('district_id')->references('id')->on('districts')->onDelete('cascade');
        });
    }

    public function down()
    {
        Schema::table('villages', function (Blueprint $table) {
            // Mengembalikan perubahan
            $table->dropForeign(['district_id']);
            $table->dropColumn(['long', 'lat']);
        });
    }
}
