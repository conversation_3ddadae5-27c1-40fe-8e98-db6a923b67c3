<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddCityIdToDistrictsTable extends Migration
{
    public function up()
    {
        Schema::table('districts', function (Blueprint $table) {
            if (!Schema::hasColumn('districts', 'city_id')) { // Cek apakah kolom city_id sudah ada
                $table->unsignedBigInteger('city_id')->nullable(); // Menambahkan kolom city_id jika belum ada
                $table->foreign('city_id')->references('id')->on('cities'); // Menambahkan foreign key
            }
        });
    }

    public function down()
    {
        Schema::table('districts', function (Blueprint $table) {
            $table->dropForeign(['city_id']); // Hapus foreign key
            $table->dropColumn('city_id'); // Menghapus kolom city_id
        });
    }
}
