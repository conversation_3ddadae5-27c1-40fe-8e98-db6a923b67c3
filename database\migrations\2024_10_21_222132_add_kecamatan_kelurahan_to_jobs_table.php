<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up()
    {
        Schema::table('jobs', function (Blueprint $table) {
            $table->unsignedBigInteger('kecamatan_id')->nullable()->after('city_id');
            $table->unsignedBigInteger('kelurahan_id')->nullable()->after('kecamatan_id');

            // Jika kamu ingin ada foreign key:
            $table->foreign('kecamatan_id')->references('id')->on('kecamatan')->onDelete('set null');
            $table->foreign('kelurahan_id')->references('id')->on('kelurahan')->onDelete('set null');
        });
    }

    public function down()
    {
        Schema::table('jobs', function (Blueprint $table) {
            $table->dropForeign(['kecamatan_id']);
            $table->dropForeign(['kelurahan_id']);
            $table->dropColumn(['kecamatan_id', 'kelurahan_id']);
        });
    }
};
