<?php

use App\Models\Candidate;
use App\Models\Company;
use App\Models\Job;
use App\Models\User;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('message_threads', function (Blueprint $table) {
            $table->id();
            $table->foreignIdFor(Company::class)->nullable()->constrained()->cascadeOnDelete();
            $table->foreignIdFor(Candidate::class)->nullable()->constrained()->cascadeOnDelete();
            $table->foreignIdFor(Job::class)->nullable()->constrained()->cascadeOnDelete();
            $table->foreignIdFor(User::class, 'initiator_id')->nullable()->constrained('users')->cascadeOnDelete();
            $table->string('subject')->nullable();
            $table->boolean('is_admin_thread')->default(false);
            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('message_threads');
    }
};
