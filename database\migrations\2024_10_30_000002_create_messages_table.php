<?php

use App\Models\MessageThread;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('messages', function (Blueprint $table) {
            $table->id();
            $table->foreignIdFor(MessageThread::class)->constrained()->cascadeOnDelete();
            $table->unsignedBigInteger('sender_id')->comment('pengirim');
            $table->unsignedBigInteger('receiver_id')->nullable()->comment('penerima');
            $table->longText('body');
            $table->enum('type', ['umum', 'informasi'])->default('umum');
            $table->boolean('can_reply')->default(true);
            $table->boolean('read')->default(false);
            $table->json('attachment')->nullable();
            $table->timestamps();
            $table->softDeletes();

            $table->foreign('sender_id')->references('id')->on('users')->onDelete('cascade');
            $table->foreign('receiver_id')->references('id')->on('users')->onDelete('cascade');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('messages');
    }
};
