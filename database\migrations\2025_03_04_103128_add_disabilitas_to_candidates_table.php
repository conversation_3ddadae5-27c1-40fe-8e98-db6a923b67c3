<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    public function up(): void
    {
        Schema::table('candidates', function (Blueprint $table) {
            $table->boolean('disabilitas')->default(0)->after('last_name'); // 0 = Tidak, 1 = Ya
            $table->string('jenis_disabilitas')->nullable()->after('disabilitas');
        });
    }

    public function down(): void
    {
        Schema::table('candidates', function (Blueprint $table) {
            $table->dropColumn(['disabilitas', 'jenis_disabilitas']);
        });
    }
};
