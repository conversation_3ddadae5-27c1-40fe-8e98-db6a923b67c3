<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('jobs', function (Blueprint $table) {
            // Add indexes to frequently filtered columns
            $table->index('status');
            $table->index('category_id');
            $table->index('job_type_id');
            $table->index('education_id');
            $table->index('locality');
            $table->index('disability_friendly');
            $table->index('min_salary');
            $table->index('max_salary');
            $table->index('is_remote');
            $table->index('featured');
            $table->index('waiting_for_edit_approval');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('jobs', function (Blueprint $table) {
            // Drop indexes
            $table->dropIndex(['status']);
            $table->dropIndex(['category_id']);
            $table->dropIndex(['job_type_id']);
            $table->dropIndex(['education_id']);
            $table->dropIndex(['locality']);
            $table->dropIndex(['disability_friendly']);
            $table->dropIndex(['min_salary']);
            $table->dropIndex(['max_salary']);
            $table->dropIndex(['is_remote']);
            $table->dropIndex(['featured']);
            $table->dropIndex(['waiting_for_edit_approval']);
        });
    }
};
