<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('message_threads', function (Blueprint $table) {
            // Hapus foreign key terle<PERSON>h dahulu
            if (Schema::hasColumn('message_threads', 'closed_by')) {
                $table->dropForeign(['closed_by']);
            }

            // Hapus kolom-kolom tiket
            $table->dropColumn([
                'status',
                'priority',
                'category',
                'ticket_number',
                'closed_at',
                'closed_by'
            ]);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('message_threads', function (Blueprint $table) {
            // Tambahkan kembali kolom-kolom tiket jika perlu rollback
            $table->enum('status', ['open', 'pending', 'closed'])->default('open')->after('is_admin_thread');
            $table->enum('priority', ['low', 'medium', 'high', 'urgent'])->default('medium')->after('status');
            $table->enum('category', ['general', 'technical', 'billing', 'other'])->default('general')->after('priority');
            $table->string('ticket_number')->nullable()->after('category');
            $table->timestamp('closed_at')->nullable()->after('ticket_number');
            $table->unsignedBigInteger('closed_by')->nullable()->after('closed_at');

            // Tambahkan kembali foreign key
            $table->foreign('closed_by')->references('id')->on('users')->onDelete('set null');
        });
    }
};
