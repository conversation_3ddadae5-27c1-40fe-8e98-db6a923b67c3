<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Modify the status enum to include 'processing'
        DB::statement("ALTER TABLE flyers MODIFY COLUMN status ENUM('pending', 'active', 'rejected', 'revision', 'processing') DEFAULT 'pending'");
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Revert the status enum to the original values
        DB::statement("ALTER TABLE flyers MODIFY COLUMN status ENUM('pending', 'active', 'rejected', 'revision') DEFAULT 'pending'");
    }
};
