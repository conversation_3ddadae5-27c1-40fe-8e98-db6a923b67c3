<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('cms', function (Blueprint $table) {
            $table->longText('about_title')->nullable();
            $table->longText('about_subtitle')->nullable();
            $table->longText('about_description')->nullable();
            $table->longText('about_why_choose_title')->nullable();
            $table->longText('about_why_choose_subtitle')->nullable();
            $table->longText('about_mission_title')->nullable();
            $table->longText('about_mission_subtitle')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('cms', function (Blueprint $table) {
            $table->dropColumn([
                'about_title',
                'about_subtitle',
                'about_description',
                'about_why_choose_title',
                'about_why_choose_subtitle',
                'about_mission_title',
                'about_mission_subtitle'
            ]);
        });
    }
};
