<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Fetch the app name
        $appName = config('app.name');

        $email_templates = [
            [
                'name' => 'Pelamar Baru Diterima',
                'type' => 'candidate_accepted',
                'subject' => 'Selamat! Lamaran Anda Diterima',
                'message' => "<div style='box-sizing:border-box;font-family:Helvetica,Arial,sans-serif; font-size:16px; text-align:center; background-color:#edf2f7; color:#000; margin:0;padding:20px ;width:100%'><div style='color:#000; margin-bottom: 20px;'><strong>$appName</strong></div><div style='background:#fff; background-color:#fff; color:#718096; font-family:Helvetica,Arial,sans-serif; font-size:16px; text-align:left; max-width:600px; margin: 0 auto 20px; border: 1px solid #e5e4e6; border-radius: 10px; padding: 20px;'><p><strong>Halo {user_name},</strong></p><p>Selamat! Lamaran Anda untuk posisi {job_title} di {company_name} telah diterima.</p><p>Silakan periksa detail lebih lanjut di akun Anda atau hubungi perusahaan untuk informasi selanjutnya.</p><p>Salam,<br><strong>$appName</strong></p></div><small>© ".date('Y')." $appName. All rights reserved.</small></div>",
            ],
            [
                'name' => 'Pelamar Ditolak',
                'type' => 'candidate_rejected',
                'subject' => 'Update Status Lamaran Anda',
                'message' => "<div style='box-sizing:border-box;font-family:Helvetica,Arial,sans-serif; font-size:16px; text-align:center; background-color:#edf2f7; color:#000; margin:0;padding:20px ;width:100%'><div style='color:#000; margin-bottom: 20px;'><strong>$appName</strong></div><div style='background:#fff; background-color:#fff; color:#718096; font-family:Helvetica,Arial,sans-serif; font-size:16px; text-align:left; max-width:600px; margin: 0 auto 20px; border: 1px solid #e5e4e6; border-radius: 10px; padding: 20px;'><p><strong>Halo {user_name},</strong></p><p>Terima kasih telah melamar untuk posisi {job_title} di {company_name}.</p><p>Setelah meninjau lamaran Anda dengan seksama, kami ingin memberitahu bahwa kami telah memutuskan untuk melanjutkan dengan kandidat lain yang kualifikasinya lebih sesuai dengan kebutuhan kami saat ini.</p><p>Kami menghargai minat Anda dan mendorong Anda untuk tetap mencari peluang lain di platform kami.</p><p>Salam,<br><strong>$appName</strong></p></div><small>© ".date('Y')." $appName. All rights reserved.</small></div>",
            ],
            [
                'name' => 'Jadwal Interview',
                'type' => 'interview_invitation',
                'subject' => 'Undangan Interview untuk Posisi {job_title}',
                'message' => "<div style='box-sizing:border-box;font-family:Helvetica,Arial,sans-serif; font-size:16px; text-align:center; background-color:#edf2f7; color:#000; margin:0;padding:20px ;width:100%'><div style='color:#000; margin-bottom: 20px;'><strong>$appName</strong></div><div style='background:#fff; background-color:#fff; color:#718096; font-family:Helvetica,Arial,sans-serif; font-size:16px; text-align:left; max-width:600px; margin: 0 auto 20px; border: 1px solid #e5e4e6; border-radius: 10px; padding: 20px;'><p><strong>Halo {user_name},</strong></p><p>Kami senang memberitahu bahwa Anda telah terpilih untuk interview untuk posisi {job_title} di {company_name}.</p><p><strong>Detail Interview:</strong><br>Tanggal: {interview_date}<br>Waktu: {interview_time}<br>Lokasi: {interview_location}<br>Jenis Interview: {interview_type}</p><p>Silakan konfirmasi kehadiran Anda dengan membalas email ini atau melalui platform kami.</p><p>Salam,<br><strong>{company_name}</strong></p></div><small>© ".date('Y')." $appName. All rights reserved.</small></div>",
            ],
            [
                'name' => 'Reset Password',
                'type' => 'reset_password',
                'subject' => 'Reset Password Akun Anda',
                'message' => "<div style='box-sizing:border-box;font-family:Helvetica,Arial,sans-serif; font-size:16px; text-align:center; background-color:#edf2f7; color:#000; margin:0;padding:20px ;width:100%'><div style='color:#000; margin-bottom: 20px;'><strong>$appName</strong></div><div style='background:#fff; background-color:#fff; color:#718096; font-family:Helvetica,Arial,sans-serif; font-size:16px; text-align:left; max-width:600px; margin: 0 auto 20px; border: 1px solid #e5e4e6; border-radius: 10px; padding: 20px;'><p><strong>Halo {user_name},</strong></p><p>Kami menerima permintaan untuk reset password akun Anda.</p><p>Silakan klik tombol di bawah ini untuk reset password Anda:</p><p><a href='{reset_link}' style='display: inline-block; background-color: #3490dc; color: white; font-weight: bold; padding: 10px 15px; border-radius: 5px; text-decoration: none;'>Reset Password</a></p><p>Jika Anda tidak meminta reset password, abaikan email ini.</p><p>Link ini akan kedaluwarsa dalam 60 menit.</p><p>Salam,<br><strong>$appName</strong></p></div><small>© ".date('Y')." $appName. All rights reserved.</small></div>",
            ],
            [
                'name' => 'Verifikasi Email',
                'type' => 'email_verification',
                'subject' => 'Verifikasi Email Anda',
                'message' => "<div style='box-sizing:border-box;font-family:Helvetica,Arial,sans-serif; font-size:16px; text-align:center; background-color:#edf2f7; color:#000; margin:0;padding:20px ;width:100%'><div style='color:#000; margin-bottom: 20px;'><strong>$appName</strong></div><div style='background:#fff; background-color:#fff; color:#718096; font-family:Helvetica,Arial,sans-serif; font-size:16px; text-align:left; max-width:600px; margin: 0 auto 20px; border: 1px solid #e5e4e6; border-radius: 10px; padding: 20px;'><p><strong>Halo {user_name},</strong></p><p>Terima kasih telah mendaftar di $appName.</p><p>Silakan klik tombol di bawah ini untuk memverifikasi alamat email Anda:</p><p><a href='{verification_link}' style='display: inline-block; background-color: #3490dc; color: white; font-weight: bold; padding: 10px 15px; border-radius: 5px; text-decoration: none;'>Verifikasi Email</a></p><p>Jika Anda tidak membuat akun, abaikan email ini.</p><p>Salam,<br><strong>$appName</strong></p></div><small>© ".date('Y')." $appName. All rights reserved.</small></div>",
            ],
            [
                'name' => 'Lowongan Baru',
                'type' => 'new_job_notification',
                'subject' => 'Lowongan Baru yang Mungkin Sesuai untuk Anda',
                'message' => "<div style='box-sizing:border-box;font-family:Helvetica,Arial,sans-serif; font-size:16px; text-align:center; background-color:#edf2f7; color:#000; margin:0;padding:20px ;width:100%'><div style='color:#000; margin-bottom: 20px;'><strong>$appName</strong></div><div style='background:#fff; background-color:#fff; color:#718096; font-family:Helvetica,Arial,sans-serif; font-size:16px; text-align:left; max-width:600px; margin: 0 auto 20px; border: 1px solid #e5e4e6; border-radius: 10px; padding: 20px;'><p><strong>Halo {user_name},</strong></p><p>Kami menemukan lowongan baru yang mungkin sesuai dengan profil Anda:</p><p><strong>{job_title}</strong> di <strong>{company_name}</strong></p><p>Lokasi: {job_location}<br>Gaji: {job_salary}</p><p>Silakan kunjungi platform kami untuk melihat detail lengkap dan melamar.</p><p>Salam,<br><strong>$appName</strong></p></div><small>© ".date('Y')." $appName. All rights reserved.</small></div>",
            ],
            [
                'name' => 'Flyer Disetujui',
                'type' => 'flyer_approved',
                'subject' => 'Flyer Anda Telah Disetujui',
                'message' => "<div style='box-sizing:border-box;font-family:Helvetica,Arial,sans-serif; font-size:16px; text-align:center; background-color:#edf2f7; color:#000; margin:0;padding:20px ;width:100%'><div style='color:#000; margin-bottom: 20px;'><strong>$appName</strong></div><div style='background:#fff; background-color:#fff; color:#718096; font-family:Helvetica,Arial,sans-serif; font-size:16px; text-align:left; max-width:600px; margin: 0 auto 20px; border: 1px solid #e5e4e6; border-radius: 10px; padding: 20px;'><p><strong>Halo {user_name},</strong></p><p>Kami senang memberitahu bahwa flyer Anda untuk lowongan <strong>{job_title}</strong> telah disetujui dan sekarang aktif di platform kami.</p><p>Flyer Anda akan ditampilkan kepada pencari kerja yang potensial.</p><p>Salam,<br><strong>$appName</strong></p></div><small>© ".date('Y')." $appName. All rights reserved.</small></div>",
            ],
            [
                'name' => 'Flyer Ditolak',
                'type' => 'flyer_rejected',
                'subject' => 'Flyer Anda Memerlukan Revisi',
                'message' => "<div style='box-sizing:border-box;font-family:Helvetica,Arial,sans-serif; font-size:16px; text-align:center; background-color:#edf2f7; color:#000; margin:0;padding:20px ;width:100%'><div style='color:#000; margin-bottom: 20px;'><strong>$appName</strong></div><div style='background:#fff; background-color:#fff; color:#718096; font-family:Helvetica,Arial,sans-serif; font-size:16px; text-align:left; max-width:600px; margin: 0 auto 20px; border: 1px solid #e5e4e6; border-radius: 10px; padding: 20px;'><p><strong>Halo {user_name},</strong></p><p>Kami telah meninjau flyer Anda untuk lowongan <strong>{job_title}</strong> dan sayangnya tidak dapat menyetujuinya dalam bentuk saat ini.</p><p><strong>Alasan:</strong><br>{rejection_reason}</p><p>Silakan lakukan revisi sesuai dengan masukan di atas dan kirimkan kembali untuk ditinjau.</p><p>Salam,<br><strong>$appName</strong></p></div><small>© ".date('Y')." $appName. All rights reserved.</small></div>",
            ],
            [
                'name' => 'Pesan Baru',
                'type' => 'new_message',
                'subject' => 'Anda Memiliki Pesan Baru',
                'message' => "<div style='box-sizing:border-box;font-family:Helvetica,Arial,sans-serif; font-size:16px; text-align:center; background-color:#edf2f7; color:#000; margin:0;padding:20px ;width:100%'><div style='color:#000; margin-bottom: 20px;'><strong>$appName</strong></div><div style='background:#fff; background-color:#fff; color:#718096; font-family:Helvetica,Arial,sans-serif; font-size:16px; text-align:left; max-width:600px; margin: 0 auto 20px; border: 1px solid #e5e4e6; border-radius: 10px; padding: 20px;'><p><strong>Halo {user_name},</strong></p><p>Anda memiliki pesan baru dari <strong>{sender_name}</strong>.</p><p>Silakan login ke akun Anda untuk melihat dan membalas pesan ini.</p><p>Salam,<br><strong>$appName</strong></p></div><small>© ".date('Y')." $appName. All rights reserved.</small></div>",
            ],
        ];

        foreach ($email_templates as $template) {
            // Check if template already exists
            $exists = DB::table('email_templates')->where('type', $template['type'])->exists();

            if (!$exists) {
                DB::table('email_templates')->insert($template);
            }
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        //
    }
};
