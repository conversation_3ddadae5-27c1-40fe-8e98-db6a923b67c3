<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::dropIfExists('candidate_certifications');

        Schema::create('candidate_certifications', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('candidate_id');
            $table->string('name'); // Certificate name
            $table->string('organization'); // Issuing organization
            $table->date('issue_date'); // Issue date
            $table->date('expiration_date')->nullable(); // Expiration date (optional)
            $table->string('credential_id')->nullable(); // Credential ID (optional)
            $table->string('credential_url')->nullable(); // Credential URL (optional)
            $table->timestamps();

            $table->foreign('candidate_id')->references('id')->on('candidates')->onDelete('cascade');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('candidate_certifications');
    }
};
