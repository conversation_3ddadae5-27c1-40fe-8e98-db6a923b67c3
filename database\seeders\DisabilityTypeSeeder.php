<?php

namespace Database\Seeders;

use App\Models\DisabilityType;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class DisabilityTypeSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $disabilityTypes = [
            [
                'name' => 'Tuna Netra',
                'description' => 'Gangguan penglihatan atau kebutaan',
                'icon' => 'fas fa-eye-slash'
            ],
            [
                'name' => 'Tuna Rungu',
                'description' => 'Gangguan pendengaran atau ketulian',
                'icon' => 'fas fa-deaf'
            ],
            [
                'name' => 'Tuna <PERSON>ra',
                'description' => 'Gangguan berbicara atau bisu',
                'icon' => 'fas fa-comment-slash'
            ],
            [
                'name' => 'Tuna Daksa',
                'description' => 'Cacat fisik atau keterbatasan gerak',
                'icon' => 'fas fa-wheelchair'
            ],
            [
                'name' => '<PERSON>na Grahita',
                'description' => 'Keterbelakangan mental atau retardasi mental',
                'icon' => 'fas fa-brain'
            ],
            [
                'name' => 'Tu<PERSON>',
                'description' => 'Gangguan emosi dan perilaku',
                'icon' => 'fas fa-heart'
            ],
            [
                'name' => 'Autisme',
                'description' => 'Gangguan spektrum autisme',
                'icon' => 'fas fa-puzzle-piece'
            ],
            [
                'name' => 'Disabilitas Intelektual',
                'description' => 'Keterbatasan fungsi intelektual',
                'icon' => 'fas fa-lightbulb'
            ],
            [
                'name' => 'Disabilitas Fisik',
                'description' => 'Keterbatasan fungsi fisik',
                'icon' => 'fas fa-walking'
            ],
            [
                'name' => 'Disabilitas Sensorik',
                'description' => 'Keterbatasan fungsi sensorik',
                'icon' => 'fas fa-hand-sparkles'
            ],
            [
                'name' => 'Disabilitas Mental',
                'description' => 'Keterbatasan fungsi mental',
                'icon' => 'fas fa-brain'
            ],
            [
                'name' => 'Disabilitas Ganda',
                'description' => 'Kombinasi dari beberapa jenis disabilitas',
                'icon' => 'fas fa-users'
            ],
        ];

        foreach ($disabilityTypes as $type) {
            DisabilityType::create($type);
        }
    }
}
