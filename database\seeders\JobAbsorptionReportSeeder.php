<?php

namespace Database\Seeders;

use App\Models\Job;
use App\Models\User;
use App\Models\Company;
use App\Models\Candidate;
use App\Models\AppliedJob;
use App\Models\ApplicationGroup;
use App\Models\CandidateResume;
use Carbon\Carbon;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class JobAbsorptionReportSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        $this->command->info('Starting Job Absorption Report Seeder...');

        // Update all job deadlines to be 1-2 months after July 9, 2025
        $this->updateJobDeadlines();

        // Create realistic job applications for absorption reports
        $this->createJobApplications();

        // Update application statuses for better reporting
        $this->updateApplicationStatuses();

        $this->command->info('Job Absorption Report Seeder completed!');
    }

    /**
     * Update job deadlines to be 1-2 months after July 9, 2025
     */
    private function updateJobDeadlines()
    {
        $this->command->info('Updating job deadlines...');

        $baseDate = Carbon::create(2025, 7, 9); // July 9, 2025
        $jobs = Job::all();

        foreach ($jobs as $job) {
            // Random deadline between 1-2 months (30-60 days) after July 9, 2025
            $randomDays = rand(30, 60);
            $newDeadline = $baseDate->copy()->addDays($randomDays);

            $job->update([
                'deadline' => $newDeadline->format('Y-m-d'),
                'status' => 'active' // Make sure jobs are active
            ]);
        }

        $this->command->info("Updated {$jobs->count()} job deadlines");
    }

    /**
     * Create realistic job applications for absorption reports
     */
    private function createJobApplications()
    {
        $this->command->info('Creating job applications...');

        // Clear existing applied jobs to avoid duplicates
        DB::statement('SET FOREIGN_KEY_CHECKS=0;');
        DB::table('applied_jobs')->truncate();
        DB::statement('SET FOREIGN_KEY_CHECKS=1;');

        $candidates = Candidate::with('user')->get();
        $jobs = Job::with('company')->get();
        $applicationGroups = ApplicationGroup::all();
        $candidateResumes = CandidateResume::all();

        if ($candidates->isEmpty() || $jobs->isEmpty()) {
            $this->command->warn('No candidates or jobs found. Please run CandidateSeeder and JobSeeder first.');
            return;
        }

        if ($candidateResumes->isEmpty()) {
            $this->command->warn('No candidate resumes found. Creating default resumes...');
            $this->createDefaultResumes($candidates);
            $candidateResumes = CandidateResume::all();
        }

        $applicationData = [];
        $batchSize = 1000;
        $totalApplications = 0;

        // Create applications with realistic distribution
        foreach ($candidates as $candidate) {
            // Each candidate applies to 3-8 jobs randomly
            $jobsToApply = $jobs->random(rand(3, 8));

            foreach ($jobsToApply as $job) {
                // Random application date between January 2025 and July 2025
                $applicationDate = $this->getRandomApplicationDate();

                // Get random application group for this company
                $companyGroups = $applicationGroups->where('company_id', $job->company_id);
                $applicationGroup = $companyGroups->isNotEmpty()
                    ? $companyGroups->random()
                    : $applicationGroups->random();

                // Get random resume
                $resume = $candidateResumes->where('candidate_id', $candidate->id)->first()
                    ?? $candidateResumes->random();

                $applicationData[] = [
                    'candidate_id' => $candidate->id,
                    'job_id' => $job->id,
                    'cover_letter' => $this->generateCoverLetter(),
                    'candidate_resume_id' => $resume->id,
                    'application_group_id' => $applicationGroup->id,
                    'created_at' => $applicationDate,
                    'updated_at' => $applicationDate,
                ];

                $totalApplications++;

                // Insert in batches for better performance
                if (count($applicationData) >= $batchSize) {
                    DB::table('applied_jobs')->insert($applicationData);
                    $applicationData = [];
                    $this->command->info("Inserted {$totalApplications} applications...");
                }
            }
        }

        // Insert remaining data
        if (!empty($applicationData)) {
            DB::table('applied_jobs')->insert($applicationData);
        }

        $this->command->info("Created {$totalApplications} job applications");
    }

    /**
     * Generate random application date between January 2025 and July 2025
     */
    private function getRandomApplicationDate()
    {
        $startDate = Carbon::create(2025, 1, 1);
        $endDate = Carbon::create(2025, 7, 9);

        $randomTimestamp = rand($startDate->timestamp, $endDate->timestamp);
        return Carbon::createFromTimestamp($randomTimestamp);
    }

    /**
     * Update application statuses for better reporting
     */
    private function updateApplicationStatuses()
    {
        $this->command->info('Updating application statuses...');

        $applications = AppliedJob::all();
        $applicationGroups = ApplicationGroup::all();

        foreach ($applications as $application) {
            $companyGroups = $applicationGroups->where('company_id', $application->job->company_id);

            // Realistic distribution: 60% still in review, 20% interview, 15% rejected, 5% accepted
            $rand = rand(1, 100);

            if ($rand <= 60) {
                // Keep in "Semua Lamaran" (under review)
                $group = $companyGroups->where('name', 'Semua Lamaran')->first();
            } elseif ($rand <= 80) {
                // Move to interview
                $group = $companyGroups->where('name', 'Interview')->first();
            } elseif ($rand <= 95) {
                // Rejected
                $group = $companyGroups->where('name', 'Ditolak')->first();
            } else {
                // Accepted
                $group = $companyGroups->where('name', 'Diterima')->first();
            }

            if ($group) {
                $application->update(['application_group_id' => $group->id]);
            }
        }

        $this->command->info("Updated {$applications->count()} application statuses");
    }

    /**
     * Create default resumes for candidates who don't have any
     */
    private function createDefaultResumes($candidates)
    {
        foreach ($candidates as $candidate) {
            CandidateResume::create([
                'candidate_id' => $candidate->id,
                'name' => 'CV ' . $candidate->user->name,
                'file' => 'default-cv.pdf',
                'created_at' => now(),
                'updated_at' => now(),
            ]);
        }
    }

    /**
     * Generate simple cover letter
     */
    private function generateCoverLetter()
    {
        $coverLetters = [
            'Saya tertarik untuk bergabung dengan perusahaan Anda dan yakin dapat memberikan kontribusi yang baik.',
            'Dengan pengalaman dan keahlian yang saya miliki, saya siap untuk mengambil tantangan di posisi ini.',
            'Saya sangat antusias untuk dapat berkontribusi dalam tim dan mengembangkan karir di perusahaan Anda.',
            'Latar belakang pendidikan dan pengalaman kerja saya sesuai dengan kualifikasi yang dibutuhkan.',
            'Saya memiliki motivasi tinggi dan siap untuk belajar hal-hal baru dalam lingkungan kerja yang dinamis.',
        ];

        return $coverLetters[array_rand($coverLetters)];
    }
}
