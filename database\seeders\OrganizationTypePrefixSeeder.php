<?php

namespace Database\Seeders;

use App\Models\OrganizationType;
use App\Models\OrganizationTypeTranslation;
use Illuminate\Database\Seeder;

class OrganizationTypePrefixSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        // Data prefix untuk berbagai jenis organisasi
        $organizationPrefixes = [
            'PT' => ['PT. ', 'PT. '], // Perseroan Terbatas
            'CV' => ['CV. ', 'CV. '], // Commanditaire Vennootschap
            'UD' => ['UD. ', 'UD. '], // Usaha Dagang
            'PD' => ['PD. ', 'PD. '], // <PERSON><PERSON>haan <PERSON>
            'Koperasi' => ['<PERSON><PERSON><PERSON> ', '<PERSON>perasi '],
            'Yayasan' => ['Yayasan ', 'Yayasan '],
            'Firma' => ['Firma ', 'Firma '],
            'Persero' => ['PT. ', 'PT. '], // BUMN
            'Perum' => ['Perum ', 'Perum '], // <PERSON><PERSON><PERSON><PERSON>
            'Perjan' => ['Perjan ', 'Perjan '], // <PERSON><PERSON>an <PERSON>
        ];

        // Buat organization types baru dengan prefix
        $languages = loadLanguage();
        
        foreach ($organizationPrefixes as $name => $prefixes) {
            // Cek apakah organization type sudah ada
            $existingType = OrganizationTypeTranslation::where('name', $name)
                ->where('locale', 'id')
                ->first();
                
            if (!$existingType) {
                // Buat organization type baru
                $organizationType = new OrganizationType();
                $organizationType->save();

                foreach ($languages as $language) {
                    $prefix = $language->code === 'id' ? $prefixes[0] : $prefixes[1];
                    
                    OrganizationTypeTranslation::create([
                        'organization_type_id' => $organizationType->id,
                        'locale' => $language->code,
                        'name' => $name,
                        'prefix' => $prefix,
                    ]);
                }
            } else {
                // Update prefix untuk organization type yang sudah ada
                $organizationType = OrganizationType::find($existingType->organization_type_id);
                
                foreach ($languages as $language) {
                    $prefix = $language->code === 'id' ? $prefixes[0] : $prefixes[1];
                    
                    $translation = OrganizationTypeTranslation::where('organization_type_id', $organizationType->id)
                        ->where('locale', $language->code)
                        ->first();
                        
                    if ($translation) {
                        $translation->update(['prefix' => $prefix]);
                    }
                }
            }
        }

        // Update organization types yang sudah ada tanpa prefix
        $existingTypes = OrganizationType::all();
        foreach ($existingTypes as $type) {
            foreach ($languages as $language) {
                $translation = OrganizationTypeTranslation::where('organization_type_id', $type->id)
                    ->where('locale', $language->code)
                    ->whereNull('prefix')
                    ->first();
                    
                if ($translation) {
                    // Set default prefix berdasarkan nama
                    $defaultPrefix = '';
                    $name = strtolower($translation->name);
                    
                    if (str_contains($name, 'private') || str_contains($name, 'swasta')) {
                        $defaultPrefix = 'PT. ';
                    } elseif (str_contains($name, 'government') || str_contains($name, 'pemerintah')) {
                        $defaultPrefix = '';
                    } elseif (str_contains($name, 'ngo')) {
                        $defaultPrefix = 'Yayasan ';
                    }
                    
                    $translation->update(['prefix' => $defaultPrefix]);
                }
            }
        }
    }
}
