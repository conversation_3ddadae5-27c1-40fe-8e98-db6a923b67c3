<?php

namespace Database\Seeders;

use App\Models\Setting;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Schema;

class UpdateRecaptchaSettingsSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        // Add columns if they don't exist
        if (!Schema::hasColumn('settings', 'recaptcha_sitekey')) {
            Schema::table('settings', function ($table) {
                $table->string('recaptcha_sitekey')->nullable();
                $table->string('recaptcha_secret')->nullable();
                $table->boolean('recaptcha_active')->default(false);
            });
        }

        // Update existing setting record with default values
        $setting = Setting::first();
        if ($setting) {
            $setting->update([
                'recaptcha_sitekey' => $setting->recaptcha_sitekey ?? env('NOCAPTCHA_SITEKEY'),
                'recaptcha_secret' => $setting->recaptcha_secret ?? env('NOCAPTCHA_SECRET'),
                'recaptcha_active' => $setting->recaptcha_active ?? (env('NOCAPTCHA_ACTIVE', false) ? true : false),
            ]);
        }
    }
}
