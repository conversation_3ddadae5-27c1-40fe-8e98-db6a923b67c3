<?php

namespace Database\Seeders;

use App\Models\Admin;
use App\Models\Candidate;
use App\Models\Company;
use App\Models\ContactInfo;
use App\Models\Education;
use App\Models\Experience;
use App\Models\IndustryType;
use App\Models\JobRole;
use App\Models\OrganizationType;
use App\Models\Profession;
use App\Models\SocialLink;
use App\Models\TeamSize;
use App\Models\User;
use Carbon\Carbon;
use Illuminate\Database\Seeder;
use Illuminate\Support\Arr;
use Illuminate\Support\Str;
use Spatie\Permission\Models\Role;

class UserSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        // Candidate
        $candidate = new User();
        $candidate->name = 'Candidate';
        $candidate->username = 'candidate';
        $candidate->email = '<EMAIL>';
        $candidate->password = bcrypt('password');
        $candidate->role = 'candidate';
        $candidate->email_verified_at = Carbon::now();
        $candidate->save();
        Candidate::create([
            'user_id' => $candidate->id,
            'role_id' => JobRole::inRandomOrder()->value('id'),
            'profession_id' => Profession::inRandomOrder()->value('id'),
            'experience_id' => Experience::inRandomOrder()->value('id'),
            'education_id' => Education::inRandomOrder()->value('id'),
            'gender' => Arr::random(['male', 'female', 'other']),
            'website' => 'https://templatecookie.com',
            'title' => 'This is Candidate Title !',
            'birth_date' => Carbon::now(),
            'marital_status' => 'married',
            'photo' => 'https://upload.wikimedia.org/wikipedia/commons/thumb/9/9a/Gull_portrait_ca_usa.jpg/800px-Gull_portrait_ca_usa.jpg?20101128165003',
            'bio' => 'Sometimes you may wish to stop running validation rules on an attribute after the first validation  failure. To do so, assign the bail rule to the attribute:',
        ]);
        SocialLink::create([
            'user_id' => $candidate->id,
            'google' => 'https://www.google.com/search?q=zakirsoft',
            'facebook' => 'https://www.facebook.com/zakirsoft',
            'twitter' => 'https://www.twitter.com/zakirsoft',
            'instagram' => 'https://www.instagram.com/zakirsoft',
            'linkedin' => 'https://www.linkedin.com/zakirsoft',
            'youtube' => 'https://www.youtube.com/zakirsoft',
        ]);
        ContactInfo::create([
            'user_id' => $candidate->id,
            'phone' => '+880123456789',
            'email' => '<EMAIL>',
        ]);

        // Company
        $company = new User();
        $company->name = 'Zakirsoft';
        $company->username = 'company';
        $company->email = '<EMAIL>';
        $company->password = bcrypt('password');
        $company->role = 'company';
        $company->email_verified_at = Carbon::now();
        $company->save();
        Company::create([
            'user_id' => $company->id,
            'industry_type_id' => IndustryType::inRandomOrder()->value('id'),
            'organization_type_id' => OrganizationType::inRandomOrder()->value('id'),
            'team_size_id' => TeamSize::inRandomOrder()->value('id'),
            'bio' => 'This is bio',
            'profile_completion' => 1,
        ]);
        SocialLink::create([
            'user_id' => $company->id,
            'google' => 'https://www.google.com/search?q=zakirsoft',
            'facebook' => 'https://www.facebook.com/zakirsoft',
            'twitter' => 'https://www.twitter.com/zakirsoft',
            'instagram' => 'https://www.instagram.com/zakirsoft',
            'linkedin' => 'https://www.linkedin.com/zakirsoft',
            'youtube' => 'https://www.youtube.com/zakirsoft',
        ]);
        ContactInfo::create([
            'user_id' => $company->id,
            'phone' => '+880123456789',
            'email' => '<EMAIL>',
        ]);

        // Admin
        $role = Role::first();
        $admin = new Admin();
        $admin->name = 'Zakir Soft';
        $admin->email = '<EMAIL>';
        $admin->image = 'backend/image/default.png';
        $admin->password = bcrypt('password');
        $admin->email_verified_at = Carbon::now();
        $admin->remember_token = Str::random(10);
        $admin->save();
        $admin->assignRole($role);

        $admin = new Admin();
        $admin->name = 'Admin';
        $admin->email = '<EMAIL>';
        $admin->image = 'backend/image/default.png';
        $admin->password = bcrypt('password');
        $admin->email_verified_at = Carbon::now();
        $admin->remember_token = Str::random(10);
        $admin->save();
        $admin->assignRole($role);
    }
}
