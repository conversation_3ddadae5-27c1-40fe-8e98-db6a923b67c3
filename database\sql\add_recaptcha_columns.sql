-- Add reCAPTCHA columns to settings table
-- Run this SQL manually if migration fails

-- Check if columns exist before adding them
SET @exist := (SELECT count(*) FROM information_schema.COLUMNS WHERE TABLE_SCHEMA=DATABASE() AND COLUMN_NAME='recaptcha_sitekey' AND TABLE_NAME='settings');
SET @sqlstmt := IF(@exist=0,'ALTER TABLE settings ADD COLUMN recaptcha_sitekey VARCHAR(255) NULL AFTER language_changing','SELECT ''Column recaptcha_sitekey already exists.'' as msg');
PREPARE stmt FROM @sqlstmt;
EXECUTE stmt;

SET @exist := (SELECT count(*) FROM information_schema.COLUMNS WHERE TABLE_SCHEMA=DATABASE() AND COLUMN_NAME='recaptcha_secret' AND TABLE_NAME='settings');
SET @sqlstmt := IF(@exist=0,'ALTER TABLE settings ADD COLUMN recaptcha_secret VARCHAR(255) NULL AFTER recaptcha_sitekey','SELECT ''Column recaptcha_secret already exists.'' as msg');
PREPARE stmt FROM @sqlstmt;
EXECUTE stmt;

SET @exist := (SELECT count(*) FROM information_schema.COLUMNS WHERE TABLE_SCHEMA=DATABASE() AND COLUMN_NAME='recaptcha_active' AND TABLE_NAME='settings');
SET @sqlstmt := IF(@exist=0,'ALTER TABLE settings ADD COLUMN recaptcha_active TINYINT(1) NOT NULL DEFAULT 0 AFTER recaptcha_secret','SELECT ''Column recaptcha_active already exists.'' as msg');
PREPARE stmt FROM @sqlstmt;
EXECUTE stmt;

-- Update existing settings with current env values if they exist
UPDATE settings SET 
    recaptcha_sitekey = COALESCE(recaptcha_sitekey, '6LcUf4wbAAAAAN5VNpsr7ucNVHiywFqZdHqrPoMi'),
    recaptcha_secret = COALESCE(recaptcha_secret, '6LcUf4wbAAAAAPZNsTFNw0L7cxBjyEbT239elyYA'),
    recaptcha_active = COALESCE(recaptcha_active, 0)
WHERE id = 1;
