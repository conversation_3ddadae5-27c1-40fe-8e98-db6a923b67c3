<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Under Construction</title>
  <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@400;600&display=swap" rel="stylesheet">
  <!-- Font Awesome CDN -->
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
  <style>
    body {
      margin: 0;
      padding: 0;
      font-family: 'Poppins', sans-serif;
      background-color: #333333;
      color: #FFFFFF;
      display: flex;
      justify-content: center;
      align-items: center;
      height: 100vh;
      text-align: center;
    }

    .container {
      background-color: #009773;
      border-radius: 10px;
      padding: 40px;
      box-shadow: 0px 8px 16px rgba(0, 0, 0, 0.3);
      max-width: 500px;
    }

    h1 {
      font-size: 48px;
      margin-bottom: 20px;
    }

    p {
      font-size: 18px;
      margin-bottom: 30px;
    }

    .progress-bar {
      background-color: #FFFFFF;
      border-radius: 25px;
      height: 20px;
      width: 100%;
      overflow: hidden;
      margin-bottom: 30px;
      position: relative;
    }

    .progress-bar-fill {
      background-color: #333333;
      height: 100%;
      width: 0;
      border-radius: 25px;
      position: absolute;
      top: 0;
      left: 0;
      transition: width 2s ease-in-out;
    }

    .social-icons {
      display: flex;
      justify-content: center;
      gap: 15px;
    }

    .social-icons a {
      color: #FFFFFF;
      font-size: 24px;
      text-decoration: none;
      transition: color 0.3s ease;
    }

    .social-icons a:hover {
      color: #333333;
    }
  </style>
</head>
<body>
  <div class="container">
    <h1>Server Migration</h1>
    <p>We're now migrating our server, and now it's in progress, we'll back shortly. Stay tuned!</p>
    <div class="progress-bar">
      <div class="progress-bar-fill" id="progress-bar-fill"></div>
    </div>
    <div class="social-icons">
      <a href="#" target="_blank"><i class="fab fa-facebook-f"></i></a>
      <a href="#" target="_blank"><i class="fab fa-twitter"></i></a>
      <a href="#" target="_blank"><i class="fab fa-instagram"></i></a>
    </div>
  </div>

  <script>
    // Animasi Progress Bar
    window.onload = function() {
      const progressBar = document.getElementById('progress-bar-fill');
      progressBar.style.width = '70%'; // Atur lebar progress bar
    };
  </script>
</body>
</html>
