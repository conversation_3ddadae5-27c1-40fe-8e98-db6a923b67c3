/* Mobile Bottom Navigation */
.mobile-bottom-nav {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    z-index: 9999;
    background-color: #ffffff;
    display: none;
    justify-content: space-around;
    align-items: center;
    box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.1);
    height: 65px;
    padding-bottom: env(safe-area-inset-bottom);
    width: 100%;
}

.mobile-bottom-nav__item {
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    min-width: 0;
    overflow: hidden;
    white-space: nowrap;
    position: relative;
}

.mobile-bottom-nav__item-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    text-decoration: none;
    color: #767F8C;
    font-size: 10px;
    padding: 5px 0;
    width: 100%;
    height: 100%;
}

.mobile-bottom-nav__item-content i {
    font-size: 22px;
    margin-bottom: 2px;
}

.mobile-bottom-nav__item.active .mobile-bottom-nav__item-content {
    color: #138C79;
}

.mobile-bottom-nav__item--center {
    margin-top: -30px;
}

.mobile-bottom-nav__item-center-button {
    width: 56px;
    height: 56px;
    border-radius: 50%;
    background-color: #138C79;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 4px 10px rgba(19, 140, 121, 0.3);
    margin-bottom: 5px;
}

.mobile-bottom-nav__item-center-button i {
    font-size: 24px;
    color: #ffffff;
    margin: 0;
}

.mobile-bottom-nav__item--center .mobile-bottom-nav__item-content {
    color: #138C79;
}

.notification-badge {
    position: absolute;
    top: -2px;
    right: -2px;
    background-color: #FF4D4F;
    color: white;
    border-radius: 50%;
    font-size: 8px;
    min-width: 14px;
    height: 14px;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0 3px;
}

/* Show bottom nav only on mobile */
@media (max-width: 991px) {
    .mobile-bottom-nav {
        display: flex !important;
    }

    body {
        padding-bottom: 65px !important;
    }

    /* Hide the default footer on mobile */
    .footer-area {
        padding-bottom: 80px !important;
    }
}
