<template>
    <transition name="fade">
        <div v-if="show"
            class="tw-fixed tw-top-0 tw-left-0 tw-right-0 tw-z-50 tw-p-4 tw-overflow-y-auto md:tw-inset-0 h-modal md:tw-h-full tw-flex tw-items-center tw-justify-center tw-bg-gray-800 tw-bg-opacity-90 modal fade show"
            id="candidate-profile-modal" style="display: block;">
            <div class="modal-dialog  modal-wrapper">
                <div class="modal-content">
                    <div class="modal-body">
                        <div class="row mb-5">
                            <div class="col-md-8">
                                <div class="candidate-profile mb-4 mb-md-0">
                                    <div class="candidate-profile-img">
                                        <img v-if="candidate.photo" :src="candidate.photo" :alt="'Foto ' + data.name">
                                    </div>
                                    <div class="candidate-profile-info">
                                        <img v-if="candidate.disabilitas == 1" class="disabilitas-icon" src="/backend/image/ikon-disabilitas.png" alt="Disabilitas">
                                        <h2 class="name">{{ data.name }}</h2>
                                        <h4 class="designation">
                                            {{ candidate.profession ? candidate.profession.name : '-' }}</h4>
                                        <h6 class="availablity d-none">{{ __('i_am_available') }}</h6>
                                    </div>
                                </div>
                            </div>

                            <div class="col-md-4" v-if="messagebutton">
                                <div class="tw-flex tw-items-center tw-justify-end">
                                    <button @click="messageCandidate()"
                                        class="btn hover:!tw-border-black tw-inline-flex tw-gap-2 tw-py-2.5 tw-px-4 !tw-border-[2px] !tw-border-[#0066CC] tw-items-center tw-text-base tw-text-[#0066CC]">
                                        <svg width="24" height="24" viewBox="0 0 20 20" fill="none"
                                            xmlns="http://www.w3.org/2000/svg">
                                            <path
                                                d="M5.83333 7.08333H10M5.83333 10H12.5M8.06979 15H13.5C14.9001 15 15.6002 15 16.135 14.7275C16.6054 14.4878 16.9878 14.1054 17.2275 13.635C17.5 13.1002 17.5 12.4001 17.5 11V6.5C17.5 5.09987 17.5 4.3998 17.2275 3.86502C16.9878 3.39462 16.6054 3.01217 16.135 2.77248C15.6002 2.5 14.9001 2.5 13.5 2.5H6.5C5.09987 2.5 4.3998 2.5 3.86502 2.77248C3.39462 3.01217 3.01217 3.39462 2.77248 3.86502C2.5 4.3998 2.5 5.09987 2.5 6.5V16.9463C2.5 17.3903 2.5 17.6123 2.59102 17.7263C2.67019 17.8255 2.79022 17.8832 2.91712 17.8831C3.06302 17.8829 3.23639 17.7442 3.58313 17.4668L5.57101 15.8765C5.9771 15.5517 6.18014 15.3892 6.40624 15.2737C6.60683 15.1712 6.82036 15.0963 7.04101 15.051C7.28972 15 7.54975 15 8.06979 15Z"
                                                stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                                stroke-linejoin="round" />
                                        </svg>
                                        <span>{{ __('message') }}</span>
                                    </button>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-lg-8">
                                <div class="biography-wrap">
                                    <input id="candidate_id" type="hidden" value="">
                                    <div
                                        v-if="social.facebook || social.twitter || social.linkedin || social.youtube || social.instagram">
                                        <div class="devider"></div>
                                        <div class="social-links">
                                            <h2 class="title">{{ __('follow_me_social_media') }}</h2>
                                            {{ social }}
                                            <div class="social-media">
                                                <ul>
                                                    <li v-if="social.facebook">
                                                        <a :href="candidate.social_info"
                                                            class="bg-primary-50 text-primary-500 plain-button icon-56 hover:bg-primary-500 hover:text-primary-50">

                                                            <svg width="20" height="20" viewBox="0 0 21 20" fill="none"
                                                                xmlns="http://www.w3.org/2000/svg">
                                                                <path
                                                                    d="M12.1666 20H8.20126V10.1414H5.5V6.9316H8.20116V4.64762C8.20116 1.9411 9.39588 0 13.3505 0C14.1869 0 15.5 0.168134 15.5 0.168134V3.14858H14.1208C12.7155 3.14858 12.1668 3.5749 12.1668 4.75352V6.9316H15.4474L15.1553 10.1414H12.1667L12.1666 20Z"
                                                                    fill="currentColor" />
                                                            </svg>
                                                        </a>
                                                    </li>
                                                    <li v-if="social.twitter">
                                                        <a :href="social.twitter"
                                                            class="bg-primary-50 text-primary-500 plain-button icon-56 hover:bg-primary-500 hover:text-primary-50">

                                                            <svg xmlns="http://www.w3.org/2000/svg"
                                                                xmlns:xlink="http://www.w3.org/1999/xlink"
                                                                aria-hidden="true" role="img" iconify--bx width="24"
                                                                height="24" preserveAspectRatio="xMidYMid meet"
                                                                viewBox="0 0 24 24" data-icon="bx:bxl-twitter">
                                                                <path
                                                                    d="M19.633 7.997c.013.175.013.349.013.523c0 5.325-4.053 11.461-11.46 11.461c-2.282 0-4.402-.661-6.186-1.809c.324.037.636.05.973.05a8.07 8.07 0 0 0 5.001-1.721a4.036 4.036 0 0 1-3.767-2.793c.249.037.499.062.761.062c.361 0 .724-.05 1.061-.137a4.027 4.027 0 0 1-3.23-3.953v-.05c.537.299 1.16.486 1.82.511a4.022 4.022 0 0 1-1.796-3.354c0-.748.199-1.434.548-2.032a11.457 11.457 0 0 0 8.306 4.215c-.062-.3-.1-.611-.1-.923a4.026 4.026 0 0 1 4.028-4.028c1.16 0 2.207.486 2.943 1.272a7.957 7.957 0 0 0 2.556-.973a4.02 4.02 0 0 1-1.771 2.22a8.073 8.073 0 0 0 2.319-.624a8.645 8.645 0 0 1-2.019 2.083z"
                                                                    fill="currentColor"></path>
                                                            </svg>
                                                        </a>
                                                    </li>
                                                    <li v-if="social.linkedin">
                                                        <a :href="social.linkedin"
                                                            class="bg-primary-50 text-primary-500 plain-button icon-56 hover:bg-primary-500 hover:text-primary-50">
                                                            <svg width="20" height="20" viewBox="0 0 20 20" fill="none"
                                                                xmlns="http://www.w3.org/2000/svg">
                                                                <path
                                                                    d="M0 2.66051C0 2.01699 0.225232 1.48611 0.675676 1.06784C1.12612 0.649561 1.71172 0.44043 2.43243 0.44043C3.14029 0.44043 3.71299 0.646337 4.15058 1.05819C4.60102 1.4829 4.82625 2.0363 4.82625 2.71842C4.82625 3.33618 4.60747 3.85097 4.16988 4.26282C3.71944 4.68753 3.12741 4.89989 2.39382 4.89989H2.37452C1.66666 4.89989 1.09396 4.68753 0.656371 4.26282C0.218784 3.83811 0 3.304 0 2.66051ZM0.250965 19.5524V6.65664H4.53668V19.5524H0.250965ZM6.9112 19.5524H11.1969V12.3516C11.1969 11.9012 11.2484 11.5537 11.3514 11.3092C11.5315 10.8716 11.805 10.5015 12.1718 10.1991C12.5386 9.89666 12.9987 9.74545 13.5521 9.74545C14.9936 9.74545 15.7143 10.7171 15.7143 12.6605V19.5524H20V12.1586C20 10.2538 19.5496 8.80915 18.6486 7.8246C17.7477 6.84004 16.5573 6.34776 15.0772 6.34776C13.417 6.34776 12.1236 7.06205 11.1969 8.49062V8.52923H11.1776L11.1969 8.49062V6.65664H6.9112C6.93693 7.06848 6.94981 8.34904 6.94981 10.4983C6.94981 12.6476 6.93693 15.6656 6.9112 19.5524Z"
                                                                    fill="var(--primary-500)" />
                                                            </svg>

                                                        </a>
                                                    </li>
                                                    <li v-if="social.youtube">
                                                        <a :href="social.youtube"
                                                            class="bg-primary-50 text-primary-500 plain-button icon-56 hover:bg-primary-500 hover:text-primary-50">

                                                            <svg xmlns="http://www.w3.org/2000/svg"
                                                                xmlns:xlink="http://www.w3.org/1999/xlink"
                                                                aria-hidden="true" role="img" width="1em" height="1em"
                                                                preserveAspectRatio="xMidYMid meet" viewBox="0 0 24 24"
                                                                data-icon="bx:bxl-instagram">
                                                                <path
                                                                    d="M11.999 7.377a4.623 4.623 0 1 0 0 9.248a4.623 4.623 0 0 0 0-9.248zm0 7.627a3.004 3.004 0 1 1 0-6.008a3.004 3.004 0 0 1 0 6.008z"
                                                                    fill="currentColor"></path>
                                                                <circle cx="16.806" cy="7.207" r="1.078"
                                                                    fill="currentColor"></circle>
                                                                <path
                                                                    d="M20.533 6.111A4.605 4.605 0 0 0 17.9 3.479a6.606 6.606 0 0 0-2.186-.42c-.963-.042-1.268-.054-3.71-.054s-2.755 0-3.71.054a6.554 6.554 0 0 0-2.184.42a4.6 4.6 0 0 0-2.633 2.632a6.585 6.585 0 0 0-.419 2.186c-.043.962-.056 1.267-.056 3.71c0 2.442 0 2.753.056 3.71c.015.748.156 1.486.419 2.187a4.61 4.61 0 0 0 2.634 2.632a6.584 6.584 0 0 0 2.185.45c.963.042 1.268.055 3.71.055s2.755 0 3.71-.055a6.615 6.615 0 0 0 2.186-.419a4.613 4.613 0 0 0 2.633-2.633c.263-.7.404-1.438.419-2.186c.043-.962.056-1.267.056-3.71s0-2.753-.056-3.71a6.581 6.581 0 0 0-.421-2.217zm-1.218 9.532a5.043 5.043 0 0 1-.311 1.688a2.987 2.987 0 0 1-1.712 1.711a4.985 4.985 0 0 1-1.67.311c-.95.044-1.218.055-3.654.055c-2.438 0-2.687 0-3.655-.055a4.96 4.96 0 0 1-1.669-.311a2.985 2.985 0 0 1-1.719-1.711a5.08 5.08 0 0 1-.311-1.669c-.043-.95-.053-1.218-.053-3.654c0-2.437 0-2.686.053-3.655a5.038 5.038 0 0 1 .311-1.687c.305-.789.93-1.41 1.719-1.712a5.01 5.01 0 0 1 1.669-.311c.951-.043 1.218-.055 3.655-.055s2.687 0 3.654.055a4.96 4.96 0 0 1 1.67.311a2.991 2.991 0 0 1 1.712 1.712a5.08 5.08 0 0 1 .311 1.669c.043.951.054 1.218.054 3.655c0 2.436 0 2.698-.043 3.654h-.011z"
                                                                    fill="currentColor"></path>
                                                            </svg>
                                                        </a>
                                                    </li>
                                                    <li v-if="social.instagram">
                                                        <a :href="social.instagram"
                                                            class="bg-primary-50 text-primary-500 plain-button icon-56 hover:bg-primary-500 hover:text-primary-50">
                                                            <svg xmlns="http://www.w3.org/2000/svg"
                                                                xmlns:xlink="http://www.w3.org/1999/xlink"
                                                                aria-hidden="true" role="img" iconify--bx width="1em"
                                                                height="1em" preserveAspectRatio="xMidYMid meet"
                                                                viewBox="0 0 24 24" data-icon="bx:bxl-youtube">
                                                                responsibilities   <path
                                                                    d="M21.593 7.203a2.506 2.506 0 0 0-1.762-1.766C18.265 5.007 12 5 12 5s-6.264-.007-7.831.404a2.56 2.56 0 0 0-1.766 1.778c-.413 1.566-.417 4.814-.417 4.814s-.004 3.264.406 4.814c.23.857.905 1.534 1.763 1.765c1.582.43 7.83.437 7.83.437s6.265.007 7.831-.403a2.515 2.515 0 0 0 1.767-1.763c.414-1.565.417-4.812.417-4.812s.02-3.265-.407-4.831zM9.996 15.005l.005-6l5.207 3.005l-5.212 2.995z"
                                                                    fill="currentColor"></path>
                                                            </svg>
                                                        </a>
                                                    </li>
                                                </ul>

                                            </div>
                                        </div>
                                    </div>
                                    <div>
                                        <div class="row">
                                            <div class="col-lg-12">
                                                <ul class="nav nav-tabs tw-gap-x-6 !tw-border-b !tw-border-[#E3E5E6]"
                                                    id="myTab" role="tablist">
                                                    <li class="nav-item" role="presentation">
                                                        <button class="ll-nav-link nav-link active" id="home-tab"
                                                            data-bs-toggle="tab" data-bs-target="#home" type="button"
                                                            role="tab" aria-controls="home" aria-selected="true">{{
                                                            __('cover_letter') }}</button>
                                                    </li>
                                                    <li class="nav-item" role="presentation">
                                                        <button class="ll-nav-link nav-link" id="profile-tab"
                                                            data-bs-toggle="tab" data-bs-target="#profile" type="button"
                                                            role="tab" aria-controls="profile" aria-selected="false">
                                                            {{ __('about_me') }}
                                                        </button>
                                                    </li>
                                                </ul>
                                                <div class="tab-content" id="myTabContent">
                                                    <div class="tab-pane fade show active" id="home" role="tabpanel"
                                                        aria-labelledby="home-tab">
                                                        <div
                                                            class="tw-py-6 tw-flex tw-flex-col tw-gap-3 tw-text-base tw-text-[#5E6670]">
                                                            <p class="tw-mb-0">{{ candidate?.cover_letter?.cover_letter ?? '' }}
                                                            </p>

                                                        </div>
                                                        <div class="devider"></div>
                                                        <div class="social-links">
                                                            <h2 class="title">{{ __('follow_me_social_media') }}</h2>
                                                            <div class="social-media">
                                                                <ul>
                                                                    <li v-if="candidate.social_info[0]">
                                                                        <a :href="candidate.social_info[0]['url']"
                                                                            class="bg-primary-50 text-primary-500 plain-button tw-inline-flex tw-justify-center tw-items-center tw-rounded !tw-w-10 !tw-h-10 hover:bg-primary-500 hover:text-primary-50">
                                                                            <svg width="20" height="20"
                                                                                viewBox="0 0 20 20" fill="none"
                                                                                xmlns="http://www.w3.org/2000/svg">
                                                                                <path
                                                                                    d="M11.6666 20H7.70126V10.1414H5V6.9316H7.70116V4.64762C7.70116 1.9411 8.89588 0 12.8505 0C13.6869 0 15 0.168134 15 0.168134V3.14858H13.6208C12.2155 3.14858 11.6668 3.5749 11.6668 4.75352V6.9316H14.9474L14.6553 10.1414H11.6667L11.6666 20Z"
                                                                                    fill="var(--primary-500)"></path>
                                                                            </svg>
                                                                        </a>
                                                                    </li>
                                                                    <li v-if="candidate.social_info[1]">
                                                                        <a :href="candidate.social_info[1]['url']"
                                                                            class="bg-primary-50 text-primary-500 plain-button tw-inline-flex tw-justify-center tw-items-center tw-rounded !tw-w-10 !tw-h-10 hover:bg-primary-500 hover:text-primary-50">
                                                                            <svg width="20" height="20"
                                                                                viewBox="0 0 20 20" fill="none"
                                                                                xmlns="http://www.w3.org/2000/svg">
                                                                                <path
                                                                                    d="M6.25472 18.0025C13.7602 18.0025 17.8646 11.8202 17.8646 6.45913C17.8646 6.28355 17.861 6.10878 17.8531 5.93474C18.6517 5.36017 19.3409 4.64876 19.8885 3.83387C19.1573 4.15706 18.3706 4.37457 17.5452 4.47265C18.3877 3.97039 19.0344 3.17581 19.3396 2.22867C18.5386 2.70111 17.6623 3.03416 16.7485 3.21345C16.0039 2.42486 14.9438 1.93164 13.77 1.93164C11.5166 1.93164 9.68928 3.74853 9.68928 5.98813C9.68928 6.30653 9.72514 6.61614 9.79515 6.91321C6.40381 6.74354 3.3966 5.12914 1.38403 2.67436C1.02154 3.2935 0.830883 3.99717 0.831516 4.71351C0.831516 6.12108 1.55187 7.36364 2.64735 8.0906C1.99936 8.07093 1.36559 7.8969 0.799339 7.58316C0.798732 7.6002 0.798732 7.61679 0.798732 7.63499C0.798732 9.59978 2.20524 11.2403 4.07233 11.612C3.72166 11.7069 3.35981 11.7549 2.99637 11.7547C2.7339 11.7547 2.4781 11.729 2.22949 11.6816C2.74898 13.2936 4.25532 14.4666 6.04129 14.4994C4.64469 15.5878 2.88541 16.236 0.973322 16.236C0.648053 16.2363 0.323049 16.2176 0 16.1798C1.80589 17.3307 3.95021 18.0022 6.25492 18.0022"
                                                                                    fill="var(--primary-500)"></path>
                                                                            </svg>
                                                                        </a>
                                                                    </li>
                                                                    <li v-if="candidate.social_info[2]">
                                                                        <a :href="candidate.social_info[2]['url']"
                                                                            class="bg-primary-50 text-primary-500 plain-button tw-inline-flex tw-justify-center tw-items-center tw-rounded !tw-w-10 !tw-h-10 hover:bg-primary-500 hover:text-primary-50">
                                                                            <svg width="20" height="20"
                                                                                viewBox="0 0 20 20" fill="none"
                                                                                xmlns="http://www.w3.org/2000/svg">
                                                                                <path
                                                                                    d="M0 2.66051C0 2.01699 0.225232 1.48611 0.675676 1.06784C1.12612 0.649561 1.71172 0.44043 2.43243 0.44043C3.14029 0.44043 3.71299 0.646337 4.15058 1.05819C4.60102 1.4829 4.82625 2.0363 4.82625 2.71842C4.82625 3.33618 4.60747 3.85097 4.16988 4.26282C3.71944 4.68753 3.12741 4.89989 2.39382 4.89989H2.37452C1.66666 4.89989 1.09396 4.68753 0.656371 4.26282C0.218784 3.83811 0 3.304 0 2.66051ZM0.250965 19.5524V6.65664H4.53668V19.5524H0.250965ZM6.9112 19.5524H11.1969V12.3516C11.1969 11.9012 11.2484 11.5537 11.3514 11.3092C11.5315 10.8716 11.805 10.5015 12.1718 10.1991C12.5386 9.89666 12.9987 9.74545 13.5521 9.74545C14.9936 9.74545 15.7143 10.7171 15.7143 12.6605V19.5524H20V12.1586C20 10.2538 19.5496 8.80915 18.6486 7.8246C17.7477 6.84004 16.5573 6.34776 15.0772 6.34776C13.417 6.34776 12.1236 7.06205 11.1969 8.49062V8.52923H11.1776L11.1969 8.49062V6.65664H6.9112C6.93693 7.06848 6.94981 8.34904 6.94981 10.4983C6.94981 12.6476 6.93693 15.6656 6.9112 19.5524Z"
                                                                                    fill="var(--primary-500)"></path>
                                                                            </svg>
                                                                        </a>
                                                                    </li>
                                                                    <li v-if="candidate.social_info[3]">
                                                                        <a :href="candidate.social_info[3]['url']"
                                                                            class="bg-primary-50 text-primary-500 plain-button tw-inline-flex tw-justify-center tw-items-center tw-rounded !tw-w-10 !tw-h-10 hover:bg-primary-500 hover:text-primary-50">
                                                                            <svg width="20" height="20"
                                                                                viewBox="0 0 20 20" fill="none"
                                                                                xmlns="http://www.w3.org/2000/svg">
                                                                                <path
                                                                                    d="M10 20C15.5228 20 20 15.5228 20 10C20 4.47715 15.5228 0 10 0C4.47715 0 0 4.47715 0 10C0 15.5228 4.47715 20 10 20Z"
                                                                                    fill="var(--primary-500)"></path>
                                                                                <path class="path2"
                                                                                    d="M16.6703 10.0004C16.6723 10.1374 16.6553 10.2742 16.6193 10.4064C16.5833 10.5387 16.5288 10.6654 16.4573 10.7824C16.386 10.8994 16.2985 11.0059 16.1975 11.0987C16.0965 11.1914 15.983 11.2694 15.8603 11.3304C15.863 11.3669 15.8653 11.4037 15.8665 11.4404C15.8695 11.5137 15.8695 11.5871 15.8665 11.6604C15.8653 11.6972 15.863 11.7339 15.8603 11.7704C15.8603 14.0104 13.2502 15.8305 10.0302 15.8305C6.81018 15.8305 4.20016 14.0104 4.20016 11.7704C4.19741 11.7339 4.19516 11.6972 4.19391 11.6604C4.1909 11.5871 4.1909 11.5137 4.19391 11.4404C4.19516 11.4037 4.19741 11.3669 4.20016 11.3304C4.00784 11.242 3.83704 11.1129 3.69955 10.952C3.56207 10.791 3.46119 10.6022 3.4039 10.3984C3.34671 10.1946 3.33452 9.98072 3.3682 9.77169C3.40187 9.56267 3.48059 9.36347 3.5989 9.18789C3.71711 9.01235 3.8721 8.86463 4.05311 8.75497C4.23413 8.64532 4.43683 8.57636 4.64716 8.55289C4.85752 8.52931 5.07049 8.55176 5.27131 8.61867C5.47213 8.68558 5.656 8.79536 5.81017 8.94039C6.09638 8.7464 6.39625 8.57336 6.70743 8.42264C7.01868 8.27214 7.34044 8.14413 7.67019 8.04013C7.99994 7.93613 8.33694 7.85638 8.6782 7.80113C9.01945 7.74588 9.36445 7.71563 9.71021 7.71038L10.4502 4.24034C10.4585 4.20009 10.4747 4.16209 10.498 4.12809C10.521 4.09434 10.5507 4.06534 10.585 4.04309C10.6195 4.02084 10.658 4.00559 10.6985 3.99809C10.7387 3.99084 10.7802 3.99159 10.8202 4.00034L13.2702 4.49035C13.5175 4.06559 14.0365 3.88359 14.4948 4.06059C14.953 4.23759 15.215 4.72135 15.1125 5.20185C15.0103 5.68236 14.5738 6.01736 14.0832 5.99211C13.5925 5.96686 13.1927 5.58886 13.1402 5.10035L11.0002 4.65035L10.3502 7.77038C10.6917 7.77763 11.0325 7.80963 11.3695 7.86588C12.0443 7.97838 12.6994 8.1877 13.3145 8.48739C13.6217 8.63689 13.9177 8.80833 14.2002 9.00039C14.4034 8.80534 14.6587 8.67328 14.9353 8.62014C15.2118 8.56681 15.4979 8.59469 15.759 8.70039C16.0201 8.80604 16.2451 8.98499 16.4068 9.21564C16.5685 9.44632 16.66 9.71887 16.6703 10.0004V10.0004ZM6.74618 11.3832C6.79668 11.5044 6.87018 11.6147 6.96318 11.7074C7.05593 11.8004 7.16618 11.8739 7.28744 11.9244C7.4088 11.9746 7.53885 12.0004 7.67019 12.0004C8.07469 12.0004 8.4392 11.7567 8.5942 11.3832C8.74895 11.0094 8.6632 10.5794 8.37719 10.2934C8.09119 10.0074 7.66119 9.92165 7.28744 10.0764C6.91393 10.2314 6.67018 10.5959 6.67018 11.0004C6.67018 11.1317 6.69593 11.2617 6.74618 11.3832V11.3832ZM12.4737 13.7837C12.525 13.7337 12.5542 13.6654 12.5552 13.5939C12.5562 13.5222 12.5285 13.4532 12.4785 13.4019C12.4285 13.3507 12.3602 13.3214 12.2887 13.3204C12.217 13.3194 12.148 13.3472 12.0902 13.3904C11.9399 13.4983 11.7806 13.5932 11.6142 13.6742C11.4478 13.7552 11.2749 13.822 11.0972 13.8739C10.9197 13.9257 10.738 13.9624 10.5542 13.9837C10.3702 14.0049 10.185 14.0104 10.0002 14.0004C9.81571 14.0087 9.63071 14.0012 9.44746 13.9782C9.26416 13.9554 9.08316 13.9169 8.90645 13.8632C8.72945 13.8097 8.5577 13.7412 8.39245 13.6587C8.22719 13.5762 8.06919 13.4797 7.92019 13.3704C7.86844 13.3279 7.80269 13.3062 7.73569 13.3094C7.66869 13.3127 7.60544 13.3409 7.55794 13.3882C7.51069 13.4357 7.48244 13.4989 7.47919 13.5659C7.47594 13.6329 7.49769 13.6987 7.54019 13.7504C7.71594 13.8829 7.90269 13.9999 8.09844 14.1004C8.29419 14.2009 8.49845 14.2842 8.70845 14.3497C8.91845 14.4152 9.1337 14.4624 9.35195 14.4912C9.57021 14.5197 9.79046 14.5294 10.0102 14.5204C10.23 14.5294 10.4502 14.5197 10.6685 14.4912C11.1056 14.4337 11.5296 14.3015 11.922 14.1004C12.1177 13.9999 12.3045 13.8829 12.4802 13.7504L12.4737 13.7837ZM12.2902 12.0804C12.4257 12.0819 12.5602 12.0557 12.6855 12.0037C12.8107 11.9514 12.924 11.8744 13.0185 11.7772C13.113 11.6799 13.1865 11.5644 13.235 11.4377C13.2835 11.3111 13.3057 11.1759 13.3002 11.0404C13.3002 10.6359 13.0565 10.2714 12.683 10.1164C12.3092 9.96165 11.8792 10.0474 11.5932 10.3334C11.3072 10.6194 11.2215 11.0494 11.3762 11.4232C11.5312 11.7967 11.8957 12.0404 12.3002 12.0404L12.2902 12.0804Z"
                                                                                    fill="white"></path>
                                                                            </svg>
                                                                        </a>
                                                                    </li>
                                                                    <li v-if="candidate.social_info[4]">
                                                                        <a :href="candidate.social_info[4]['url']"
                                                                            class="bg-primary-50 text-primary-500 plain-button tw-inline-flex tw-justify-center tw-items-center tw-rounded !tw-w-10 !tw-h-10 hover:bg-primary-500 hover:text-primary-50">
                                                                            <svg width="20" height="20"
                                                                                viewBox="0 0 20 20" fill="none"
                                                                                xmlns="http://www.w3.org/2000/svg">
                                                                                <path
                                                                                    d="M6.6672 10C6.6672 8.15912 8.15912 6.6664 10 6.6664C11.8409 6.6664 13.3336 8.15912 13.3336 10C13.3336 11.8409 11.8409 13.3336 10 13.3336C8.15912 13.3336 6.6672 11.8409 6.6672 10ZM4.86512 10C4.86512 12.836 7.164 15.1349 10 15.1349C12.836 15.1349 15.1349 12.836 15.1349 10C15.1349 7.164 12.836 4.86512 10 4.86512C7.164 4.86512 4.86512 7.164 4.86512 10ZM14.1382 4.66152C14.1381 4.89886 14.2083 5.13089 14.3401 5.32829C14.4719 5.52568 14.6593 5.67956 14.8785 5.77047C15.0977 5.86138 15.339 5.88524 15.5718 5.83904C15.8046 5.79283 16.0185 5.67862 16.1863 5.51087C16.3542 5.34311 16.4686 5.12934 16.515 4.89658C16.5614 4.66382 16.5377 4.42253 16.447 4.20322C16.3563 3.98392 16.2025 3.79644 16.0052 3.6645C15.808 3.53257 15.576 3.4621 15.3386 3.462H15.3382C15.02 3.46215 14.715 3.58856 14.49 3.81347C14.265 4.03837 14.1384 4.34339 14.1382 4.66152ZM5.96 18.1398C4.98504 18.0954 4.45512 17.933 4.10296 17.7958C3.63608 17.614 3.30296 17.3975 2.95272 17.0478C2.60248 16.698 2.38568 16.3652 2.20472 15.8983C2.06744 15.5463 1.90504 15.0162 1.86072 14.0413C1.81224 12.9872 1.80256 12.6706 1.80256 10.0001C1.80256 7.3296 1.81304 7.01384 1.86072 5.95888C1.90512 4.98392 2.06872 4.45488 2.20472 4.10184C2.38648 3.63496 2.60296 3.30184 2.95272 2.9516C3.30248 2.60136 3.63528 2.38456 4.10296 2.2036C4.45496 2.06632 4.98504 1.90392 5.96 1.8596C7.01408 1.81112 7.33072 1.80144 10 1.80144C12.6693 1.80144 12.9862 1.81192 14.0412 1.8596C15.0162 1.904 15.5452 2.0676 15.8982 2.2036C16.3651 2.38456 16.6982 2.60184 17.0485 2.9516C17.3987 3.30136 17.6147 3.63496 17.7965 4.10184C17.9338 4.45384 18.0962 4.98392 18.1405 5.95888C18.189 7.01384 18.1986 7.3296 18.1986 10.0001C18.1986 12.6706 18.189 12.9863 18.1405 14.0413C18.0961 15.0162 17.9329 15.5462 17.7965 15.8983C17.6147 16.3652 17.3982 16.6983 17.0485 17.0478C16.6987 17.3972 16.3651 17.614 15.8982 17.7958C15.5462 17.933 15.0162 18.0954 14.0412 18.1398C12.9871 18.1882 12.6705 18.1979 10 18.1979C7.32952 18.1979 7.01376 18.1882 5.96 18.1398ZM5.8772 0.06056C4.81264 0.10904 4.0852 0.27784 3.44992 0.52504C2.792 0.78032 2.23504 1.1228 1.67848 1.67848C1.12192 2.23416 0.78032 2.792 0.52504 3.44992C0.27784 4.0856 0.10904 4.81264 0.06056 5.8772C0.01128 6.94344 0 7.28432 0 10C0 12.7157 0.01128 13.0566 0.06056 14.1228C0.10904 15.1874 0.27784 15.9144 0.52504 16.5501C0.78032 17.2076 1.122 17.7661 1.67848 18.3215C2.23496 18.877 2.792 19.219 3.44992 19.475C4.0864 19.7222 4.81264 19.891 5.8772 19.9394C6.944 19.9879 7.28432 20 10 20C12.7157 20 13.0566 19.9887 14.1228 19.9394C15.1874 19.891 15.9144 19.7222 16.5501 19.475C17.2076 19.219 17.765 18.8772 18.3215 18.3215C18.8781 17.7658 19.219 17.2076 19.475 16.5501C19.7222 15.9144 19.8918 15.1874 19.9394 14.1228C19.9879 13.0558 19.9992 12.7157 19.9992 10C19.9992 7.28432 19.9879 6.94344 19.9394 5.8772C19.891 4.81256 19.7222 4.0852 19.475 3.44992C19.219 2.7924 18.8772 2.23504 18.3215 1.67848C17.7658 1.12192 17.2076 0.78032 16.5509 0.52504C15.9144 0.27784 15.1874 0.10824 14.1236 0.06056C13.0574 0.01208 12.7165 0 10.0008 0C7.28512 0 6.944 0.01128 5.8772 0.06056Z"
                                                                                    fill="var(--primary-500)"></path>
                                                                                <path
                                                                                    d="M6.6672 10C6.6672 8.15912 8.15912 6.6664 10 6.6664C11.8409 6.6664 13.3336 8.15912 13.3336 10C13.3336 11.8409 11.8409 13.3336 10 13.3336C8.15912 13.3336 6.6672 11.8409 6.6672 10ZM4.86512 10C4.86512 12.836 7.164 15.1349 10 15.1349C12.836 15.1349 15.1349 12.836 15.1349 10C15.1349 7.164 12.836 4.86512 10 4.86512C7.164 4.86512 4.86512 7.164 4.86512 10ZM14.1382 4.66152C14.1381 4.89886 14.2083 5.13089 14.3401 5.32829C14.4719 5.52568 14.6593 5.67956 14.8785 5.77047C15.0977 5.86138 15.339 5.88524 15.5718 5.83904C15.8046 5.79283 16.0185 5.67862 16.1863 5.51087C16.3542 5.34311 16.4686 5.12934 16.515 4.89658C16.5614 4.66382 16.5377 4.42253 16.447 4.20322C16.3563 3.98392 16.2025 3.79644 16.0052 3.6645C15.808 3.53257 15.576 3.4621 15.3386 3.462H15.3382C15.02 3.46215 14.715 3.58856 14.49 3.81347C14.265 4.03837 14.1384 4.34339 14.1382 4.66152ZM5.96 18.1398C4.98504 18.0954 4.45512 17.933 4.10296 17.7958C3.63608 17.614 3.30296 17.3975 2.95272 17.0478C2.60248 16.698 2.38568 16.3652 2.20472 15.8983C2.06744 15.5463 1.90504 15.0162 1.86072 14.0413C1.81224 12.9872 1.80256 12.6706 1.80256 10.0001C1.80256 7.3296 1.81304 7.01384 1.86072 5.95888C1.90512 4.98392 2.06872 4.45488 2.20472 4.10184C2.38648 3.63496 2.60296 3.30184 2.95272 2.9516C3.30248 2.60136 3.63528 2.38456 4.10296 2.2036C4.45496 2.06632 4.98504 1.90392 5.96 1.8596C7.01408 1.81112 7.33072 1.80144 10 1.80144C12.6693 1.80144 12.9862 1.81192 14.0412 1.8596C15.0162 1.904 15.5452 2.0676 15.8982 2.2036C16.3651 2.38456 16.6982 2.60184 17.0485 2.9516C17.3987 3.30136 17.6147 3.63496 17.7965 4.10184C17.9338 4.45384 18.0962 4.98392 18.1405 5.95888C18.189 7.01384 18.1986 7.3296 18.1986 10.0001C18.1986 12.6706 18.189 12.9863 18.1405 14.0413C18.0961 15.0162 17.9329 15.5462 17.7965 15.8983C17.6147 16.3652 17.3982 16.6983 17.0485 17.0478C16.6987 17.3972 16.3651 17.614 15.8982 17.7958C15.5462 17.933 15.0162 18.0954 14.0412 18.1398C12.9871 18.1882 12.6705 18.1979 10 18.1979C7.32952 18.1979 7.01376 18.1882 5.96 18.1398ZM5.8772 0.06056C4.81264 0.10904 4.0852 0.27784 3.44992 0.52504C2.792 0.78032 2.23504 1.1228 1.67848 1.67848C1.12192 2.23416 0.78032 2.792 0.52504 3.44992C0.27784 4.0856 0.10904 4.81264 0.06056 5.8772C0.01128 6.94344 0 7.28432 0 10C0 12.7157 0.01128 13.0566 0.06056 14.1228C0.10904 15.1874 0.27784 15.9144 0.52504 16.5501C0.78032 17.2076 1.122 17.7661 1.67848 18.3215C2.23496 18.877 2.792 19.219 3.44992 19.475C4.0864 19.7222 4.81264 19.891 5.8772 19.9394C6.944 19.9879 7.28432 20 10 20C12.7157 20 13.0566 19.9887 14.1228 19.9394C15.1874 19.891 15.9144 19.7222 16.5501 19.475C17.2076 19.219 17.765 18.8772 18.3215 18.3215C18.8781 17.7658 19.219 17.2076 19.475 16.5501C19.7222 15.9144 19.8918 15.1874 19.9394 14.1228C19.9879 13.0558 19.9992 12.7157 19.9992 10C19.9992 7.28432 19.9879 6.94344 19.9394 5.8772C19.891 4.81256 19.7222 4.0852 19.475 3.44992C19.219 2.7924 18.8772 2.23504 18.3215 1.67848C17.7658 1.12192 17.2076 0.78032 16.5509 0.52504C15.9144 0.27784 15.1874 0.10824 14.1236 0.06056C13.0574 0.01208 12.7165 0 10.0008 0C7.28512 0 6.944 0.01128 5.8772 0.06056Z"
                                                                                    fill="var(--primary-500)"></path>
                                                                            </svg>
                                                                        </a>
                                                                    </li>
                                                                    <li v-if="candidate.social_info[5]">
                                                                        <a :href="candidate.social_info[5]['url']"
                                                                            class="bg-primary-50 text-primary-500 plain-button tw-inline-flex tw-justify-center tw-items-center tw-rounded !tw-w-10 !tw-h-10 hover:bg-primary-500 hover:text-primary-50">
                                                                            <svg width="20" height="20"
                                                                                viewBox="0 0 20 20" fill="none"
                                                                                xmlns="http://www.w3.org/2000/svg">
                                                                                <path
                                                                                    d="M10.4433 17.041L6.34002 16.9644C5.01143 16.9377 3.67958 16.991 2.3771 16.7145C0.395646 16.3015 0.25528 14.2762 0.108384 12.5774C-0.0940053 10.189 -0.015661 7.75739 0.366267 5.38902C0.581714 4.05994 1.43044 3.26716 2.74271 3.18055C7.17243 2.86743 11.6315 2.90407 16.0514 3.05064C16.5182 3.06396 16.9883 3.13725 17.4486 3.22052C19.7206 3.62691 19.7761 5.92199 19.923 7.85399C20.0699 9.80597 20.0078 11.7679 19.7271 13.7066C19.5019 15.3122 19.071 16.6579 17.2527 16.7878C14.9742 16.9577 12.7479 17.0943 10.4629 17.051C10.4629 17.041 10.4498 17.041 10.4433 17.041ZM8.03095 12.9771C9.74799 11.9711 11.4324 10.9818 13.1396 9.98251C11.4193 8.97654 9.7382 7.98723 8.03095 6.98792V12.9771Z"
                                                                                    fill="var(--primary-500)"></path>
                                                                            </svg>
                                                                        </a>
                                                                    </li>
                                                                </ul>

                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div class="tab-pane fade " id="profile" role="tabpanel"
                                                        aria-labelledby="profile-tab">
                                                        <div class="tw-py-6">
                                                            <h4
                                                                class="tw-text-lg tw-font-medium tw-leading-[100%] tw-text-[#18191C] text-capitalize">
                                                                {{ __('Tentang') }}</h4>
                                                            <p class="tw-text-base tw-text-[#5E6670]"
                                                                id="candidate-bio">{{
                                                                sanitizedBio }} </p>
                                                            <h4
                                                                class="tw-text-lg tw-font-medium tw-leading-[100%] tw-text-[#18191C] tw-mb-4 text-uppercase">
                                                                {{ __('experience') }}</h4>
                                                            <ul class="tw-p-0 tw-m-0 tw-list-none ll-experience-list">
                                                                <li v-for="experience in candidate.experiences"
                                                                    :key="experience.id"
                                                                    class="tw-text-sm tw-flex tw-flex-col tw-gap-3">
                                                                    <div class="tw-flex tw-flex-col tw-gap-1">
                                                                        <p
                                                                            class="tw-m-0 tw-text-sm tw-text-[#0066CC] tw-font-medium -tw-mt-1.5">
                                                                            {{ experience.formatted_start }}</p>
                                                                        <h4
                                                                            class="tw-m-0 tw-text-sm tw-text-[#14181A] tw-font-medium">
                                                                            {{ experience.designation }}</h4>
                                                                        <p class="tw-m-0 tw-text-sm tw-text-[#707A7D]">
                                                                            {{ experience.company }} /
                                                                            {{ experience.department }}</p>
                                                                    </div>
                                                                    <p class="tw-m-0 tw-text-sm tw-text-[#3C4649]">
                                                                        {{ experience.responsibilities }}</p>
                                                                </li>
                                                            </ul>
                                                            <h4
                                                                class="tw-text-lg tw-font-medium tw-leading-[100%] tw-text-[#18191C] tw-mb-4">
                                                                {{ __('education') }}</h4>

                                                            <ul class="tw-p-0 tw-m-0 tw-list-none ll-experience-list"
                                                                id="education-container">
                                                                <li v-for="education in candidate.educations"
                                                                    :key="education.id"
                                                                    class="tw-text-sm tw-flex tw-flex-col tw-gap-3">
                                                                    <div class="tw-flex tw-flex-col tw-gap-1">
                                                                        <p class="tw-m-0 tw-text-sm tw-text-[#0066CC] tw-font-medium -tw-mt-1.5"
                                                                            id="candidate-edu-year">
                                                                            {{ education.year }}</p>
                                                                        <h4
                                                                            class="tw-m-0 tw-text-sm tw-text-[#14181A] tw-font-medium">
                                                                            {{ education.degree }}</h4>
                                                                        <p class="tw-m-0 tw-text-sm tw-text-[#707A7D]">
                                                                            {{ education.degree }} /
                                                                            {{ education.level }}</p>
                                                                    </div>
                                                                    <p class="tw-m-0 tw-text-sm tw-text-[#3C4649]">
                                                                        {{ education.notes }}</p>
                                                                </li>
                                                            </ul>

                                                        </div>
                                                        <div class="devider"></div>
                                                        <div class="social-links">
                                                            <h2 class="title"> {{ __('follow_me_social_media') }} </h2>
                                                            <div class="social-media">
                                                                <ul>
                                                                    <li v-if="candidate.social_info[0]">
                                                                        <a :href="candidate.social_info[0]['url']"
                                                                            class="bg-primary-50 text-primary-500 plain-button tw-inline-flex tw-justify-center tw-items-center tw-rounded !tw-w-10 !tw-h-10 hover:bg-primary-500 hover:text-primary-50">
                                                                            <svg width="20" height="20"
                                                                                viewBox="0 0 20 20" fill="none"
                                                                                xmlns="http://www.w3.org/2000/svg">
                                                                                <path
                                                                                    d="M11.6666 20H7.70126V10.1414H5V6.9316H7.70116V4.64762C7.70116 1.9411 8.89588 0 12.8505 0C13.6869 0 15 0.168134 15 0.168134V3.14858H13.6208C12.2155 3.14858 11.6668 3.5749 11.6668 4.75352V6.9316H14.9474L14.6553 10.1414H11.6667L11.6666 20Z"
                                                                                    fill="var(--primary-500)"></path>
                                                                            </svg>
                                                                        </a>
                                                                    </li>
                                                                    <li v-if="candidate.social_info[1]">
                                                                        <a :href="candidate.social_info[1]['url']"
                                                                            class="bg-primary-50 text-primary-500 plain-button tw-inline-flex tw-justify-center tw-items-center tw-rounded !tw-w-10 !tw-h-10 hover:bg-primary-500 hover:text-primary-50">
                                                                            <svg width="20" height="20"
                                                                                viewBox="0 0 20 20" fill="none"
                                                                                xmlns="http://www.w3.org/2000/svg">
                                                                                <path
                                                                                    d="M6.25472 18.0025C13.7602 18.0025 17.8646 11.8202 17.8646 6.45913C17.8646 6.28355 17.861 6.10878 17.8531 5.93474C18.6517 5.36017 19.3409 4.64876 19.8885 3.83387C19.1573 4.15706 18.3706 4.37457 17.5452 4.47265C18.3877 3.97039 19.0344 3.17581 19.3396 2.22867C18.5386 2.70111 17.6623 3.03416 16.7485 3.21345C16.0039 2.42486 14.9438 1.93164 13.77 1.93164C11.5166 1.93164 9.68928 3.74853 9.68928 5.98813C9.68928 6.30653 9.72514 6.61614 9.79515 6.91321C6.40381 6.74354 3.3966 5.12914 1.38403 2.67436C1.02154 3.2935 0.830883 3.99717 0.831516 4.71351C0.831516 6.12108 1.55187 7.36364 2.64735 8.0906C1.99936 8.07093 1.36559 7.8969 0.799339 7.58316C0.798732 7.6002 0.798732 7.61679 0.798732 7.63499C0.798732 9.59978 2.20524 11.2403 4.07233 11.612C3.72166 11.7069 3.35981 11.7549 2.99637 11.7547C2.7339 11.7547 2.4781 11.729 2.22949 11.6816C2.74898 13.2936 4.25532 14.4666 6.04129 14.4994C4.64469 15.5878 2.88541 16.236 0.973322 16.236C0.648053 16.2363 0.323049 16.2176 0 16.1798C1.80589 17.3307 3.95021 18.0022 6.25492 18.0022"
                                                                                    fill="var(--primary-500)"></path>
                                                                            </svg>
                                                                        </a>
                                                                    </li>
                                                                    <li v-if="candidate.social_info[2]">
                                                                        <a :href="candidate.social_info[2]['url']"
                                                                            class="bg-primary-50 text-primary-500 plain-button tw-inline-flex tw-justify-center tw-items-center tw-rounded !tw-w-10 !tw-h-10 hover:bg-primary-500 hover:text-primary-50">
                                                                            <svg width="20" height="20"
                                                                                viewBox="0 0 20 20" fill="none"
                                                                                xmlns="http://www.w3.org/2000/svg">
                                                                                <path
                                                                                    d="M0 2.66051C0 2.01699 0.225232 1.48611 0.675676 1.06784C1.12612 0.649561 1.71172 0.44043 2.43243 0.44043C3.14029 0.44043 3.71299 0.646337 4.15058 1.05819C4.60102 1.4829 4.82625 2.0363 4.82625 2.71842C4.82625 3.33618 4.60747 3.85097 4.16988 4.26282C3.71944 4.68753 3.12741 4.89989 2.39382 4.89989H2.37452C1.66666 4.89989 1.09396 4.68753 0.656371 4.26282C0.218784 3.83811 0 3.304 0 2.66051ZM0.250965 19.5524V6.65664H4.53668V19.5524H0.250965ZM6.9112 19.5524H11.1969V12.3516C11.1969 11.9012 11.2484 11.5537 11.3514 11.3092C11.5315 10.8716 11.805 10.5015 12.1718 10.1991C12.5386 9.89666 12.9987 9.74545 13.5521 9.74545C14.9936 9.74545 15.7143 10.7171 15.7143 12.6605V19.5524H20V12.1586C20 10.2538 19.5496 8.80915 18.6486 7.8246C17.7477 6.84004 16.5573 6.34776 15.0772 6.34776C13.417 6.34776 12.1236 7.06205 11.1969 8.49062V8.52923H11.1776L11.1969 8.49062V6.65664H6.9112C6.93693 7.06848 6.94981 8.34904 6.94981 10.4983C6.94981 12.6476 6.93693 15.6656 6.9112 19.5524Z"
                                                                                    fill="var(--primary-500)"></path>
                                                                            </svg>
                                                                        </a>
                                                                    </li>
                                                                    <li v-if="candidate.social_info[3]">
                                                                        <a :href="candidate.social_info[3]['url']"
                                                                            class="bg-primary-50 text-primary-500 plain-button tw-inline-flex tw-justify-center tw-items-center tw-rounded !tw-w-10 !tw-h-10 hover:bg-primary-500 hover:text-primary-50">
                                                                            <svg width="20" height="20"
                                                                                viewBox="0 0 20 20" fill="none"
                                                                                xmlns="http://www.w3.org/2000/svg">
                                                                                <path
                                                                                    d="M10 20C15.5228 20 20 15.5228 20 10C20 4.47715 15.5228 0 10 0C4.47715 0 0 4.47715 0 10C0 15.5228 4.47715 20 10 20Z"
                                                                                    fill="var(--primary-500)"></path>
                                                                                <path class="path2"
                                                                                    d="M16.6703 10.0004C16.6723 10.1374 16.6553 10.2742 16.6193 10.4064C16.5833 10.5387 16.5288 10.6654 16.4573 10.7824C16.386 10.8994 16.2985 11.0059 16.1975 11.0987C16.0965 11.1914 15.983 11.2694 15.8603 11.3304C15.863 11.3669 15.8653 11.4037 15.8665 11.4404C15.8695 11.5137 15.8695 11.5871 15.8665 11.6604C15.8653 11.6972 15.863 11.7339 15.8603 11.7704C15.8603 14.0104 13.2502 15.8305 10.0302 15.8305C6.81018 15.8305 4.20016 14.0104 4.20016 11.7704C4.19741 11.7339 4.19516 11.6972 4.19391 11.6604C4.1909 11.5871 4.1909 11.5137 4.19391 11.4404C4.19516 11.4037 4.19741 11.3669 4.20016 11.3304C4.00784 11.242 3.83704 11.1129 3.69955 10.952C3.56207 10.791 3.46119 10.6022 3.4039 10.3984C3.34671 10.1946 3.33452 9.98072 3.3682 9.77169C3.40187 9.56267 3.48059 9.36347 3.5989 9.18789C3.71711 9.01235 3.8721 8.86463 4.05311 8.75497C4.23413 8.64532 4.43683 8.57636 4.64716 8.55289C4.85752 8.52931 5.07049 8.55176 5.27131 8.61867C5.47213 8.68558 5.656 8.79536 5.81017 8.94039C6.09638 8.7464 6.39625 8.57336 6.70743 8.42264C7.01868 8.27214 7.34044 8.14413 7.67019 8.04013C7.99994 7.93613 8.33694 7.85638 8.6782 7.80113C9.01945 7.74588 9.36445 7.71563 9.71021 7.71038L10.4502 4.24034C10.4585 4.20009 10.4747 4.16209 10.498 4.12809C10.521 4.09434 10.5507 4.06534 10.585 4.04309C10.6195 4.02084 10.658 4.00559 10.6985 3.99809C10.7387 3.99084 10.7802 3.99159 10.8202 4.00034L13.2702 4.49035C13.5175 4.06559 14.0365 3.88359 14.4948 4.06059C14.953 4.23759 15.215 4.72135 15.1125 5.20185C15.0103 5.68236 14.5738 6.01736 14.0832 5.99211C13.5925 5.96686 13.1927 5.58886 13.1402 5.10035L11.0002 4.65035L10.3502 7.77038C10.6917 7.77763 11.0325 7.80963 11.3695 7.86588C12.0443 7.97838 12.6994 8.1877 13.3145 8.48739C13.6217 8.63689 13.9177 8.80833 14.2002 9.00039C14.4034 8.80534 14.6587 8.67328 14.9353 8.62014C15.2118 8.56681 15.4979 8.59469 15.759 8.70039C16.0201 8.80604 16.2451 8.98499 16.4068 9.21564C16.5685 9.44632 16.66 9.71887 16.6703 10.0004V10.0004ZM6.74618 11.3832C6.79668 11.5044 6.87018 11.6147 6.96318 11.7074C7.05593 11.8004 7.16618 11.8739 7.28744 11.9244C7.4088 11.9746 7.53885 12.0004 7.67019 12.0004C8.07469 12.0004 8.4392 11.7567 8.5942 11.3832C8.74895 11.0094 8.6632 10.5794 8.37719 10.2934C8.09119 10.0074 7.66119 9.92165 7.28744 10.0764C6.91393 10.2314 6.67018 10.5959 6.67018 11.0004C6.67018 11.1317 6.69593 11.2617 6.74618 11.3832V11.3832ZM12.4737 13.7837C12.525 13.7337 12.5542 13.6654 12.5552 13.5939C12.5562 13.5222 12.5285 13.4532 12.4785 13.4019C12.4285 13.3507 12.3602 13.3214 12.2887 13.3204C12.217 13.3194 12.148 13.3472 12.0902 13.3904C11.9399 13.4983 11.7806 13.5932 11.6142 13.6742C11.4478 13.7552 11.2749 13.822 11.0972 13.8739C10.9197 13.9257 10.738 13.9624 10.5542 13.9837C10.3702 14.0049 10.185 14.0104 10.0002 14.0004C9.81571 14.0087 9.63071 14.0012 9.44746 13.9782C9.26416 13.9554 9.08316 13.9169 8.90645 13.8632C8.72945 13.8097 8.5577 13.7412 8.39245 13.6587C8.22719 13.5762 8.06919 13.4797 7.92019 13.3704C7.86844 13.3279 7.80269 13.3062 7.73569 13.3094C7.66869 13.3127 7.60544 13.3409 7.55794 13.3882C7.51069 13.4357 7.48244 13.4989 7.47919 13.5659C7.47594 13.6329 7.49769 13.6987 7.54019 13.7504C7.71594 13.8829 7.90269 13.9999 8.09844 14.1004C8.29419 14.2009 8.49845 14.2842 8.70845 14.3497C8.91845 14.4152 9.1337 14.4624 9.35195 14.4912C9.57021 14.5197 9.79046 14.5294 10.0102 14.5204C10.23 14.5294 10.4502 14.5197 10.6685 14.4912C11.1056 14.4337 11.5296 14.3015 11.922 14.1004C12.1177 13.9999 12.3045 13.8829 12.4802 13.7504L12.4737 13.7837ZM12.2902 12.0804C12.4257 12.0819 12.5602 12.0557 12.6855 12.0037C12.8107 11.9514 12.924 11.8744 13.0185 11.7772C13.113 11.6799 13.1865 11.5644 13.235 11.4377C13.2835 11.3111 13.3057 11.1759 13.3002 11.0404C13.3002 10.6359 13.0565 10.2714 12.683 10.1164C12.3092 9.96165 11.8792 10.0474 11.5932 10.3334C11.3072 10.6194 11.2215 11.0494 11.3762 11.4232C11.5312 11.7967 11.8957 12.0404 12.3002 12.0404L12.2902 12.0804Z"
                                                                                    fill="white"></path>
                                                                            </svg>
                                                                        </a>
                                                                    </li>
                                                                    <li v-if="candidate.social_info[4]">
                                                                        <a :href="candidate.social_info[4]['url']"
                                                                            class="bg-primary-50 text-primary-500 plain-button tw-inline-flex tw-justify-center tw-items-center tw-rounded !tw-w-10 !tw-h-10 hover:bg-primary-500 hover:text-primary-50">
                                                                            <svg width="20" height="20"
                                                                                viewBox="0 0 20 20" fill="none"
                                                                                xmlns="http://www.w3.org/2000/svg">
                                                                                <path
                                                                                    d="M6.6672 10C6.6672 8.15912 8.15912 6.6664 10 6.6664C11.8409 6.6664 13.3336 8.15912 13.3336 10C13.3336 11.8409 11.8409 13.3336 10 13.3336C8.15912 13.3336 6.6672 11.8409 6.6672 10ZM4.86512 10C4.86512 12.836 7.164 15.1349 10 15.1349C12.836 15.1349 15.1349 12.836 15.1349 10C15.1349 7.164 12.836 4.86512 10 4.86512C7.164 4.86512 4.86512 7.164 4.86512 10ZM14.1382 4.66152C14.1381 4.89886 14.2083 5.13089 14.3401 5.32829C14.4719 5.52568 14.6593 5.67956 14.8785 5.77047C15.0977 5.86138 15.339 5.88524 15.5718 5.83904C15.8046 5.79283 16.0185 5.67862 16.1863 5.51087C16.3542 5.34311 16.4686 5.12934 16.515 4.89658C16.5614 4.66382 16.5377 4.42253 16.447 4.20322C16.3563 3.98392 16.2025 3.79644 16.0052 3.6645C15.808 3.53257 15.576 3.4621 15.3386 3.462H15.3382C15.02 3.46215 14.715 3.58856 14.49 3.81347C14.265 4.03837 14.1384 4.34339 14.1382 4.66152ZM5.96 18.1398C4.98504 18.0954 4.45512 17.933 4.10296 17.7958C3.63608 17.614 3.30296 17.3975 2.95272 17.0478C2.60248 16.698 2.38568 16.3652 2.20472 15.8983C2.06744 15.5463 1.90504 15.0162 1.86072 14.0413C1.81224 12.9872 1.80256 12.6706 1.80256 10.0001C1.80256 7.3296 1.81304 7.01384 1.86072 5.95888C1.90512 4.98392 2.06872 4.45488 2.20472 4.10184C2.38648 3.63496 2.60296 3.30184 2.95272 2.9516C3.30248 2.60136 3.63528 2.38456 4.10296 2.2036C4.45496 2.06632 4.98504 1.90392 5.96 1.8596C7.01408 1.81112 7.33072 1.80144 10 1.80144C12.6693 1.80144 12.9862 1.81192 14.0412 1.8596C15.0162 1.904 15.5452 2.0676 15.8982 2.2036C16.3651 2.38456 16.6982 2.60184 17.0485 2.9516C17.3987 3.30136 17.6147 3.63496 17.7965 4.10184C17.9338 4.45384 18.0962 4.98392 18.1405 5.95888C18.189 7.01384 18.1986 7.3296 18.1986 10.0001C18.1986 12.6706 18.189 12.9863 18.1405 14.0413C18.0961 15.0162 17.9329 15.5462 17.7965 15.8983C17.6147 16.3652 17.3982 16.6983 17.0485 17.0478C16.6987 17.3972 16.3651 17.614 15.8982 17.7958C15.5462 17.933 15.0162 18.0954 14.0412 18.1398C12.9871 18.1882 12.6705 18.1979 10 18.1979C7.32952 18.1979 7.01376 18.1882 5.96 18.1398ZM5.8772 0.06056C4.81264 0.10904 4.0852 0.27784 3.44992 0.52504C2.792 0.78032 2.23504 1.1228 1.67848 1.67848C1.12192 2.23416 0.78032 2.792 0.52504 3.44992C0.27784 4.0856 0.10904 4.81264 0.06056 5.8772C0.01128 6.94344 0 7.28432 0 10C0 12.7157 0.01128 13.0566 0.06056 14.1228C0.10904 15.1874 0.27784 15.9144 0.52504 16.5501C0.78032 17.2076 1.122 17.7661 1.67848 18.3215C2.23496 18.877 2.792 19.219 3.44992 19.475C4.0864 19.7222 4.81264 19.891 5.8772 19.9394C6.944 19.9879 7.28432 20 10 20C12.7157 20 13.0566 19.9887 14.1228 19.9394C15.1874 19.891 15.9144 19.7222 16.5501 19.475C17.2076 19.219 17.765 18.8772 18.3215 18.3215C18.8781 17.7658 19.219 17.2076 19.475 16.5501C19.7222 15.9144 19.8918 15.1874 19.9394 14.1228C19.9879 13.0558 19.9992 12.7157 19.9992 10C19.9992 7.28432 19.9879 6.94344 19.9394 5.8772C19.891 4.81256 19.7222 4.0852 19.475 3.44992C19.219 2.7924 18.8772 2.23504 18.3215 1.67848C17.7658 1.12192 17.2076 0.78032 16.5509 0.52504C15.9144 0.27784 15.1874 0.10824 14.1236 0.06056C13.0574 0.01208 12.7165 0 10.0008 0C7.28512 0 6.944 0.01128 5.8772 0.06056Z"
                                                                                    fill="var(--primary-500)"></path>
                                                                                <path
                                                                                    d="M6.6672 10C6.6672 8.15912 8.15912 6.6664 10 6.6664C11.8409 6.6664 13.3336 8.15912 13.3336 10C13.3336 11.8409 11.8409 13.3336 10 13.3336C8.15912 13.3336 6.6672 11.8409 6.6672 10ZM4.86512 10C4.86512 12.836 7.164 15.1349 10 15.1349C12.836 15.1349 15.1349 12.836 15.1349 10C15.1349 7.164 12.836 4.86512 10 4.86512C7.164 4.86512 4.86512 7.164 4.86512 10ZM14.1382 4.66152C14.1381 4.89886 14.2083 5.13089 14.3401 5.32829C14.4719 5.52568 14.6593 5.67956 14.8785 5.77047C15.0977 5.86138 15.339 5.88524 15.5718 5.83904C15.8046 5.79283 16.0185 5.67862 16.1863 5.51087C16.3542 5.34311 16.4686 5.12934 16.515 4.89658C16.5614 4.66382 16.5377 4.42253 16.447 4.20322C16.3563 3.98392 16.2025 3.79644 16.0052 3.6645C15.808 3.53257 15.576 3.4621 15.3386 3.462H15.3382C15.02 3.46215 14.715 3.58856 14.49 3.81347C14.265 4.03837 14.1384 4.34339 14.1382 4.66152ZM5.96 18.1398C4.98504 18.0954 4.45512 17.933 4.10296 17.7958C3.63608 17.614 3.30296 17.3975 2.95272 17.0478C2.60248 16.698 2.38568 16.3652 2.20472 15.8983C2.06744 15.5463 1.90504 15.0162 1.86072 14.0413C1.81224 12.9872 1.80256 12.6706 1.80256 10.0001C1.80256 7.3296 1.81304 7.01384 1.86072 5.95888C1.90512 4.98392 2.06872 4.45488 2.20472 4.10184C2.38648 3.63496 2.60296 3.30184 2.95272 2.9516C3.30248 2.60136 3.63528 2.38456 4.10296 2.2036C4.45496 2.06632 4.98504 1.90392 5.96 1.8596C7.01408 1.81112 7.33072 1.80144 10 1.80144C12.6693 1.80144 12.9862 1.81192 14.0412 1.8596C15.0162 1.904 15.5452 2.0676 15.8982 2.2036C16.3651 2.38456 16.6982 2.60184 17.0485 2.9516C17.3987 3.30136 17.6147 3.63496 17.7965 4.10184C17.9338 4.45384 18.0962 4.98392 18.1405 5.95888C18.189 7.01384 18.1986 7.3296 18.1986 10.0001C18.1986 12.6706 18.189 12.9863 18.1405 14.0413C18.0961 15.0162 17.9329 15.5462 17.7965 15.8983C17.6147 16.3652 17.3982 16.6983 17.0485 17.0478C16.6987 17.3972 16.3651 17.614 15.8982 17.7958C15.5462 17.933 15.0162 18.0954 14.0412 18.1398C12.9871 18.1882 12.6705 18.1979 10 18.1979C7.32952 18.1979 7.01376 18.1882 5.96 18.1398ZM5.8772 0.06056C4.81264 0.10904 4.0852 0.27784 3.44992 0.52504C2.792 0.78032 2.23504 1.1228 1.67848 1.67848C1.12192 2.23416 0.78032 2.792 0.52504 3.44992C0.27784 4.0856 0.10904 4.81264 0.06056 5.8772C0.01128 6.94344 0 7.28432 0 10C0 12.7157 0.01128 13.0566 0.06056 14.1228C0.10904 15.1874 0.27784 15.9144 0.52504 16.5501C0.78032 17.2076 1.122 17.7661 1.67848 18.3215C2.23496 18.877 2.792 19.219 3.44992 19.475C4.0864 19.7222 4.81264 19.891 5.8772 19.9394C6.944 19.9879 7.28432 20 10 20C12.7157 20 13.0566 19.9887 14.1228 19.9394C15.1874 19.891 15.9144 19.7222 16.5501 19.475C17.2076 19.219 17.765 18.8772 18.3215 18.3215C18.8781 17.7658 19.219 17.2076 19.475 16.5501C19.7222 15.9144 19.8918 15.1874 19.9394 14.1228C19.9879 13.0558 19.9992 12.7157 19.9992 10C19.9992 7.28432 19.9879 6.94344 19.9394 5.8772C19.891 4.81256 19.7222 4.0852 19.475 3.44992C19.219 2.7924 18.8772 2.23504 18.3215 1.67848C17.7658 1.12192 17.2076 0.78032 16.5509 0.52504C15.9144 0.27784 15.1874 0.10824 14.1236 0.06056C13.0574 0.01208 12.7165 0 10.0008 0C7.28512 0 6.944 0.01128 5.8772 0.06056Z"
                                                                                    fill="var(--primary-500)"></path>
                                                                            </svg>
                                                                        </a>
                                                                    </li>
                                                                    <li v-if="candidate.social_info[5]">
                                                                        <a :href="candidate.social_info[5]['url']"
                                                                            class="bg-primary-50 text-primary-500 plain-button tw-inline-flex tw-justify-center tw-items-center tw-rounded !tw-w-10 !tw-h-10 hover:bg-primary-500 hover:text-primary-50">
                                                                            <svg width="20" height="20"
                                                                                viewBox="0 0 20 20" fill="none"
                                                                                xmlns="http://www.w3.org/2000/svg">
                                                                                <path
                                                                                    d="M10.4433 17.041L6.34002 16.9644C5.01143 16.9377 3.67958 16.991 2.3771 16.7145C0.395646 16.3015 0.25528 14.2762 0.108384 12.5774C-0.0940053 10.189 -0.015661 7.75739 0.366267 5.38902C0.581714 4.05994 1.43044 3.26716 2.74271 3.18055C7.17243 2.86743 11.6315 2.90407 16.0514 3.05064C16.5182 3.06396 16.9883 3.13725 17.4486 3.22052C19.7206 3.62691 19.7761 5.92199 19.923 7.85399C20.0699 9.80597 20.0078 11.7679 19.7271 13.7066C19.5019 15.3122 19.071 16.6579 17.2527 16.7878C14.9742 16.9577 12.7479 17.0943 10.4629 17.051C10.4629 17.041 10.4498 17.041 10.4433 17.041ZM8.03095 12.9771C9.74799 11.9711 11.4324 10.9818 13.1396 9.98251C11.4193 8.97654 9.7382 7.98723 8.03095 6.98792V12.9771Z"
                                                                                    fill="var(--primary-500)"></path>
                                                                            </svg>
                                                                        </a>
                                                                    </li>
                                                                </ul>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                            <div v-if="answers && answers.length">
                                                <h2 class="title mt-4 text-uppercase">{{ __('question ans answers') }}
                                                </h2>
                                                <div v-for="ans in answers" :key="ans.id" class="my-4 border-bottom">
                                                    <dt>
                                                        {{ ans.question }}

                                                    </dt>
                                                    <dd>
                                                        {{ ans.answers ? ans.answers : '-' }}
                                                    </dd>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                            </div>
                            <div class="col-lg-4">
                                <div class="sidebar-widget">
                                    <div class="row">
                                        <div class="col-md-12" v-if="candidate.skills && candidate.skills.length">
                                            <h2 class="title">{{ __('skills') }}</h2>
                                            <div class="tw-flex tw-flex-wrap tw-gap-2 tw-mb-6">
                                                <span
                                                    class="tw-bg-[#E7F0FA] tw-rounded-[4px] tw-text-sm tw-text-primary-500 tw-px-3 tw-py-1.5"
                                                    v-for="skill in candidate.skills" :key="skill.id">
                                                    {{ skill.name }}
                                                </span>
                                            </div>
                                        </div>
                                        <div class="col-md-12" v-if="candidate.languages && candidate.languages.length">
                                            <h2 class="title">{{ __('languages') }}</h2>
                                            <div class="tw-flex tw-flex-wrap tw-gap-2 tw-mb-6">
                                                <span
                                                    class="tw-bg-[#E7F0FA] tw-rounded-[4px] tw-text-sm tw-text-primary-500 tw-px-3 tw-py-1.5"
                                                    v-for="language in candidate.languages" :key="language.id">
                                                    {{ language.name }}
                                                </span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="sidebar-widget">
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="icon-box">
                                                <div class="icon-img">
                                                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none"
                                                        xmlns="http://www.w3.org/2000/svg">
                                                        <path d="M12 8.25V6" stroke="var(--primary-500)"
                                                            stroke-width="1.5" stroke-linecap="round"
                                                            stroke-linejoin="round" />
                                                        <path
                                                            d="M12 6C16.3312 4.5 12 0.75 12 0.75C12 0.75 7.5 4.5 12 6Z"
                                                            stroke="var(--primary-500)" stroke-width="1.5"
                                                            stroke-linecap="round" stroke-linejoin="round" />
                                                        <path
                                                            d="M15.1875 11.8125C15.1875 12.6579 14.8517 13.4686 14.2539 14.0664C13.6561 14.6642 12.8454 15 12 15C11.1546 15 10.3439 14.6642 9.7461 14.0664C9.14832 13.4686 8.8125 12.6579 8.8125 11.8125"
                                                            stroke="var(--primary-500)" stroke-width="1.5"
                                                            stroke-linecap="round" stroke-linejoin="round" />
                                                        <path
                                                            d="M8.81252 11.8125C8.8127 12.6466 8.4859 13.4476 7.90224 14.0435C7.31859 14.6395 6.5246 14.9828 5.69064 15C3.90002 15.0375 2.43752 13.5375 2.43752 11.7469V10.5C2.43628 10.2042 2.49363 9.91106 2.60626 9.63752C2.7189 9.36397 2.88458 9.11544 3.09376 8.90626C3.30294 8.69708 3.55147 8.5314 3.82502 8.41876C4.09856 8.30613 4.3917 8.24878 4.68752 8.25002H19.3125C19.6083 8.24878 19.9015 8.30613 20.175 8.41876C20.4486 8.5314 20.6971 8.69708 20.9063 8.90626C21.1155 9.11544 21.2811 9.36397 21.3938 9.63752C21.5064 9.91106 21.5638 10.2042 21.5625 10.5V11.7469C21.5625 13.5375 20.1 15.0375 18.3094 15C17.4754 14.9828 16.6814 14.6395 16.0978 14.0435C15.5141 13.4476 15.1873 12.6466 15.1875 11.8125"
                                                            stroke="var(--primary-500)" stroke-width="1.5"
                                                            stroke-linecap="round" stroke-linejoin="round" />
                                                        <path
                                                            d="M20.25 14.3721V19.5002C20.25 19.6991 20.171 19.8899 20.0303 20.0305C19.8897 20.1712 19.6989 20.2502 19.5 20.2502H4.5C4.30109 20.2502 4.11032 20.1712 3.96967 20.0305C3.82902 19.8899 3.75 19.6991 3.75 19.5002V14.3721"
                                                            stroke="var(--primary-500)" stroke-width="1.5"
                                                            stroke-linecap="round" stroke-linejoin="round" />
                                                    </svg>

                                                </div>
                                                <h3 class="sub-title text-uppercase">{{ __('date_of_birth') }}</h3>
                                                <h2 class="title" id="candidate_birth_date">
                                                    {{ candidate.birth_date ? candidate.birth_date : '-' }}</h2>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="icon-box">
                                                <div class="icon-img">
                                                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none"
                                                        xmlns="http://www.w3.org/2000/svg">
                                                        <path d="M9 14.25H15" stroke="var(--primary-500)"
                                                            stroke-width="1.5" stroke-linecap="round"
                                                            stroke-linejoin="round" />
                                                        <path d="M9 11.25H15" stroke="var(--primary-500)"
                                                            stroke-width="1.5" stroke-linecap="round"
                                                            stroke-linejoin="round" />
                                                        <path
                                                            d="M15.0002 3.75H18.75C18.9489 3.75 19.1397 3.82902 19.2803 3.96967C19.421 4.11032 19.5 4.30109 19.5 4.5V20.25C19.5 20.4489 19.421 20.6397 19.2803 20.7803C19.1397 20.921 18.9489 21 18.75 21H5.25C5.05109 21 4.86032 20.921 4.71967 20.7803C4.57902 20.6397 4.5 20.4489 4.5 20.25V4.5C4.5 4.30109 4.57902 4.11032 4.71967 3.96967C4.86032 3.82902 5.05109 3.75 5.25 3.75H8.9998"
                                                            stroke="var(--primary-500)" stroke-width="1.5"
                                                            stroke-linecap="round" stroke-linejoin="round" />
                                                        <path
                                                            d="M8.25 6.75V6C8.25 5.00544 8.64509 4.05161 9.34835 3.34835C10.0516 2.64509 11.0054 2.25 12 2.25C12.9946 2.25 13.9484 2.64509 14.6517 3.34835C15.3549 4.05161 15.75 5.00544 15.75 6V6.75H8.25Z"
                                                            stroke="var(--primary-500)" stroke-width="1.5"
                                                            stroke-linecap="round" stroke-linejoin="round" />
                                                    </svg>

                                                </div>
                                                <h3 class="sub-title">{{ __('marital_status') }}</h3>
                                                <h2 class="title tw-capitalize">
                                                    {{ candidate.marital_status ? candidate.marital_status : '-' }}
                                                </h2>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="icon-box">
                                                <div class="icon-img">
                                                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none"
                                                        xmlns="http://www.w3.org/2000/svg">
                                                        <path
                                                            d="M12 21C16.9706 21 21 16.9706 21 12C21 7.02944 16.9706 3 12 3C7.02944 3 3 7.02944 3 12C3 16.9706 7.02944 21 12 21Z"
                                                            stroke="var(--primary-500)" stroke-width="1.5"
                                                            stroke-miterlimit="10" />
                                                        <path
                                                            d="M12 15C14.0711 15 15.75 13.3211 15.75 11.25C15.75 9.17893 14.0711 7.5 12 7.5C9.92893 7.5 8.25 9.17893 8.25 11.25C8.25 13.3211 9.92893 15 12 15Z"
                                                            stroke="var(--primary-500)" stroke-width="1.5"
                                                            stroke-miterlimit="10" />
                                                        <path
                                                            d="M5.98145 18.6913C6.54639 17.5806 7.40768 16.6478 8.46997 15.9963C9.53226 15.3448 10.7541 15 12.0003 15C13.2464 15 14.4683 15.3448 15.5306 15.9963C16.5929 16.6478 17.4542 17.5806 18.0191 18.6913"
                                                            stroke="var(--primary-500)" stroke-width="1.5"
                                                            stroke-linecap="round" stroke-linejoin="round" />
                                                    </svg>

                                                </div>
                                                <h3 class="sub-title">{{ __('gender') }}</h3>
                                                <h2 class="title">
                                                    {{ candidate.gender ? candidate.gender : '-' }}
                                                </h2>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="icon-box">
                                                <div class="icon-img">
                                                    <i class="ph-suitcase-simple f-size-24 text-primary-500"></i>
                                                </div>
                                                <h3 class="sub-title">{{ __('experience') }}</h3>
                                                <h2 class="title" id="candidate_experience">
                                                    {{ candidate.experience ? candidate.experience.name : '-' }}
                                                </h2>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="icon-box">
                                                <div class="icon-img">
                                                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none"
                                                        xmlns="http://www.w3.org/2000/svg">
                                                        <path d="M0.75 9L12 3L23.25 9L12 15L0.75 9Z"
                                                            stroke="var(--primary-500)" stroke-width="1.5"
                                                            stroke-linecap="round" stroke-linejoin="round" />
                                                        <path d="M17.625 22.5V12L12 9" stroke="var(--primary-500)"
                                                            stroke-width="1.5" stroke-linecap="round"
                                                            stroke-linejoin="round" />
                                                        <path
                                                            d="M20.625 10.4004V15.5117C20.6253 15.6735 20.573 15.831 20.476 15.9605C19.8444 16.8009 17.18 19.8754 12 19.8754C6.82004 19.8754 4.15558 16.8009 3.52402 15.9605C3.42699 15.831 3.37469 15.6735 3.375 15.5117V10.4004"
                                                            stroke="var(--primary-500)" stroke-width="1.5"
                                                            stroke-linecap="round" stroke-linejoin="round" />
                                                    </svg>

                                                </div>
                                                <h3 class="sub-title">{{ __('education') }}</h3>
                                                <h2 class="title">
                                                    {{ candidate.education ? candidate.education.name : '-' }}
                                                </h2>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="sidebar-widget">
                                    <div class="contact">
                                        <h2 class="title">{{ __('contact_information') }}</h2>
                                        <div v-if="candidate.whatsapp_number">
                                            <a target="_blank" :href="'https://wa.me/' + candidate.whatsapp_number"
                                                class="whatsapp-button" id="contact_whatsapp">
                                                <svg width="18" height="18" viewBox="0 0 24 24" fill="none">
                                                    <path
                                                        d="M11.4182 0.0138947C5.07986 0.315691 0.077995 5.59432 0.0972232 11.9399C0.103078 13.8726 0.569664 15.6968 1.39275 17.3086L0.129022 23.4429C0.0606609 23.7747 0.359933 24.0652 0.689568 23.9872L6.70043 22.5631C8.24472 23.3323 9.97927 23.7766 11.815 23.8046C18.2935 23.9036 23.6953 18.7596 23.8972 12.2836C24.1137 5.34172 18.3844 -0.317864 11.4182 0.0138947ZM18.5905 18.4934C16.8302 20.2537 14.4896 21.2231 12.0002 21.2231C10.5425 21.2231 9.14695 20.8961 7.85212 20.2511L7.01503 19.8341L3.32975 20.7072L4.10548 16.9415L3.69308 16.134C3.02089 14.8175 2.68006 13.394 2.68006 11.903C2.68006 9.4135 3.64951 7.07306 5.40984 5.31267C7.15445 3.56807 9.53312 2.58284 12.0004 2.58284C14.4898 2.5829 16.8302 3.55234 18.5905 5.31261C20.3509 7.07294 21.3203 9.41339 21.3204 11.9028C21.3203 14.3701 20.3351 16.7488 18.5905 18.4934Z"
                                                        fill="#00C236"></path>
                                                    <path
                                                        d="M17.777 14.4844L15.4714 13.8224C15.1683 13.7353 14.8419 13.8213 14.621 14.0464L14.0572 14.6208C13.8194 14.863 13.4587 14.9409 13.144 14.8136C12.0534 14.3722 9.75907 12.3323 9.17316 11.3119C9.00412 11.0176 9.03207 10.6496 9.23962 10.381L9.73187 9.74418C9.92472 9.49467 9.96542 9.15958 9.83788 8.87116L8.86786 6.67724C8.63551 6.15176 7.96402 5.9988 7.52532 6.36982C6.88184 6.91407 6.11833 7.74111 6.02552 8.65735C5.86188 10.2728 6.55467 12.3091 9.17448 14.7543C12.2011 17.5791 14.6247 17.9523 16.2028 17.57C17.0978 17.3532 17.8131 16.4841 18.2646 15.7725C18.5723 15.2873 18.3293 14.643 17.777 14.4844Z"
                                                        fill="#00C236"></path>
                                                </svg>
                                                <span style="margin-left: 5px;">WhatsApp</span>
                                            </a>
                                            <div class="devider"></div>
                                        </div>
                                        <div class="contact-icon-box">
                                            <div class="icon-img">
                                                <svg width="24" height="24" viewBox="0 0 32 32" fill="none"
                                                    xmlns="http://www.w3.org/2000/svg">
                                                    <path
                                                        d="M16 28C22.6274 28 28 22.6274 28 16C28 9.37258 22.6274 4 16 4C9.37258 4 4 9.37258 4 16C4 22.6274 9.37258 28 16 28Z"
                                                        fill="none" stroke="var(--primary-500)" stroke-width="2"
                                                        stroke-miterlimit="10" />
                                                    <path d="M4 16H28" stroke="var(--primary-500)" stroke-width="2"
                                                        stroke-linecap="round" stroke-linejoin="round" />
                                                    <path
                                                        d="M16 27.678C18.7614 27.678 21 22.4496 21 16.0001C21 9.55062 18.7614 4.32227 16 4.32227C13.2386 4.32227 11 9.55062 11 16.0001C11 22.4496 13.2386 27.678 16 27.678Z"
                                                        stroke="var(--primary-500)" stroke-width="2"
                                                        stroke-miterlimit="10" />
                                                </svg>

                                            </div>
                                            <div class="info">
                                                <h3 class="subtitle">{{ __('website') }}</h3>
                                                <h2 class="title">
                                                    {{ candidate . website ? candidate . website : '-' }}
                                                </h2>
                                            </div>
                                        </div>
                                        <div class="devider"></div>
                                        <div class="contact-icon-box">
                                            <div class="icon-img">
                                                <svg width="24" height="24" viewBox="0 0 32 32" fill="none"
                                                    xmlns="http://www.w3.org/2000/svg">
                                                    <path opacity="0.2"
                                                        d="M16 3C13.3478 3.00001 10.8043 4.05358 8.92894 5.92894C7.05358 7.8043 6.00001 10.3478 6 13C6 22 16 29 16 29C16 29 26 22 26 13C26 10.3478 24.9464 7.8043 23.0711 5.92894C21.1957 4.05358 18.6522 3.00001 16 3ZM16 17C15.2089 17 14.4355 16.7654 13.7777 16.3259C13.1199 15.8864 12.6072 15.2616 12.3045 14.5307C12.0017 13.7998 11.9225 12.9956 12.0769 12.2196C12.2312 11.4437 12.6122 10.731 13.1716 10.1716C13.731 9.61216 14.4437 9.2312 15.2196 9.07686C15.9956 8.92252 16.7998 9.00173 17.5307 9.30448C18.2616 9.60723 18.8864 10.1199 19.3259 10.7777C19.7654 11.4355 20 12.2089 20 13C20 14.0609 19.5786 15.0783 18.8284 15.8284C18.0783 16.5786 17.0609 17 16 17V17Z"
                                                        fill="var(--primary-500)" />
                                                    <path d="M7 29H25" stroke="var(--primary-500)" stroke-width="1.8"
                                                        stroke-linecap="round" stroke-linejoin="round" />
                                                    <path
                                                        d="M16 17C18.2091 17 20 15.2091 20 13C20 10.7909 18.2091 9 16 9C13.7909 9 12 10.7909 12 13C12 15.2091 13.7909 17 16 17Z"
                                                        stroke="var(--primary-500)" stroke-width="1.8"
                                                        stroke-linecap="round" stroke-linejoin="round" />
                                                    <path
                                                        d="M26 13C26 22 16 29 16 29C16 29 6 22 6 13C6 10.3478 7.05357 7.8043 8.92893 5.92893C10.8043 4.05357 13.3478 3 16 3C18.6522 3 21.1957 4.05357 23.0711 5.92893C24.9464 7.8043 26 10.3478 26 13V13Z"
                                                        stroke="var(--primary-500)" stroke-width="1.8"
                                                        stroke-linecap="round" stroke-linejoin="round" />
                                                </svg>

                                            </div>
                                            <div class="info">
                                                <h3 class="subtitle">{{ __('location') }}</h3>
                                                <h2 class="title" id="candidate_location">
                                                    {{ data . contact_info && data . contact_info . country ? data .
                                                    contact_info . country . name : '-' }}
                                                </h2>
                                            </div>
                                        </div>
                                        <div class="devider"></div>
                                        <div class="contact-icon-box">
                                            <div class="icon-img">
                                                <svg width="24" height="24" viewBox="0 0 32 32" fill="none"
                                                    xmlns="http://www.w3.org/2000/svg">
                                                    <path opacity="0.2"
                                                        d="M11.5595 15.6025C12.5968 17.7232 14.3158 19.4344 16.4412 20.462C16.5967 20.5357 16.7687 20.5676 16.9403 20.5546C17.1119 20.5417 17.2771 20.4842 17.4198 20.388L20.5492 18.3012C20.6877 18.2089 20.8469 18.1526 21.0126 18.1374C21.1782 18.1221 21.3451 18.1485 21.498 18.214L27.3526 20.7231C27.5515 20.8076 27.7175 20.9545 27.8257 21.1415C27.9339 21.3286 27.9783 21.5457 27.9524 21.7602C27.7673 23.2082 27.0608 24.5392 25.9652 25.5038C24.8695 26.4684 23.4598 27.0005 22 27.0006C17.4913 27.0006 13.1673 25.2095 9.97919 22.0214C6.79107 18.8333 5 14.5093 5 10.0006C5.00008 8.54083 5.53224 7.13113 6.49685 6.03546C7.46146 4.93979 8.79237 4.23328 10.2404 4.04824C10.4549 4.02228 10.672 4.06673 10.8591 4.17491C11.0461 4.28309 11.193 4.44913 11.2775 4.64801L13.7888 10.5077C13.8537 10.6593 13.8802 10.8246 13.8658 10.9889C13.8514 11.1531 13.7967 11.3113 13.7064 11.4493L11.6268 14.6267C11.5322 14.7697 11.4762 14.9347 11.4644 15.1058C11.4526 15.2768 11.4854 15.4479 11.5595 15.6025Z"
                                                        fill="var(--primary-500)" />
                                                    <path
                                                        d="M11.5595 15.6025C12.5968 17.7232 14.3158 19.4344 16.4412 20.462C16.5967 20.5357 16.7687 20.5676 16.9403 20.5546C17.1119 20.5417 17.2771 20.4842 17.4198 20.388L20.5492 18.3012C20.6877 18.2089 20.8469 18.1526 21.0126 18.1374C21.1782 18.1221 21.3451 18.1485 21.498 18.214L27.3526 20.7231C27.5515 20.8076 27.7175 20.9545 27.8257 21.1415C27.9339 21.3286 27.9783 21.5457 27.9524 21.7602C27.7673 23.2082 27.0608 24.5391 25.9652 25.5038C24.8695 26.4684 23.4598 27.0005 22 27.0006C17.4913 27.0006 13.1673 25.2095 9.97919 22.0214C6.79107 18.8333 5 14.5093 5 10.0006C5.00008 8.54083 5.53224 7.13113 6.49685 6.03546C7.46146 4.93979 8.79237 4.23328 10.2404 4.04824C10.4549 4.02228 10.672 4.06673 10.8591 4.17491C11.0461 4.28309 11.193 4.44913 11.2775 4.64801L13.7888 10.5077C13.8537 10.6593 13.8802 10.8246 13.8658 10.9889C13.8514 11.1531 13.7967 11.3113 13.7064 11.4493L11.6268 14.6267C11.5322 14.7697 11.4762 14.9347 11.4644 15.1058C11.4526 15.2768 11.4854 15.4479 11.5595 15.6025V15.6025Z"
                                                        stroke="var(--primary-500)" stroke-width="1.8"
                                                        stroke-linecap="round" stroke-linejoin="round" />
                                                    <path
                                                        d="M19.9268 5C21.622 5.45592 23.1677 6.34928 24.409 7.59059C25.6503 8.8319 26.5437 10.3776 26.9996 12.0728"
                                                        stroke="var(--primary-500)" stroke-width="1.8"
                                                        stroke-linecap="round" stroke-linejoin="round" />
                                                    <path
                                                        d="M18.8916 8.86523C19.9087 9.13879 20.8362 9.6748 21.5809 10.4196C22.3257 11.1644 22.8618 12.0918 23.1353 13.1089"
                                                        stroke="var(--primary-500)" stroke-width="1.8"
                                                        stroke-linecap="round" stroke-linejoin="round" />
                                                </svg>

                                            </div>
                                            <div class="info">
                                                <h3 class="subtitle">{{ __('phone') }}</h3>
                                                <h2 class="title" id="candidate_phone">
                                                    {{ data . contact_info && data . contact_info . phone ? data .
                                                    contact_info . phone : '-' }}
                                                </h2>
                                                <h3 class="subtitle">{{ __('secondary_phone') }}</h3>
                                                <h2 class="title" id="candidate_seconday_phone">
                                                    {{ data . contact_info && data . contact_info . secondary_phone ?
                                                    data .
                                                    contact_info . secondary_phone : '-' }}
                                                </h2>
                                            </div>
                                        </div>
                                        <div class="devider"></div>
                                        <div class="contact-icon-box">
                                            <div class="icon-img">
                                                <svg width="24" height="24" viewBox="0 0 24 24" fill="none"
                                                    xmlns="http://www.w3.org/2000/svg">
                                                    <path d="M21 5.25L12 13.5L3 5.25" stroke="var(--primary-500)"
                                                        stroke-width="1.5" stroke-linecap="round"
                                                        stroke-linejoin="round" />
                                                    <path
                                                        d="M3 5.25H21V18C21 18.1989 20.921 18.3897 20.7803 18.5303C20.6397 18.671 20.4489 18.75 20.25 18.75H3.75C3.55109 18.75 3.36032 18.671 3.21967 18.5303C3.07902 18.3897 3 18.1989 3 18V5.25Z"
                                                        stroke="var(--primary-500)" stroke-width="1.5"
                                                        stroke-linecap="round" stroke-linejoin="round" />
                                                    <path d="M10.3628 12L3.23047 18.538" stroke="var(--primary-500)"
                                                        stroke-width="1.5" stroke-linecap="round"
                                                        stroke-linejoin="round" />
                                                    <path d="M20.7692 18.5381L13.6367 12" stroke="var(--primary-500)"
                                                        stroke-width="1.5" stroke-linecap="round"
                                                        stroke-linejoin="round" />
                                                </svg>
                                            </div>
                                            <div class="info">
                                                <h3 class="subtitle">{{ __('email_address') }}</h3>
                                                <h2 class="title" id="contact_info_email">
                                                    {{ data . contact_info && data . contact_info . email ? data .
                                                    contact_info . email : '-' }}
                                                </h2>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <button type="button" class="btn-close" @click="$emit('close-modal')"></button>
                </div>
            </div>
            <transition name="fade">
                <div v-if="candidateMessageModal" class="tw-fixed tw-top-0 tw-left-0 tw-right-0 tw-z-50 tw-p-4 tw-overflow-y-auto md:tw-inset-0 h-modal md:tw-h-full tw-flex tw-items-center tw-justify-center tw-bg-gray-800 tw-bg-opacity-90 modal fade show" id="messageModal" tabindex="-1" role="dialog" aria-labelledby="exampleModalLabel"
                    aria-hidden="true" style="display: block;">
                    <div class="modal-dialog modal-wrapper !tw-max-w-[648px]">
                        <div class="modal-content">
                            <div class="chat-top tw-rounded-tl-xl tw-rounded-tr-xl tw-p-6 tw-bg-[#F1F2F4]">
                                <h2 class="tw-text-base tw-font-semibold tw-text-[#18191C] tw-mt-0 tw-mb-3">{{ __('message_candidate') }}</h2>
                                <div class="tw-flex tw-gap-3 tw-mb-3 tw-items-center">
                                    <img class="tw-w-[48px] tw-h-[48px] tw-rounded-full tw-object-cover"
                                    v-if="candidate.photo" :src="candidate.photo" alt="candidate img">
                                    <div>
                                        <h3 class="tw-mt-0 tw-mb-1 tw-font-medium tw-text-sm tw-text-[#18191C]">
                                            {{ data.name }}
                                        </h3>
                                        <p class="tw-mb-0 tw-text-sm tw-text-[#5E6670]">
                                            {{ candidate.profession ? candidate.profession.name:'-' }}
                                        </p>
                                    </div>
                                </div>
                                <a href="javascript:void(0)"
                                    class="tw-flex tw-items-center tw-gap-1.5 tw-text-[#0066CC] tw-text-base tw-font-semibold tw-mb-0">
                                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                        <rect x="3" y="8" width="18" height="12" rx="1" stroke="#0066CC" stroke-width="2" />
                                        <rect x="8" y="4" width="8" height="4" rx="1" stroke="#0066CC" stroke-width="2" />
                                    </svg>
                                    <span>{{ job?.title ?? '' }}</span>
                                </a>
                            </div>
                            <div class="chat-bottom tw-p-6 tw-bg-white tw-rounded-bl-xl tw-rounded-br-xl">
                                <form @submit.prevent="sendMessage()">
                                    <div class="form-group">
                                        <label for="chat" class="tw-text-sm tw-text-[#18191C] tw-mb-2">{{ __('your_message') }}</label>
                                        <textarea v-model="message" id="chat" :placeholder="__('write_your_message')" value="" class="form-control" cols="1"
                                            rows="4"></textarea>
                                        <span class="invalid-feedback" role="alert"></span>
                                    </div>
                                    <div class="tw-mt-6 tw-flex tw-justify-between tw-items-center">
                                        <button @click="candidateMessageModal = false" type="button" class="btn tw-py-[11px] tw-px-[23px] tw-text-base tw-capitalize tw-font-semibold tw-text-[#474C54] tw-border tw-border-[#E4E5E8]">{{ __('cancel') }}</button>
                                        <button type="submit" class="btn tw-py-[11px] tw-text-base tw-capitalize tw-font-semibold tw-text-white tw-bg-[#0066CC] hover:tw-bg-transparent tw-border-black tw-px-[23px]">
                                            <loading-icon v-if="msgloading" />
                                            <span v-else>{{ __('send_message') }}</span>
                                        </button>
                                    </div>
                                </form>
                            </div>
                            <button type="button" class="btn-close" @click="candidateMessageModal = false"></button>
                        </div>
                    </div>
                </div>
            </transition>
        </div>
    </transition>
</template>

<script>
    import LoadingIcon from '../SvgIcon/LoadingIcon.vue';

    export default {
        components: {
            LoadingIcon,
        },
        props: {
            show: {
                type: Boolean,
                required: true
            },
            response: {
                type: Object,
                required: true
            },
            answers: {
                type: Array,
            },
            language: {
                type: Object,
                required: false
            },
            job: {
                type: Object,
                required: false
            },
            messagebutton: {
                type: Boolean,
                required: true,
                default: true
            }
        },
        data() {
            return {
                show: false,
                data: {},
                social: {},
                candidate: {},
                languageTranslation: {},
                candidateMessageModal: false,
                message: '',
                msgloading: false,
            }
        },
        methods: {
            __(key) {
                if (this.languageTranslation) {
                    return this.languageTranslation[key] || key;
                }

                return key;
            },
            truncateText(text, words) {
                if (!text) return '';

                const wordArray = text.trim().split(' ');
                const truncatedText = wordArray.slice(0, words).join(' ');

                if (wordArray.length > words) {
                    return truncatedText + '...';
                }

                return truncatedText;
            },
            messageCandidate(){
                // Cek apakah URL mengandung parameter from_recruitment=true atau job_id
                const urlParams = new URLSearchParams(window.location.search);
                const fromRecruitment = urlParams.get('from_recruitment') === 'true';
                const jobId = urlParams.get('job');

                // Cek juga apakah URL mengandung 'lamaran-kerja-simple'
                const isFromRecruitmentPage = window.location.href.includes('lamaran-kerja-simple');

                // Jika tidak diakses dari halaman rekrutmen, tampilkan pesan peringatan
                if (!fromRecruitment && !isFromRecruitmentPage && !jobId) {
                    alert('Perusahaan hanya dapat mengirim pesan ke pencaker jika pencaker telah melamar pekerjaan. Silakan akses fitur ini melalui halaman rekrutmen.');
                    return;
                }

                this.candidateMessageModal = true;
            },
            async sendMessage(){
                if (!this.message) {
                    alert('Please write a message to send!');
                    return;
                }

                if (!this.candidate || !this.candidate.id) {
                    alert('Candidate information is missing. Please try again.');
                    return;
                }

                this.msgloading = true;

                try {
                    const payload = {
                        message: this.message,
                        candidate_id: this.candidate.id,
                        job_id: this.job ? this.job.id : null
                    };

                    console.log('Sending message with payload:', payload);

                    let response = await axios.post('/company/message/candidate', payload);

                    if (response.data.success) {
                        alert('Message sent successfully!');
                        this.candidateMessageModal = false;
                        this.message = '';
                    } else {
                        alert(response.data.message || 'Something went wrong while trying to send message! Please try again');
                    }
                } catch (error) {
                    console.error('Error sending message:', error);

                    if (error.response && error.response.data && error.response.data.message) {
                        alert('Error: ' + error.response.data.message);
                    } else {
                        alert('Something went wrong while trying to send message! Please try again');
                    }
                } finally {
                    this.msgloading = false;
                }
            }
        },
        watch: {
            response() {
                this.data = this.response.data
                this.social = this.response.data.social_info
                this.candidate = this.response.data.candidate
            }
        },
        mounted() {
            this.data = this.response.data
            this.social = this.response.data.social_info
            this.candidate = this.response.data.candidate
            this.languageTranslation = this.language
        },
        computed: {
            sanitizedBio() {
                const parser = new DOMParser();
                const doc = parser.parseFromString(this.candidate.bio, 'text/html');
                return doc.body.textContent;
            }
        }
    }
</script>

<style scoped>
    .whatsapp-button {
        background: rgb(243, 243, 243);
        border-radius: 35px;
        display: flex;
        -webkit-box-flex: 2;
        flex-grow: 2;
        -webkit-box-pack: center;
        justify-content: center;
        -webkit-box-align: center;
        align-items: center;
        height: 40px;
        border: 1px solid rgb(223, 223, 223);
        width: 144px;
        max-width: 160px;
        margin-top: 10px;
        margin-bottom: 10px;
        font-size: 15px;
    }

    /* Style for truncated text */
    td.truncated-notes {
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        cursor: pointer;
    }

    /* Style for displaying full text on hover */
    td.truncated-notes:hover {
        white-space: normal;
        overflow: visible;
    }
</style>
