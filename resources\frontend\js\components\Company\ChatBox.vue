<template>
    <div>
        <div class="tw-flex tw-gap-2.5 tw-items-center tw-mt-6 tw-mb-4">
            <a href="/user/dashboard">
                <LeftArrowIcon />
            </a>
            <h3 class="tw-mb-0 tw-text-2xl tw-text-[#18191C] tw-font-medium">
                {{ __('messenger') }}
                <span v-if="total_unread_count && total_unread_count > 0">({{ total_unread_count }} {{ __('unread')
                    }})</span>
            </h3>
        </div>
        <div class="row g-3 tw-mb-16">
            <div class="col-lg-4">
                <div class="tw-py-5 tw-rounded-lg chat-box-card">
                    <div class="tw-px-4 tw-pb-4">
                        <div class="tw-flex tw-mb-3 tw-justify-between tw-items-center">
                            <h3 class="tw-mb-0 tw-text-lg tw-text-[#161719] tw-font-medium">{{ __('filter_by') }} {{
                                __('job') }}</h3>
                            <div class="custom-checkbox">
                                <input @change="showUnreadList($event)" ref="unreadCheckbox" type="checkbox" id="unread"
                                    hidden>
                                <label for="unread" class="tw-text-[#474C54] tw-text-base tw-cursor-pointer">{{
                                    __('unread') }}</label>
                            </div>
                        </div>
                        <div class="tw-relative">
                            <select @change="filterData()" v-model="filter.job"
                                class="rt-selectactive tw-pl-10 tw-rounded-md select-job" name="" required>
                                <option value="" selected>{{ __('all_job') }}</option>
                                <option :value="job.id" v-for="job in jobs" :key="job.id">{{ job.title }}</option>
                            </select>
                            <span
                                class="tw-absolute tw-inline-flex tw-justify-center tw-items-center tw-z-[998] tw-left-3 tw-top-1/2 -tw-translate-y-1/2">
                                <BriefcaseIcon />
                            </span>
                        </div>
                    </div>
                    <ul class="tw-p-0 tw-m-0 tw-list-none scrollbar-hide tw-overflow-auto tw-h-[856px]">
                        <li v-for="user in users_list" :key="user.id">
                            <div v-if="auth.role == 'company'"
                                class="tw-flex tw-gap-3 tw-px-4 tw-py-3 tw-items-center tw-cursor-pointer   "
                                :class="!user.last_message_from_me && user.unread_count != 0 ? 'tw-bg-[#FAEBEB]':'tw-bg-white'"
                                @click="getMessages(user)">
                                <img class="tw-w-10 tw-h-10 tw-rounded-full tw-object-cover"
                                    :src="user?.candidate?.photo" alt="user image">
                                <div class="tw-flex-grow">
                                    <h3 class="tw-mb-0.5 tw-text-md tw-text-base tw-font-medium tw-text-[#18191C]">
                                        {{ user?.candidate?.user?.name ?? 'No User' }}
                                    </h3>
                                    <p class="tw-mb-0 tw-text-sm tw-text-[#18191C]">
                                        {{ __('job') }}: {{ user?.job?.title ?? 'No Job' }}
                                    </p>
                                    <p class="tw-mb-0 tw-text-xs tw-text-[#474C54] tw-font-medium">
                                        {{ user?.latest_message ?? '' }}
                                    </p>
                                </div>
                                <div class="tw-flex tw-w-[48px] tw-flex-col tw-gap-1.5 tw-items-end tw-justify-end">
                                    <span class="tw-h-[20px]"
                                        :class="!user.last_message_from_me && user.unread_count != 0 ? 'tw-w-[22px] tw-text-white tw-inline-flex tw-justify-center tw-items-center tw-rounded-full tw-text-sm tw-bg-[#C73333]':''">
                                        {{ user.unread_count ? user.unread_count:'' }}
                                    </span>
                                    <span class="tw-text-sm tw-text-[#9199A3]">{{ user.latest_message_humans_time
                                        }}</span>
                                </div>
                            </div>
                            <div v-else class="tw-flex tw-gap-3 tw-px-4 tw-py-3 tw-items-center tw-cursor-pointer   "
                                :class="!user.last_message_from_me && user.unread_count != 0 ? 'tw-bg-[#FAEBEB]':'tw-bg-white'"
                                @click="getMessages(user)">
                                <img class="tw-w-10 tw-h-10 tw-rounded-full tw-object-cover"
                                    :src="user?.company?.logo_url" alt="user image">
                                <div class="tw-flex-grow">
                                    <h3 class="tw-mb-0.5 tw-text-md tw-text-base tw-font-semi-medium tw-text-[#18191C]">
                                        {{ user?.company?.user?.name ?? 'No User' }}
                                    </h3>
                                    <p class="tw-mb-0 tw-text-sm tw-text-[#18191C]">
                                        {{ __('job') }}: {{ user?.job?.title ?? 'No Job' }}
                                    </p>
                                    <p class="tw-mb-0 tw-text-xs tw-text-[#474C54] tw-font-medium">
                                        {{ user?.latest_message ?? '' }}
                                    </p>
                                </div>
                                <div class="tw-flex tw-w-[48px] tw-flex-col tw-gap-1.5 tw-items-end tw-justify-end">
                                    <span class="tw-h-[20px]"
                                        :class="!user.last_message_from_me && user.unread_count != 0 ? 'tw-w-[22px] tw-text-white tw-inline-flex tw-justify-center tw-items-center tw-rounded-full tw-text-sm tw-bg-[#C73333]':''">
                                        {{ user.unread_count ? user.unread_count:'' }}
                                    </span>
                                    <span class="tw-text-sm tw-text-[#9199A3]">{{ user.latest_message_humans_time
                                        }}</span>
                                </div>
                            </div>
                        </li>
                    </ul>
                </div>
            </div>
            <div class="col-lg-8">
                <div class="chat-box-card chat-box-card_detail tw-rounded-xl tw-relative" id="chat-box-card">
                    <div class="tw-p-6 chat-box__detail-top" v-if="selectedUser">
                        <div v-if="selectedUser">
                            <a :href="'/job/'+selectedUser?.job?.slug" target="_blank"
                                class="tw-text-[#18191C] tw-font-medium tw-underline">
                                {{ selectedUser?.job?.title ?? 'No Title' }}
                            </a>
                        </div>
                        <div class="tw-h-[1px] tw-my-3 tw-bg-[#E4E5E8]"></div>
                        <div>
                            <div v-if="auth.role == 'company'" class="tw-flex tw-gap-3 tw-items-center">
                                <img class="tw-w-10 tw-h-10 tw-rounded-full tw-object-cover"
                                    :src="selectedUser?.candidate?.photo" alt="">
                                <div class="tw-flex-grow">
                                    <h3 class="tw-mb-0.5 tw-text-base tw-font-medium tw-text-[#18191C]">
                                        {{ selectedUser?.candidate?.user?.name ?? 'No User' }}
                                    </h3>
                                    <div class="tw-flex tw-gap-3 tw-items-center">
                                        <a @click="previewResume(selectedUser?.candidate?.user?.username)" href="#"
                                            class="tw-text-sm tw-text-[#0A65CC] tw-font-medium">{{ __('view_profile')
                                            }}</a>
                                        <p class="tw-mb-0 tw-flex tw-items-center tw-text-sm">
                                            <span class="tw-text-[#767F8C]">{{ __('stage') }}: </span>
                                            <span class="tw-font-medium tw-text-[#18191C]">&nbsp;{{
                                                selectedUser.application_status }}</span>
                                        </p>
                                    </div>
                                </div>
                            </div>
                            <div v-if="auth.role == 'candidate'" class="tw-flex tw-gap-3 tw-items-center">
                                <img class="tw-w-10 tw-h-10 tw-rounded-full tw-object-cover"
                                    :src="selectedUser?.company?.logo_url" alt="">
                                <div class="tw-flex-grow">
                                    <h3 class="tw-mb-0.5 tw-text-base tw-font-medium tw-text-[#18191C]">
                                        {{ selectedUser?.company?.user?.name ?? 'No User' }}
                                    </h3>
                                    <div class="tw-flex tw-gap-3 tw-items-center">
                                        <a target="_blank" :href="'/employer/'+selectedUser?.company?.user?.username"
                                            class="tw-text-sm tw-text-[#0A65CC] tw-font-medium">{{ __('view_profile')
                                            }}</a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="tw-p-6 tw-overflow-auto tw-h-[696px]" ref="chatbox">
                        <div v-if="selectedUser" class="tw-h-full">
                            <!-- Date separator -->
                            <div v-if="messages.length > 0" class="tw-flex tw-justify-center tw-mb-4">
                                <div class="tw-bg-gray-100 tw-px-4 tw-py-1 tw-rounded-full tw-text-xs tw-text-gray-500">
                                    {{ formatMessageDate(messages[0].created_at) }}
                                </div>
                            </div>

                            <div>
                                <div v-for="(message, index) in messages" :key="message.id">
                                    <!-- Date separator between messages from different days -->
                                    <div v-if="index > 0 && shouldShowDateSeparator(messages[index-1], message)" class="tw-flex tw-justify-center tw-my-4">
                                        <div class="tw-bg-gray-100 tw-px-4 tw-py-1 tw-rounded-full tw-text-xs tw-text-gray-500">
                                            {{ formatMessageDate(message.created_at) }}
                                        </div>
                                    </div>

                                    <!-- Sent message (by current user) -->
                                    <div v-if="auth.id == message.from" class="sent-message tw-flex tw-justify-end">
                                        <div class="tw-mb-4 md:tw-max-w-[70%]">
                                            <!-- Message with link preview if contains URL -->
                                            <div v-if="hasUrl(message.body)" class="tw-bg-primary-500 tw-text-white tw-rounded-tl-md tw-rounded-tr-md tw-rounded-bl-md tw-rounded-br-none tw-overflow-hidden">
                                                <p class="tw-text-block tw-whitespace-pre-wrap tw-text-base tw-py-2 tw-px-3 tw-mb-0 tw-font-medium">
                                                    {{ message.body }}
                                                </p>
                                                <div v-if="message.link_preview" class="tw-border-t tw-border-white/20 tw-bg-black/10">
                                                    <div class="tw-p-2 tw-text-sm">
                                                        <div class="tw-font-medium tw-truncate">{{ message.link_preview.title || 'Link Preview' }}</div>
                                                        <div class="tw-text-white/80 tw-text-xs tw-truncate">{{ message.link_preview.url }}</div>
                                                    </div>
                                                </div>
                                            </div>
                                            <!-- Regular message -->
                                            <p v-else
                                                class="tw-text-block tw-whitespace-pre-wrap tw-bg-primary-500 tw-text-white tw-rounded-tl-md tw-rounded-tr-md tw-rounded-bl-md tw-rounded-br-none tw-text-base tw-py-2 tw-px-3 tw-mb-1 tw-font-medium">
                                                {{ message.body }}
                                            </p>

                                            <!-- Message metadata -->
                                            <div class="tw-flex tw-items-center tw-justify-end tw-gap-1">
                                                <p class="tw-text-xs tw-text-gray-500">
                                                    {{ formatMessageTime(message.created_at) }}
                                                </p>
                                                <!-- Read receipt -->
                                                <span v-if="message.read" class="tw-text-blue-500">
                                                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" viewBox="0 0 16 16">
                                                        <path d="M8.97 4.97a.75.75 0 0 1 1.07 1.05l-3.99 4.99a.75.75 0 0 1-1.08.02L2.324 8.384a.75.75 0 1 1 1.06-1.06l2.094 2.093L8.95 4.992a.252.252 0 0 1 .02-.022zm-.92 5.14.92.92a.75.75 0 0 0 1.079-.02l3.992-4.99a.75.75 0 1 0-1.091-1.028L9.477 9.417l-.485-.486-.943 1.179z"/>
                                                    </svg>
                                                </span>
                                                <span v-else class="tw-text-gray-400">
                                                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" viewBox="0 0 16 16">
                                                        <path d="M10.97 4.97a.75.75 0 0 1 1.07 1.05l-3.99 4.99a.75.75 0 0 1-1.08.02L4.324 8.384a.75.75 0 1 1 1.06-1.06l2.094 2.093 3.473-4.425a.267.267 0 0 1 .02-.022z"/>
                                                    </svg>
                                                </span>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Received message (from other user) -->
                                    <div v-else>
                                        <div class="received-message tw-flex tw-justify-start">
                                            <div class="tw-mb-4 md:tw-max-w-[70%]">
                                                <!-- Message with link preview if contains URL -->
                                                <div v-if="hasUrl(message.body)" class="tw-bg-[#E7F0FA] tw-text-gray-900 tw-rounded-tr-md tw-rounded-br-md tw-rounded-bl-md tw-rounded-tl-none tw-overflow-hidden">
                                                    <p class="tw-text-block tw-whitespace-pre-wrap tw-text-base tw-py-2 tw-px-3 tw-mb-0 tw-font-medium">
                                                        {{ message.body }}
                                                    </p>
                                                    <div v-if="message.link_preview" class="tw-border-t tw-border-gray-200 tw-bg-white/50">
                                                        <div class="tw-p-2 tw-text-sm">
                                                            <div class="tw-font-medium tw-truncate">{{ message.link_preview.title || 'Link Preview' }}</div>
                                                            <div class="tw-text-gray-500 tw-text-xs tw-truncate">{{ message.link_preview.url }}</div>
                                                        </div>
                                                    </div>
                                                </div>
                                                <!-- Regular message -->
                                                <p v-else
                                                    class="tw-text-block tw-whitespace-pre-wrap tw-text-base tw-py-2 tw-px-3 tw-mb-1 tw-rounded-tr-md tw-rounded-br-md tw-rounded-bl-md tw-rounded-tl-none tw-font-medium tw-text-gray-900 tw-bg-[#E7F0FA]">
                                                    {{ message.body }}
                                                </p>

                                                <!-- Message metadata -->
                                                <p class="tw-text-xs tw-text-gray-500 tw-text-left">
                                                    {{ formatMessageTime(message.created_at) }}
                                                </p>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Typing indicator -->
                            <div v-if="isTyping" class="tw-flex tw-justify-start tw-mb-4">
                                <div class="tw-bg-gray-100 tw-rounded-full tw-px-4 tw-py-2">
                                    <div class="tw-flex tw-space-x-1">
                                        <div class="tw-w-2 tw-h-2 tw-bg-gray-400 tw-rounded-full tw-animate-bounce" style="animation-delay: 0s"></div>
                                        <div class="tw-w-2 tw-h-2 tw-bg-gray-400 tw-rounded-full tw-animate-bounce" style="animation-delay: 0.1s"></div>
                                        <div class="tw-w-2 tw-h-2 tw-bg-gray-400 tw-rounded-full tw-animate-bounce" style="animation-delay: 0.2s"></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div v-else class="tw-flex tw-justify-center tw-items-center tw-w-full tw-h-full">
                            <div class="tw-text-center">
                                <div class="tw-mb-4">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="64" height="64" fill="#9CA3AF" viewBox="0 0 16 16">
                                        <path d="M8 15c4.418 0 8-3.134 8-7s-3.582-7-8-7-8 3.134-8 7c0 1.76.743 3.37 1.97 4.6-.097 1.016-.417 2.13-.771 2.966-.079.186.074.394.273.362 2.256-.37 3.597-.938 4.18-1.234A9.06 9.06 0 0 0 8 15z"/>
                                    </svg>
                                </div>
                                <h2 class="tw-text-xl tw-text-gray-600">{{ __('you_dont_have_select_any_message_till_now') }}</h2>
                                <p class="tw-text-gray-500 tw-mt-2">{{ __('select_a_conversation_to_start_messaging') }}</p>
                            </div>
                        </div>
                    </div>
                    <div class="tw-p-4 tw-bg-[#F1F2F4] chate-box__detail-bottom" id="chat-form" v-if="selectedUser">
                        <form @submit.prevent="sendMessage" class="message-form tw-flex tw-gap-2 tw-items-center">
                            <!-- Attachment button -->
                            <div class="tw-relative tw-flex-shrink-0">
                                <button type="button" @click="toggleAttachmentMenu" class="tw-p-2 tw-rounded-full hover:tw-bg-gray-200 tw-transition-colors tw-w-10 tw-h-10 tw-flex tw-items-center tw-justify-center">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" fill="#6B7280" viewBox="0 0 16 16">
                                        <path d="M4.5 3a2.5 2.5 0 0 1 5 0v9a1.5 1.5 0 0 1-3 0V5a.5.5 0 0 1 1 0v7a.5.5 0 0 0 1 0V3a1.5 1.5 0 1 0-3 0v9a2.5 2.5 0 0 0 5 0V5a.5.5 0 0 1 1 0v7a3.5 3.5 0 1 1-7 0V3z"/>
                                    </svg>
                                </button>

                                <!-- Attachment menu -->
                                <div v-if="showAttachmentMenu" class="tw-absolute tw-bottom-12 tw-left-0 tw-bg-white tw-shadow-lg tw-rounded-lg tw-p-2 tw-w-48">
                                    <div class="tw-flex tw-flex-col tw-gap-2">
                                        <label class="tw-flex tw-items-center tw-gap-2 tw-p-2 tw-rounded hover:tw-bg-gray-100 tw-cursor-pointer">
                                            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="#6B7280" viewBox="0 0 16 16">
                                                <path d="M4.502 9a1.5 1.5 0 1 0 0-3 1.5 1.5 0 0 0 0 3z"/>
                                                <path d="M14.002 13a2 2 0 0 1-2 2h-10a2 2 0 0 1-2-2V5A2 2 0 0 1 2 3a2 2 0 0 1 2-2h10a2 2 0 0 1 2 2v8a2 2 0 0 1-1.998 2zM14 2H4a1 1 0 0 0-1 1h9.002a2 2 0 0 1 2 2v7A1 1 0 0 0 15 11V3a1 1 0 0 0-1-1zM2.002 4a1 1 0 0 0-1 1v8l2.646-2.354a.5.5 0 0 1 .63-.062l2.66 1.773 3.71-3.71a.5.5 0 0 1 .577-.094l1.777 1.947V5a1 1 0 0 0-1-1h-10z"/>
                                            </svg>
                                            <span class="tw-text-sm">{{ __('image') }}</span>
                                            <input type="file" class="tw-hidden" accept="image/*" @change="handleFileUpload">
                                        </label>
                                        <label class="tw-flex tw-items-center tw-gap-2 tw-p-2 tw-rounded hover:tw-bg-gray-100 tw-cursor-pointer">
                                            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="#6B7280" viewBox="0 0 16 16">
                                                <path d="M14 4.5V14a2 2 0 0 1-2 2H4a2 2 0 0 1-2-2V2a2 2 0 0 1 2-2h5.5L14 4.5zm-3 0A1.5 1.5 0 0 1 9.5 3V1H4a1 1 0 0 0-1 1v12a1 1 0 0 0 1 1h8a1 1 0 0 0 1-1V4.5h-2z"/>
                                            </svg>
                                            <span class="tw-text-sm">{{ __('document') }}</span>
                                            <input type="file" class="tw-hidden" accept=".pdf,.doc,.docx" @change="handleFileUpload">
                                        </label>
                                    </div>
                                </div>
                            </div>

                            <!-- Emoji picker button -->
                            <div class="tw-relative tw-flex-shrink-0">
                                <button type="button" @click="toggleEmojiPicker" class="tw-p-2 tw-rounded-full hover:tw-bg-gray-200 tw-transition-colors tw-w-10 tw-h-10 tw-flex tw-items-center tw-justify-center">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" fill="#6B7280" viewBox="0 0 16 16">
                                        <path d="M8 16A8 8 0 1 0 8 0a8 8 0 0 0 0 16zM7 6.5C7 7.328 6.552 8 6 8s-1-.672-1-1.5S5.448 5 6 5s1 .672 1 1.5zM4.285 9.567a.5.5 0 0 1 .683.183A3.498 3.498 0 0 0 8 11.5a3.498 3.498 0 0 0 3.032-******** 0 1 1 .866.5A4.498 4.498 0 0 1 8 12.5a4.498 4.498 0 0 1-3.898-******** 0 0 1 .183-.683zM10 8c-.552 0-1-.672-1-1.5S9.448 5 10 5s1 .672 1 1.5S10.552 8 10 8z"/>
                                    </svg>
                                </button>

                                <!-- Emoji picker -->
                                <div v-if="showEmojiPicker" class="tw-absolute tw-bottom-12 tw-left-0 tw-bg-white tw-shadow-lg tw-rounded-lg tw-p-2 tw-w-64 tw-h-48 tw-overflow-y-auto">
                                    <div class="tw-grid tw-grid-cols-8 tw-gap-1">
                                        <button
                                            v-for="emoji in emojis"
                                            :key="emoji"
                                            @click="addEmoji(emoji)"
                                            class="tw-p-1 tw-text-xl hover:tw-bg-gray-100 tw-rounded"
                                            type="button"
                                        >
                                            {{ emoji }}
                                        </button>
                                    </div>
                                </div>
                            </div>

                            <!-- Message input -->
                            <div class="tw-flex-grow tw-relative tw-min-w-0">
                                <textarea
                                    placeholder="Write your message"
                                    class="tw-pl-4 tw-pr-4 tw-py-2 hover:tw-border-[#0A65CC] focus:tw-bg-[#E7F0FA] hover:tw-bg-[#E7F0FA] tw-w-full tw-rounded-lg tw-resize-none tw-border tw-border-gray-300 focus:tw-outline-none focus:tw-border-[#0A65CC] tw-text-sm"
                                    v-model="message"
                                    @keydown.shift.enter="addNewLine"
                                    @keydown="handleTyping"
                                    @keydown.enter.prevent="sendMessage"
                                    ref="messageInput"
                                    rows="1"
                                ></textarea>
                            </div>

                            <!-- Send button -->
                            <div class="tw-flex-shrink-0">
                                <button
                                    :disabled="loading || !message.trim()"
                                    :class="writingStart ? '!tw-bg-[#0A65CC] hover:!tw-bg-[#0A55BB]' : '!tw-bg-gray-400 !tw-cursor-not-allowed'"
                                    type="submit"
                                    class="tw-rounded-full tw-w-10 tw-h-10 tw-flex tw-justify-center tw-items-center tw-text-white tw-transition-colors"
                                >
                                    <loading-icon v-if="loading" />
                                    <send-icon v-else />
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>

        <!-- Profile modal  -->
        <CandidateResumeModal v-if="showCandidateResumeModal" :response="candidateResumeInformation" :answers="answers"
            :show="showCandidateResumeModal" @close-modal="showCandidateResumeModal = false"
            :language="languageTranslation" :job="job" :messagebutton="false" />
    </div>
</template>

<script>
    import SendIcon from '../SvgIcon/SendIcon.vue';
    import LoadingIcon from '../SvgIcon/LoadingIcon.vue';
    import ChatIcon from '../SvgIcon/ChatIcon.vue';
    import BriefcaseIcon from '../SvgIcon/BriefcaseIcon.vue';
    import LeftArrowIcon from '../SvgIcon/LeftArrowIcon.vue';
    import CandidateResumeModal from "./CandidateResumeModal.vue";

    export default {
        components: {
            SendIcon,
            LoadingIcon,
            ChatIcon,
            BriefcaseIcon,
            LeftArrowIcon,
            CandidateResumeModal
        },
        props: {
            users: Object,
            auth: Object,
            jobs: Array,
        },
        data() {
            return {
                users_list: this.users,
                isMessageClick: false,
                loading: false,
                messages: [],
                selectedUser: '',
                message: '',
                languageTranslation: [],
                total_unread_count: 0,
                showCandidateResumeModal: false,
                candidateResumeInformation: '',
                answers: [],
                isTyping: false,
                typingTimeout: null,
                showAttachmentMenu: false,
                showEmojiPicker: false,
                emojis: ['😀', '😃', '😄', '😁', '😆', '😅', '😂', '🤣', '😊', '😇', '🙂', '🙃', '😉', '😌', '😍', '🥰', '😘', '😗', '😙', '😚', '😋', '😛', '😝', '😜', '🤪', '🤨', '🧐', '🤓', '😎', '🤩', '🥳', '😏', '😒', '😞', '😔', '😟', '😕', '🙁', '☹️', '😣', '😖', '😫', '😩', '🥺', '😢', '😭', '😤', '😠', '😡', '🤬', '🤯', '😳', '🥵', '🥶', '😱', '😨', '😰', '😥', '😓', '🤗', '🤔', '🤭', '🤫', '🤥', '😶', '😐', '😑', '😬', '🙄', '😯', '😦', '😧', '😮', '😲', '🥱', '😴', '🤤', '😪', '😵', '🤐', '🥴', '🤢', '🤮', '🤧', '😷', '🤒', '🤕'],

                filter: {
                    job: '',
                    role: this.auth.role,
                },

                writingStart: false,
                typingIndicatorTimeout: null,
            }
        },
        watch: {
            message() {
                if (this.message.length > 0) {
                    this.writingStart = true
                } else {
                    this.writingStart = false
                }
            },
        },
        methods: {
            previewResume(username) {
                if (!username) {
                    alert('Something went wrong while trying to preview resume! Please try again')
                }

                axios.get("/candidate/application/profile/details", {
                    params: {
                        username: username,
                    }
                }).then((response) => {
                    this.showCandidateResumeModal = true;
                    this.candidateResumeInformation = response.data
                }).catch((err) => {
                    this.errors = err.response.data.errors;
                });
            },
            scrollToBottom() {
                this.$nextTick(function () {
                    var container = this.$refs.chatbox;
                    container.scrollTop = container.scrollHeight;
                });
            },
            playAudio() {
                const sound = new Audio('/frontend/assets/sound.mp3')
                sound.play()
            },
            async fetchTranslateData() {
                let data = await axios.get('/translated/texts');
                this.languageTranslation = data.data
            },
            __(key) {
                if (this.languageTranslation) {
                    return this.languageTranslation[key] || key;
                }

                return key;
            },
            scrollToBottom() {
                this.$nextTick(function () {
                    var container = this.$refs.chatbox;
                    container.scrollTop = container.scrollHeight + 120;
                });
            },
            async filterData() {
                let response = await axios.get('/get/users', { params: this.filter })
                this.users_list = response.data
            },
            async getMessages(user) {
                this.isMessageClick = !this.isMessageClick
                if (this.auth.role == 'company') {
                    var username = user?.candidate?.user?.username || null
                } else {
                    var username = user?.company?.user?.username || null
                }

                this.selectedUser = user
                const section = document.getElementById("chat-box-card");
                console.log(section)
                if (section) {
                    window.scroll({
                        behavior: 'smooth',
                        left: 0,
                        top: section.offsetTop
                    });
                }

                if (username) {
                    let response = await axios.get('/get/messages/' + username)
                    this.messages = response.data
                    user.unread_count = null
                    user.last_message_from_me = true

                    this.loadUnreadMessage();

                    if (this.total_unread_count == 0) {
                        document.querySelector('.unread-message-part').className = 'circle d-none unread-message-part'
                    }
                } else {
                    alert('User not found');
                }
            },
            // Format message date for display
            formatMessageDate(date) {
                if (!date) return '';

                const messageDate = new Date(date);
                const today = new Date();
                const yesterday = new Date(today);
                yesterday.setDate(yesterday.getDate() - 1);

                // Format date as "Today", "Yesterday", or "DD/MM/YYYY"
                if (messageDate.toDateString() === today.toDateString()) {
                    return 'Today';
                } else if (messageDate.toDateString() === yesterday.toDateString()) {
                    return 'Yesterday';
                } else {
                    return messageDate.toLocaleDateString('id-ID', {
                        day: 'numeric',
                        month: 'long',
                        year: 'numeric'
                    });
                }
            },

            // Format message time for display
            formatMessageTime(date) {
                if (!date) return '';

                const messageDate = new Date(date);
                return messageDate.toLocaleTimeString('id-ID', {
                    hour: '2-digit',
                    minute: '2-digit'
                });
            },

            // Check if date separator should be shown between messages
            shouldShowDateSeparator(prevMessage, currentMessage) {
                if (!prevMessage || !currentMessage) return false;

                const prevDate = new Date(prevMessage.created_at);
                const currentDate = new Date(currentMessage.created_at);

                return prevDate.toDateString() !== currentDate.toDateString();
            },

            // Check if message contains URL
            hasUrl(text) {
                const urlRegex = /(https?:\/\/[^\s]+)/g;
                return urlRegex.test(text);
            },

            // Toggle attachment menu
            toggleAttachmentMenu() {
                this.showAttachmentMenu = !this.showAttachmentMenu;
                if (this.showAttachmentMenu) {
                    this.showEmojiPicker = false;
                }
            },

            // Toggle emoji picker
            toggleEmojiPicker() {
                this.showEmojiPicker = !this.showEmojiPicker;
                if (this.showEmojiPicker) {
                    this.showAttachmentMenu = false;
                }
            },

            // Add emoji to message
            addEmoji(emoji) {
                this.message += emoji;
                this.$refs.messageInput.focus();
                this.showEmojiPicker = false;
            },

            // Handle typing indicator
            handleTyping() {
                // Send typing indicator to other user
                if (this.selectedUser) {
                    // Clear previous timeout
                    if (this.typingIndicatorTimeout) {
                        clearTimeout(this.typingIndicatorTimeout);
                    }

                    // Get recipient ID
                    let to_id = null;
                    if (this.auth.role == 'company') {
                        to_id = this.selectedUser?.candidate?.user?.id || null;
                    } else {
                        to_id = this.selectedUser?.company?.user?.id || null;
                    }

                    if (!to_id) return;

                    // Send typing event
                    axios.post('/typing-indicator', {
                        to: to_id,
                        chat_id: this.selectedUser.id,
                        typing: true
                    }).catch(error => {
                        console.error('Error sending typing indicator:', error);
                    });

                    // Set timeout to stop typing indicator after 3 seconds
                    this.typingIndicatorTimeout = setTimeout(() => {
                        axios.post('/typing-indicator', {
                            to: to_id,
                            chat_id: this.selectedUser.id,
                            typing: false
                        }).catch(error => {
                            console.error('Error sending typing indicator:', error);
                        });
                    }, 3000);
                }
            },

            // Handle file upload
            handleFileUpload(event) {
                const file = event.target.files[0];
                if (!file) return;

                this.showAttachmentMenu = false;

                // Get recipient ID
                let to_id = null;
                if (this.auth.role == 'company') {
                    to_id = this.selectedUser?.candidate?.user?.id || null;
                } else {
                    to_id = this.selectedUser?.company?.user?.id || null;
                }

                if (!to_id) {
                    alert('User not found');
                    return;
                }

                // Create FormData
                const formData = new FormData();
                formData.append('file', file);
                formData.append('to', to_id);
                formData.append('chat_id', this.selectedUser.id);

                // Show loading state
                this.loading = true;

                // Upload file
                axios.post('/send/attachment', formData, {
                    headers: {
                        'Content-Type': 'multipart/form-data'
                    }
                })
                .then(response => {
                    this.messages.push(response.data);
                    this.scrollToBottom();
                    this.syncMessageUserList();
                })
                .catch(error => {
                    console.error('Error uploading file:', error);
                    alert('Failed to upload file. Please try again.');
                })
                .finally(() => {
                    this.loading = false;
                    // Reset file input
                    event.target.value = '';
                });
            },

            async sendMessage() {
                if (!this.message.length || this.loading) { return; }
                this.loading = true;

                // Clear typing indicator
                if (this.typingIndicatorTimeout) {
                    clearTimeout(this.typingIndicatorTimeout);
                    this.typingIndicatorTimeout = null;
                }

                // Get recipient ID
                let to_id = null;
                if (this.auth.role == 'company') {
                    to_id = this.selectedUser?.candidate?.user?.id || null;
                } else {
                    to_id = this.selectedUser?.company?.user?.id || null;
                }

                if (!to_id) {
                    alert('User not found');
                    this.loading = false;
                    return;
                }

                try {
                    let response = await axios.post('/send/message', {
                        message: this.message,
                        to: to_id,
                        chat_id: this.selectedUser.id,
                    });

                    this.messages.push(response.data);
                    this.message = '';
                    this.scrollToBottom();
                    this.syncMessageUserList();
                } catch (error) {
                    console.error('Error sending message:', error);
                    alert('Something went wrong');
                } finally {
                    this.loading = false;
                }
            },
            addNewLine(event) {
                if (event.shiftKey && event.key === 'Enter') {
                    event.preventDefault();
                    this.message += '\n';
                }
            },
            backPreviousPage() {
                this.isMessageClick = !this.isMessageClick
                this.messages = []
                this.selectedUser = ''
            },
            async syncMessageUserList() {
                let response = await axios.get('/sync/user-list')
                this.users_list = response.data
            },
            async loadUnreadMessage() {
                try {
                    let response = await axios.get('/messages/unread-count')
                    // Handle both formats: direct number or JSON with count property
                    if (typeof response.data === 'object' && response.data.hasOwnProperty('count')) {
                        this.total_unread_count = response.data.count
                    } else {
                        this.total_unread_count = response.data
                    }
                } catch (error) {
                    console.error('Error loading unread count:', error)
                    this.total_unread_count = 0
                }
            },
            showUnreadList() {
                if (this.$refs.unreadCheckbox.checked == true) {
                    var unread_lists = this.users.filter(function (user) {
                        return !user.last_message_from_me && user.unread_count != 0;
                    });

                    if (unread_lists && unread_lists.length != 0) {
                        this.users_list = unread_lists
                    }
                } else {
                    this.syncMessageUserList();
                }
            },

            // Mark message as read
            markAsRead(messageId) {
                if (!messageId) return;

                axios.post('/mark-message-read', {
                    message_id: messageId
                }).catch(error => {
                    console.error('Error marking message as read:', error);
                });
            },

            // Play notification sound
            playAudio() {
                const audio = new Audio("/frontend/assets/sound.mp3");
                audio.play();
            }
        },
        updated() {
            this.scrollToBottom();
        },
        mounted() {
            this.fetchTranslateData();

            // Listen for new messages
            Echo.private('chat')
                .listen('ChatMessage', (e) => {
                    if (e.chatMessage.to == this.auth.id) {
                        this.playAudio();
                        this.messages.push(e.chatMessage);
                        this.syncMessageUserList();
                        this.loadUnreadMessage();

                        // Mark message as read if user is viewing this conversation
                        if (this.selectedUser &&
                            ((this.auth.role == 'company' && this.selectedUser?.candidate?.user?.id == e.chatMessage.from) ||
                             (this.auth.role == 'candidate' && this.selectedUser?.company?.user?.id == e.chatMessage.from))) {
                            this.markAsRead(e.chatMessage.id);
                        }
                    }
                });

            // Listen for typing indicators
            Echo.private('typing.' + this.auth.id)
                .listen('TypingIndicator', (e) => {
                    if (this.selectedUser &&
                        ((this.auth.role == 'company' && this.selectedUser?.candidate?.user?.id == e.from) ||
                         (this.auth.role == 'candidate' && this.selectedUser?.company?.user?.id == e.from))) {
                        this.isTyping = e.typing;

                        // Auto-hide typing indicator after 5 seconds as a fallback
                        if (this.isTyping) {
                            setTimeout(() => {
                                this.isTyping = false;
                            }, 5000);
                        }
                    }
                });
        },
    }
</script>

<style scoped>
/* Mobile responsive chat improvements */
@media (max-width: 768px) {
    .message-form {
        gap: 0.5rem !important;
        padding: 0.75rem !important;
    }

    .message-form button {
        width: 40px !important;
        height: 40px !important;
        padding: 0.5rem !important;
        flex-shrink: 0;
    }

    .message-form textarea {
        font-size: 14px !important;
        min-height: 40px !important;
        padding: 0.75rem !important;
    }

    /* Ensure message bubbles don't overflow on mobile */
    .sent-message > div,
    .received-message > div {
        max-width: 85% !important;
        word-wrap: break-word;
        overflow-wrap: break-word;
    }

    /* Attachment menu responsive */
    .tw-w-48 {
        width: 200px !important;
    }

    /* Emoji picker responsive */
    .tw-w-64 {
        width: 250px !important;
    }
}

/* Ensure proper text wrapping in all screen sizes */
.tw-text-block {
    word-wrap: break-word;
    overflow-wrap: break-word;
    white-space: pre-wrap;
}

/* Prevent layout shift from long file names */
.tw-truncate {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}
</style>
