"use strict";function _defineProperty(e,i,s){return i in e?Object.defineProperty(e,i,{value:s,enumerable:!0,configurable:!0,writable:!0}):e[i]=s,e}

// Function to ensure all skeleton loaders are hidden
function hideAllSkeletons() {
    // Job details page
    if ($('#job-details-skeleton').length && !$('#job-details-skeleton').hasClass('d-none')) {
        $('#job-details-skeleton').addClass('d-none');
        $('#job-details-content').removeClass('d-none').hide().fadeIn(300);
    }

    // Job listings page
    if ($('#job-skeleton').length && !$('#job-skeleton').hasClass('d-none')) {
        $('#job-skeleton').addClass('d-none');
        $('#job-listings').removeClass('d-none').hide().fadeIn(300);
    }
}

// Ensure skeletons are hidden when page is fully loaded
window.addEventListener('load', function() {
    setTimeout(hideAllSkeletons, 1000);
});

// Ensure skeletons are hidden when DOM is fully loaded
document.addEventListener('DOMContentLoaded', function() {
    setTimeout(hideAllSkeletons, 1000);
});

!function(e){if(e(".menu-item-has-children > a").on("click",function(){var i=e(this).parent("li");i.hasClass("open")?(i.removeClass("open"),i.find("li").removeClass("open"),i.find("ul").slideUp(500),i.find(".rt-mega-menu").slideUp(500)):(i.addClass("open"),i.children("ul").slideDown(500),i.children(".rt-mega-menu").slideDown(500),i.siblings("li").children("ul").slideUp(),i.siblings("li").removeClass("open"),i.siblings("li").find("li").removeClass("open"),i.siblings("li").find("ul").slideUp())}),e(".has-children > .jobwidget_tiitle").on("click",function(){var i=e(this).parent("li");i.hasClass("open")?(i.removeClass("open"),i.find("li").removeClass("open"),i.find("ul").slideUp(200)):(i.addClass("open"),i.children("ul").slideDown(200),i.siblings("li").children("ul").slideUp(),i.siblings("li").removeClass("open"),i.siblings("li").find("li").removeClass("open"),i.siblings("li").find("ul").slideUp())}),e(window).width()>991.98){var i,s,l;s=(i=e(".rt-sticky")).outerHeight(),l=e(document).scrollTop(),e(window).on("load",function(){e(document).scrollTop()>s&&(i.hasClass("rt-sticky-active")?i.removeClass("rt-sticky-active"):i.addClass("rt-sticky-active"))}),e(window).on("scroll",function(){var s=e(document).scrollTop(),o=e(".rt-sticky-active");s>l?o.addClass("sticky"):o.removeClass("sticky"),0===s?i.removeClass("rt-sticky-active"):i.addClass("rt-sticky-active"),l=e(document).scrollTop()})}e(".main-menu ul li").on("click",function(){e(".main-menu ul li").removeClass("active"),e(this).addClass("active")}),e(".menu-click").on("click",function(){return e(".main-menu").toggleClass("active-mobile-menu"),e(".rt-mobile-menu-overlay").toggleClass("active"),!1}),e(".rt-mobile-menu-close, .rt-mobile-menu-overlay").on("click",function(){return e(".main-menu").removeClass("active-mobile-menu"),e(".rt-mobile-menu-overlay").removeClass("active"),!1}),e(".counter").counterUp({delay:10,time:1e3}),e.scrollUp({scrollText:'<i class="fa fa-angle-up pb-1"></i>'}),e(".toggle-filter-sidebar").on("click",function(){e(".sidebar-widget-overlay, .jobsidebar").toggleClass("active")}),e(".sidebar-widget-overlay, .close-me").on("click",function(){e(".sidebar-widget-overlay, .jobsidebar").removeClass("active")}),e(".close-tag").on("click",function(){e(this).parent(".single-tag").hide()}),e(".job-filter-overlay").on("click",function(){e(".jobsearchBox").removeClass("active-adf"),e(".job-filter-overlay").removeClass("active"),e(".advance-hidden-filter-menu").slideUp(300),e("body").removeClass("body-no-scrolling")});var o,t=document.getElementById("togglclass1"),n=document.getElementById("toggoleSidebar"),a=document.getElementsByClassName("condition_class");e(".toggole-colum-classes").on("click",function(){e(n).toggleClass("d-none"),n.classList.contains("d-none")?(t.classList.add("col-xl-12"),t.classList.remove("col-lg-8")):(t.classList.add("col-lg-8"),t.classList.remove("col-xl-12")),t.classList.contains("col-lg-8")?e(a).removeClass("col-xl-4"):e(a).addClass("col-xl-4")}),e(".togglepass").on("click",function(){var e=document.getElementById("myInput");"password"===e.type?e.type="text":e.type="password"}),e(".togglepass2").on("click",function(){var e=document.getElementById("myInput2");"password"===e.type?e.type="text":e.type="password"}),e(".sidebar-open-nav").on("click",function(){e(".d-sidebar, .d-page-content ").toggleClass("acitve"),e(".sidebar-overlay").toggleClass("active")}),e(".close-sidebar").on("click",function(){e(".d-sidebar, .d-page-content ").toggleClass("acitve"),e(".sidebar-overlay").toggleClass("active")}),e(".sidebar-overlay").on("click",function(){e(".d-sidebar, .d-page-content ").toggleClass("acitve"),e(".sidebar-overlay").toggleClass("active")}),e("#flexSwitchCheckDefault").on("change",function(){e("body").toggleClass("price-toggole")});var r=e(".notification-icon");e(document).mouseup(function(e){r.is(e.target)||0!==r.has(e.target).length||r.removeClass("notification-visiable")}),e(".notification-icon").on("click",function(e){e.preventDefault(),r.toggleClass("notification-visiable")});var c=e(".switch-profile");e(document).mouseup(function(e){c.is(e.target)||0!==c.has(e.target).length||c.removeClass("profile-visiable")}),e(".switch-profile").on("click",function(e){e.preventDefault(),c.toggleClass("profile-visiable")}),AOS.init({disable:"mobile",easing:"ease-in-out-sine",once:!0}),e(".testimonail_active").length>0&&e(".testimonail_active").slick((_defineProperty(o={slidesToShow:3,infinite:!0,slidesToScroll:2,dots:!0,prevArrow:e(".slickprev2"),nextArrow:e(".slicknext2")},"prevArrow",'<button aria-label="Arrow Left" title="Arrow Left" class="slide-arrow prev-arrow"></button>'),_defineProperty(o,"nextArrow",'<button aria-label="Arrow Right" title="Arrow Right" class="slide-arrow next-arrow"></button>'),_defineProperty(o,"responsive",[{breakpoint:1199,settings:{slidesToShow:2,slidesToScroll:1}},{breakpoint:768,settings:{slidesToShow:1,slidesToScroll:1}},{breakpoint:479,settings:{slidesToShow:1,slidesToScroll:1}},{breakpoint:320,settings:{slidesToShow:1,slidesToScroll:1}},{breakpoint:210,settings:{slidesToShow:1,slidesToScroll:1}},]),o))}(jQuery);var hideMenuBtn=document.querySelector(".hide-menu-btn"),hideMenu=document.getElementById("progressbar");hideMenuBtn&&hideMenuBtn.addEventListener("click",function(){hideMenu.classList.add("hide-menu")});
