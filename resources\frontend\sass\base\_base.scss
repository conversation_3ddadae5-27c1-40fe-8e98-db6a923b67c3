:root{
    @each $color, $shades in $colors{
      @each $shade, $value in $shades{
         #{'--'+$color}-#{$shade}: #{$value};    
       }
    }
    @each $font, $sizes in $bodyfontScale{
      @each $size, $value in $sizes{
         #{'--fs-'+$font}-#{$size}: #{$value};    
       }
    }
}



*,
*::before,
*::after {
  box-sizing: inherit;
}


html {
  box-sizing: border-box;
  height: 100%;
}


body {
  height: 100%;
  // line-height: var(--line-height);
  // font-size: var(--body-font-size);
  color: var(--gray-900);
  font-family: $body-font;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  vertical-align: baseline;
  text-rendering: optimizeLegibility;
  font-weight: 400;
  overflow-x: hidden;
  counter-reset: my-sec-counter;
  &.styleguide{
    background-color: #DCDFE8;
  }
  
}


hr {
  display: block;
  height: 1px;
  border: 0;
  border-top: 1px solid var(--gray-200);
  margin: 1em 0;
  padding: 0;
}



audio,
canvas,
iframe,
img,
svg,
video {
  vertical-align: middle;
}



fieldset {
  border: 0;
  margin: 0;
  padding: 0;
}



textarea {
  resize: vertical;
}



.browserupgrade {
  margin: 0.2em 0;
  background: #ccc;
  color: #000;
  padding: 0.2em 0;
}



.hidden {
  display: none !important;
}



.visuallyhidden {
  border: 0;
  clip: rect(0 0 0 0);
  height: 1px;
  margin: -1px;
  overflow: hidden;
  padding: 0;
  position: absolute;
  width: 1px;
  white-space: nowrap; /* 1 */
}




.visuallyhidden.focusable:active,
.visuallyhidden.focusable:focus {
  clip: auto;
  height: auto;
  margin: 0;
  overflow: visible;
  position: static;
  width: auto;
  white-space: inherit;
}



.invisible {
  visibility: hidden;
}



.clearfix:before,
.clearfix:after {
  content: " "; /* 1 */
  display: table; /* 2 */
}

.clearfix:after {
  clear: both;
}

::-moz-selection {
  background-color: #989ffd;
  color: #fff;
}

::selection {
  background-color: #989ffd;
  color: #fff;
}

#{$all-buttons-active},
#{$all-buttons-focus},
#{$all-buttons-hover},
#{$all-text-inputs-focus},
#{$all-text-inputs-active},
#{$all-text-inputs-hover},
#{$all-text-inputs-invalid} {
  outline: none;
  box-shadow: none;
}
#{$all-buttons-active},
#{$all-buttons-focus},
#{$all-buttons-hover} {
  cursor: pointer;
}


@media print {
  *,
  *:before,
  *:after {
    background: transparent !important;
    color: #000 !important; /* Black prints faster */
    -webkit-box-shadow: none !important;
    box-shadow: none !important;
    text-shadow: none !important;
  }

  a,
  a:visited {
    text-decoration: underline;
  }

  a[href]:after {
    content: " (" attr(href) ")";
  }

  abbr[title]:after {
    content: " (" attr(title) ")";
  }

  /*
   * Don't show links that are fragment identifiers,
   * or use the `javascript:` pseudo protocol
   */

  a[href^="#"]:after,
  a[href^="javascript:"]:after {
    content: "";
  }

  pre {
    white-space: pre-wrap !important;
  }
  pre,
  blockquote {
    border: 1px solid #999;
    page-break-inside: avoid;
  }


  thead {
    display: table-header-group;
  }

  tr,
  img {
    page-break-inside: avoid;
  }

  p,
  h2,
  h3 {
    orphans: 3;
    widows: 3;
  }

  h2,
  h3 {
    page-break-after: avoid;
  }
}
select{
  -webkit-appearance: listbox !important;
}