.single-price-table {
  padding: 32px;
  border: 1px solid #edeff5;
  border-radius: 12px;
  position: relative;
  .price-header{
      padding-bottom: 32px;
      border-bottom: 1px solid #edeff5;
      margin-left: -32px;
      margin-right: -32px;
      padding-left: 32px;
      padding-right: 32px;
      position: relative;
      sup{
          vertical-align:baseline;
      }
      .badge{
          position: absolute;
          top: -58px;
          left: 50%;
          transform: translateX(-50%);
      }
      @include breakpoint(md){
          text-align: center;
      }
  }
  .price-body{
      padding: 32px 0 12px;
      ul{
          li{
              margin-bottom: 20px;
              display: flex;
              column-gap: 12px;
              color: var(--gray-700);
              font-size: 14px;
          }
      }
  }
  &.active{
      border-color: var(--primary-500);
      box-shadow: 0px 12px 20px rgba(0, 44, 109, 0.1);
      
  }
}

.form-switch{
   padding-left: 0px;
}