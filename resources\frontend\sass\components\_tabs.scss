.cadidate-dashboard-tabs{
  .nav{
    border-bottom: 1px solid var(--gray-100);
  }
  .nav-pills .nav-link{
    font-weight: 600;
    padding: 12px 20px;
    font-size: 14px;
    line-height: 1;
    display: flex;
    align-items: center;
    color: var(--gray-500);
    position: relative;
    svg{
      margin-right: 7px;
    }
    path{
      stroke: var(--gray-500);
    }
    &:hover,
    &.active{
      color: var(--primary-500);
      background-color: transparent !important;
      path{
        stroke: var(--primary-500);
      }
      &:before{
        content: "";
        left: 0;
        bottom: 0;
        width: 100%;
        height: 2px;
        transition: all 0.4s;
        position: absolute;
        background-color: var(--primary-500);
      }
    }
  }
}

#pricemodal{
  .nav{
    border-bottom: none;
    .nav-link{
      padding: 4px 8px;
      margin-right: 4px;
      border-radius: 0px;
      @media (min-width: 575px){
        padding: 12px 20px;
      }
      color: #767F8C;
      &:hover{
        box-shadow: inset 0px -2px 0px #0A65CC; 
      }
    }
    .active{
      color: var(--primary-500);
      background-color: transparent !important;
      box-shadow: inset 0px -2px 0px #0A65CC;
    }
  }
}
.paypal-wrap{
  .from-radio-custom .form-check-input {
    height: 12px;
    width: 12px;
    margin-right: 0px;
    @media (min-width: 575px) {
      width: 22px;
      height: 22px;
    }
  }
  .label-radio{
    padding: 8px;
    border-radius: 4px !important;
    @media (min-width: 575px){
      padding: 20px;
    }
  }
  .d-flex{
    gap: 8px;
    @media (min-width:575px){
      gap: 16px;
    }
  }
  img{
    width: 12px;
    height: 12px;
    @media (min-width: 575px){
      width: 36px;
      height: 36px;
    }
  }
  .card-info{
  h4{
    font-weight: 400;
    font-size: 10px;
    line-height: 1.5;
    color: #767F8C;
    @media (min-width: 575px){
      font-size: 10px;
    }
  }
  h2{
    font-weight: 500;
    font-size: 12px;
    line-height: 1.43;
    color: #18191C;
    @media (min-width: 575px){
      font-size: 14px;
    }
  }
}
label{
  font-weight: 400;
  font-size: 14px;
  line-height: 1.43;
  color: #18191C;
}
.card-element,input{
  border: 1px solid var(--gray-100);
}
}
