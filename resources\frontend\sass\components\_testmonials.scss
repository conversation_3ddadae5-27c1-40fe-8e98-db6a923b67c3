.testimonals-box {
  padding: 24px;
  background-color: #fff;
  // box-shadow: 0px 12px 80px rgba(0, 44, 109, 0.05);
  border-radius: 12px;
  min-height: 282px;
  position: relative;
  // margin: 12px 0 24px;
  @include breakpoint(xxl){
    min-height: 350px;
  }
  @include breakpoint(lg){
    min-height: 282px;
  }
  @media (max-width: 767px) {
    min-height: auto !important;
  }
  .rt-single-icon-box {
    position: absolute;
    left: 0;
    bottom: 24px;
    width: 100%;
    padding-left: 24px;
    padding-right: 24px;
    .userimage{
      height: 48px;
      width: 48px;
      border-radius: 50%;
      img{
        object-fit: cover;
        border-radius: 50%;
        height: 100%;
      }
    }
    @include breakpoint(md){
      position: unset;
      padding-left: 0px;
      padding-right: 0px;
      margin-top: 20px;
    }
  }
}
.p-12 {
  padding: 12px;
}

.testimonal-item {
  display: flex;
  align-items: center;
  @include breakpoint(lg){
   display: block;
  }
  .left-img {
    flex-grow: 0 !important;
    flex-shrink: 0 !important;
    @include breakpoint(lg){
      margin-bottom: 24px;
    }
    .img-box {
      width: 560px;
      height: 552px;
      position: relative;
      overflow: hidden;
      margin-right: 112px;
      background-color: var(--primary-500);
      border-radius: 12px;
      @include breakpoint(xl){
        width: 380px;
        margin-right: 50px;
      }
      @include breakpoint(lg){
        height: 270px;
        width: 300px;
        margin-right: 0px;
      }
      @include breakpoint(xs){
        width: 100%;
      }
      img {
        @extend .img-fit;
        border-radius: 12px;
      }
    }
  }
}
.testi-title {
  margin-left: auto;
  max-width: 682px;
  display: none;
}
.t-overlay {
  background: var(--gray-20);
  position: absolute;
  left: 0px;
  top: 198px;
  z-index: -1;
  width: 100%;
  height: 356px;
  @include breakpoint(lg){
    height: calc(100% - 200px)
  }
}
.testimonal-item {
  position: relative;
  .rt-single-icon-box {
    .iconbox-content {
      border-left: 2px solid var(--primary-500);
      padding-left: 16px;
    }
  }
}
.testimonials-area2 {
  .carosle-button {
    margin-left: 52%;
    margin-top: -116px;
    @include breakpoint(xl){
      margin-left: 45%;
      
    }
    @include breakpoint(lg){
      margin-top: 0px;
      position: relative;
      top: -30px;
      margin-left: 0px;
    }
  }
  .t-title{
    margin-left: 52%;
    left: 0;
    top: 100px;
    width: 100%;
    position: absolute;
    @include breakpoint(xl){
      margin-left: 45%;
    }
    @include breakpoint(lg){
      position: static;
      margin-left: 0px;
      margin-bottom: 35px;
    }
  }
}
.testimonail_active{
  margin: 0 50px;
  @media (max-width: 1300px) {
    margin: 0;
  }
  .prev-arrow, 
  .next-arrow {
    width: 48px;
    height: 48px;
    background-image: url(../images/svg/arrow-left.svg);
    background-color: white;
    background-size: 24px;
    background-repeat: no-repeat;
    background-position: center center;
    border-radius: 5px;
    position: absolute;
    top: 42%;
    left: -110px;
    z-index: 99;
    border: none;
    transform: translateY(-50%);
    @media (max-width: 1300px) {
      left: -30px;
    }
    @media (max-width: 767px) {
      display: none!important;
    }
  }
  .next-arrow{
    left: auto;
    right: -110px;
    @media (max-width: 1300px) {
      right: -30px;
    }
    background-image: url(../images/svg/arrow-right.svg);
  }

}