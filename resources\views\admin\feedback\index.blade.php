@extends('admin.layouts.app')
@section('title')
    {{ __('Saran dan Ma<PERSON>') }}
@endsection

@section('content')
    <div class="container-fluid">
        <div class="row">
            <div class="col-md-12">
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title line-height-36">{{ __('Saran dan Ma<PERSON>kan') }}</h3>
                    </div>
                    <div class="card-body table-responsive p-0">
                        <div class="row p-3">
                            <div class="col-sm-12 col-md-4 mb-3">
                                <div class="card bg-primary text-white">
                                    <div class="card-body">
                                        <h5 class="card-title">Total Saran</h5>
                                        <h2 class="card-text" id="total-feedback">0</h2>
                                    </div>
                                </div>
                            </div>
                            <div class="col-sm-12 col-md-4 mb-3">
                                <div class="card bg-success text-white">
                                    <div class="card-body">
                                        <h5 class="card-title">Saran Dibaca</h5>
                                        <h2 class="card-text" id="read-feedback">0</h2>
                                    </div>
                                </div>
                            </div>
                            <div class="col-sm-12 col-md-4 mb-3">
                                <div class="card bg-warning text-white">
                                    <div class="card-body">
                                        <h5 class="card-title">Saran Belum Dibaca</h5>
                                        <h2 class="card-text" id="unread-feedback">0</h2>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <table class="table table-hover text-nowrap table-bordered" id="feedbackTable">
                            <thead>
                                <tr>
                                    <th width="5%">{{ __('#') }}</th>
                                    <th>{{ __('Nama') }}</th>
                                    <th>{{ __('Email') }}</th>
                                    <th>{{ __('Tipe Pengguna') }}</th>
                                    <th>{{ __('Penilaian') }}</th>
                                    <th>{{ __('Tanggal') }}</th>
                                    <th>{{ __('Status') }}</th>
                                    <th width="10%">{{ __('Aksi') }}</th>
                                </tr>
                            </thead>
                            <tbody>
                                {{-- Data will be loaded via AJAX --}}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    {{-- Feedback Detail Modal --}}
    <div class="modal fade" id="feedbackDetailModal" tabindex="-1" role="dialog" aria-labelledby="feedbackDetailModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="feedbackDetailModalLabel">Detail Saran dan Masukan</h5>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label>Nama:</label>
                                <p id="detail-name" class="font-weight-bold"></p>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label>Email:</label>
                                <p id="detail-email" class="font-weight-bold"></p>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label>Telepon:</label>
                                <p id="detail-phone" class="font-weight-bold"></p>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label>Tipe Pengguna:</label>
                                <p id="detail-user-type" class="font-weight-bold"></p>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label>Penilaian:</label>
                                <div id="detail-rating" class="font-weight-bold"></div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label>Tanggal:</label>
                                <p id="detail-date" class="font-weight-bold"></p>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-12">
                            <div class="form-group">
                                <label>Pesan:</label>
                                <div id="detail-message" class="p-3 bg-light rounded"></div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">Tutup</button>
                    <button type="button" class="btn btn-success" id="mark-as-read-btn">Tandai Dibaca</button>
                </div>
            </div>
        </div>
    </div>

    {{-- Delete Confirmation Modal --}}
    <div class="modal fade" id="deleteModal" tabindex="-1" role="dialog" aria-labelledby="deleteModalLabel" aria-hidden="true">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="deleteModalLabel">Konfirmasi Hapus</h5>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    Apakah Anda yakin ingin menghapus saran ini?
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">Batal</button>
                    <button type="button" class="btn btn-danger" id="confirm-delete-btn">Hapus</button>
                </div>
            </div>
        </div>
    </div>
@endsection

@section('style')
    <link rel="stylesheet" href="{{ asset('backend') }}/plugins/datatables-bs4/css/dataTables.bootstrap4.min.css">
    <link rel="stylesheet" href="{{ asset('backend') }}/plugins/datatables-responsive/css/responsive.bootstrap4.min.css">
    <style>
        .rating-stars {
            color: #FFD700;
        }
        .badge-read {
            background-color: #28a745;
            color: white;
        }
        .badge-unread {
            background-color: #ffc107;
            color: black;
        }
    </style>
@endsection

@section('script')
    <script src="{{ asset('backend') }}/plugins/datatables/jquery.dataTables.min.js"></script>
    <script src="{{ asset('backend') }}/plugins/datatables-bs4/js/dataTables.bootstrap4.min.js"></script>
    <script src="{{ asset('backend') }}/plugins/datatables-responsive/js/dataTables.responsive.min.js"></script>
    <script src="{{ asset('backend') }}/plugins/datatables-responsive/js/responsive.bootstrap4.min.js"></script>
    <script>
        $(function() {
            let feedbackId = null;
            let table = $('#feedbackTable').DataTable({
                processing: true,
                serverSide: true,
                responsive: true,
                ajax: "{{ route('admin.feedback.data') }}",
                columns: [{
                        data: 'DT_RowIndex',
                        name: 'DT_RowIndex',
                        orderable: false,
                        searchable: false
                    },
                    {
                        data: 'name',
                        name: 'name'
                    },
                    {
                        data: 'email',
                        name: 'email'
                    },
                    {
                        data: 'user_info',
                        name: 'user_info',
                        orderable: false,
                        searchable: false
                    },
                    {
                        data: 'rating_stars',
                        name: 'rating',
                        orderable: true,
                        searchable: false
                    },
                    {
                        data: 'created_date',
                        name: 'created_at'
                    },
                    {
                        data: function(row) {
                            return row.is_read ? 
                                '<span class="badge badge-read">Dibaca</span>' : 
                                '<span class="badge badge-unread">Belum Dibaca</span>';
                        },
                        name: 'is_read',
                        orderable: true,
                        searchable: false
                    },
                    {
                        data: 'action',
                        name: 'action',
                        orderable: false,
                        searchable: false
                    }
                ],
                drawCallback: function(settings) {
                    // Update summary counts
                    updateSummary();
                }
            });

            // View feedback details
            $(document).on('click', '.view-feedback-btn', function() {
                const id = $(this).data('id');
                feedbackId = id;
                
                // Show loading state
                $('#detail-name, #detail-email, #detail-phone, #detail-user-type, #detail-rating, #detail-date, #detail-message').html('<div class="spinner-border spinner-border-sm" role="status"><span class="sr-only">Loading...</span></div>');
                
                // Fetch feedback details
                $.ajax({
                    url: `/admin/feedback/${id}`,
                    type: 'GET',
                    dataType: 'json',
                    success: function(response) {
                        if (response.success) {
                            const feedback = response.data;
                            
                            // Populate modal with feedback details
                            $('#detail-name').text(feedback.name);
                            $('#detail-email').text(feedback.email);
                            $('#detail-phone').text(feedback.phone || '-');
                            $('#detail-user-type').text(feedback.user_type ? feedback.user_type.charAt(0).toUpperCase() + feedback.user_type.slice(1) : 'Guest');
                            
                            // Display rating as stars
                            let stars = '';
                            for (let i = 1; i <= 5; i++) {
                                if (i <= feedback.rating) {
                                    stars += '<i class="fas fa-star text-warning"></i>';
                                } else {
                                    stars += '<i class="far fa-star text-warning"></i>';
                                }
                            }
                            stars += ` <span class="ml-2">${feedback.rating}/5</span>`;
                            $('#detail-rating').html(stars);
                            
                            // Format date
                            const date = new Date(feedback.created_at);
                            const formattedDate = date.toLocaleDateString('id-ID', {
                                day: 'numeric',
                                month: 'long',
                                year: 'numeric',
                                hour: '2-digit',
                                minute: '2-digit'
                            });
                            $('#detail-date').text(formattedDate);
                            
                            // Display message
                            $('#detail-message').text(feedback.message);
                            
                            // Show/hide mark as read button
                            if (feedback.is_read) {
                                $('#mark-as-read-btn').hide();
                            } else {
                                $('#mark-as-read-btn').show();
                            }
                            
                            // Show modal
                            $('#feedbackDetailModal').modal('show');
                        } else {
                            toastr.error('Gagal memuat detail saran');
                        }
                    },
                    error: function() {
                        toastr.error('Terjadi kesalahan. Silakan coba lagi.');
                    }
                });
            });

            // Mark as read
            $('#mark-as-read-btn').on('click', function() {
                if (!feedbackId) return;
                
                // Show loading state
                $(this).html('<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> Memproses...');
                $(this).prop('disabled', true);
                
                $.ajax({
                    url: `/admin/feedback/${feedbackId}/mark-as-read`,
                    type: 'PUT',
                    dataType: 'json',
                    headers: {
                        'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                    },
                    success: function(response) {
                        if (response.success) {
                            // Hide button
                            $('#mark-as-read-btn').hide();
                            
                            // Show success message
                            toastr.success(response.message);
                            
                            // Reload table
                            table.ajax.reload();
                        } else {
                            toastr.error('Gagal menandai saran sebagai dibaca');
                        }
                    },
                    error: function() {
                        toastr.error('Terjadi kesalahan. Silakan coba lagi.');
                    },
                    complete: function() {
                        // Reset button
                        $('#mark-as-read-btn').html('Tandai Dibaca');
                        $('#mark-as-read-btn').prop('disabled', false);
                    }
                });
            });

            // Delete feedback
            $(document).on('click', '.delete-feedback-btn', function() {
                feedbackId = $(this).data('id');
                $('#deleteModal').modal('show');
            });

            // Confirm delete
            $('#confirm-delete-btn').on('click', function() {
                if (!feedbackId) return;
                
                // Show loading state
                $(this).html('<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> Menghapus...');
                $(this).prop('disabled', true);
                
                $.ajax({
                    url: `/admin/feedback/${feedbackId}`,
                    type: 'DELETE',
                    dataType: 'json',
                    headers: {
                        'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                    },
                    success: function(response) {
                        if (response.success) {
                            // Hide modal
                            $('#deleteModal').modal('hide');
                            
                            // Show success message
                            toastr.success(response.message);
                            
                            // Reload table
                            table.ajax.reload();
                        } else {
                            toastr.error('Gagal menghapus saran');
                        }
                    },
                    error: function() {
                        toastr.error('Terjadi kesalahan. Silakan coba lagi.');
                    },
                    complete: function() {
                        // Reset button
                        $('#confirm-delete-btn').html('Hapus');
                        $('#confirm-delete-btn').prop('disabled', false);
                    }
                });
            });

            // Update summary counts
            function updateSummary() {
                $.ajax({
                    url: "{{ route('admin.feedback.data') }}",
                    type: 'GET',
                    dataType: 'json',
                    data: {
                        summary: true
                    },
                    success: function(response) {
                        if (response.summary) {
                            $('#total-feedback').text(response.summary.total);
                            $('#read-feedback').text(response.summary.read);
                            $('#unread-feedback').text(response.summary.unread);
                        }
                    }
                });
            }

            // Initial summary update
            updateSummary();
        });
    </script>
@endsection
