@extends('backend.layouts.app')

@section('title')
    {{ __('Kirim Pesan Broadcast') }}
@endsection

@section('content')
    <div class="container-fluid">
        <div class="row">
            <div class="col-md-12">
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title line-height-36">{{ __('Kirim Pesan Broadcast') }}</h3>
                        <a href="{{ route('admin.inbox.index') }}" class="btn bg-primary float-right d-flex align-items-center justify-content-center">
                            <i class="fas fa-arrow-left"></i>&nbsp; {{ __('Kembali') }}
                        </a>
                    </div>
                    <div class="card-body">
                        <form action="{{ route('admin.inbox.broadcast.send') }}" method="POST" enctype="multipart/form-data">
                            @csrf
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="recipient_type">{{ __('<PERSON><PERSON>') }} <span class="text-danger">*</span></label>
                                        <select name="recipient_type" id="recipient_type" class="form-control @error('recipient_type') is-invalid @enderror" required>
                                            <option value="">{{ __('Pilih Jenis Penerima') }}</option>
                                            <option value="all_companies">{{ __('Semua Perusahaan') }}</option>
                                            <option value="all_candidates">{{ __('Semua Pencaker') }}</option>
                                            <option value="all_users">{{ __('Semua Pengguna') }}</option>
                                            <option value="verified_companies">{{ __('Perusahaan Terverifikasi') }}</option>
                                            <option value="unverified_companies">{{ __('Perusahaan Belum Terverifikasi') }}</option>
                                            <option value="verified_candidates">{{ __('Pencaker Terverifikasi') }}</option>
                                            <option value="unverified_candidates">{{ __('Pencaker Belum Terverifikasi') }}</option>
                                        </select>
                                        @error('recipient_type')
                                            <span class="invalid-feedback" role="alert">
                                                <strong>{{ $message }}</strong>
                                            </span>
                                        @enderror
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="send_method">{{ __('Metode Pengiriman') }} <span class="text-danger">*</span></label>
                                        <select name="send_method" id="send_method" class="form-control @error('send_method') is-invalid @enderror" required>
                                            <option value="inbox">{{ __('Inbox (Pesan Internal)') }}</option>
                                            <option value="email">{{ __('Email') }}</option>
                                            <option value="both">{{ __('Inbox & Email') }}</option>
                                        </select>
                                        @error('send_method')
                                            <span class="invalid-feedback" role="alert">
                                                <strong>{{ $message }}</strong>
                                            </span>
                                        @enderror
                                    </div>
                                </div>
                                <div class="col-md-12">
                                    <div class="form-group">
                                        <label for="subject">{{ __('Subjek') }} <span class="text-danger">*</span></label>
                                        <input type="text" name="subject" id="subject" class="form-control @error('subject') is-invalid @enderror" value="{{ old('subject') }}" required>
                                        @error('subject')
                                            <span class="invalid-feedback" role="alert">
                                                <strong>{{ $message }}</strong>
                                            </span>
                                        @enderror
                                    </div>
                                </div>
                                <div class="col-md-12">
                                    <div class="form-group">
                                        <label for="message">{{ __('Pesan') }} <span class="text-danger">*</span></label>
                                        <textarea name="message" id="message" class="form-control ckeditor @error('message') is-invalid @enderror" rows="5" required>{{ old('message') }}</textarea>
                                        @error('message')
                                            <span class="invalid-feedback" role="alert">
                                                <strong>{{ $message }}</strong>
                                            </span>
                                        @enderror
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="type">{{ __('Jenis Pesan') }} <span class="text-danger">*</span></label>
                                        <select name="type" id="type" class="form-control @error('type') is-invalid @enderror" required>
                                            <option value="umum">{{ __('Umum (Dapat Dibalas)') }}</option>
                                            <option value="informasi">{{ __('Informasi (Tidak Dapat Dibalas)') }}</option>
                                        </select>
                                        @error('type')
                                            <span class="invalid-feedback" role="alert">
                                                <strong>{{ $message }}</strong>
                                            </span>
                                        @enderror
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="attachment">{{ __('Lampiran') }}</label>
                                        <div class="custom-file">
                                            <input type="file" name="attachment" id="attachment" class="custom-file-input @error('attachment') is-invalid @enderror">
                                            <label class="custom-file-label" for="attachment">{{ __('Pilih file') }}</label>
                                            @error('attachment')
                                                <span class="invalid-feedback" role="alert">
                                                    <strong>{{ $message }}</strong>
                                                </span>
                                            @enderror
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-12">
                                    <div class="form-group">
                                        <div class="custom-control custom-checkbox">
                                            <input type="checkbox" name="can_reply" id="can_reply" class="custom-control-input" value="1" checked>
                                            <label class="custom-control-label" for="can_reply">{{ __('Penerima dapat membalas pesan ini') }}</label>
                                        </div>
                                    </div>
                                </div>

                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="priority">{{ __('Prioritas Tiket') }}</label>
                                        <select name="priority" id="priority" class="form-control">
                                            <option value="low">{{ __('Rendah') }}</option>
                                            <option value="medium" selected>{{ __('Sedang') }}</option>
                                            <option value="high">{{ __('Tinggi') }}</option>
                                            <option value="urgent">{{ __('Mendesak') }}</option>
                                        </select>
                                    </div>
                                </div>

                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="category">{{ __('Kategori Tiket') }}</label>
                                        <select name="category" id="category" class="form-control">
                                            <option value="general" selected>{{ __('Umum') }}</option>
                                            <option value="technical">{{ __('Teknis') }}</option>
                                            <option value="billing">{{ __('Pembayaran') }}</option>
                                            <option value="other">{{ __('Lainnya') }}</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="col-md-12">
                                    <div class="form-group">
                                        <div class="custom-control custom-checkbox">
                                            <input type="checkbox" name="send_notification" id="send_notification" class="custom-control-input" value="1" checked>
                                            <label class="custom-control-label" for="send_notification">{{ __('Kirim notifikasi ke pengguna') }}</label>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-12">
                                    <div class="form-group">
                                        <button type="submit" class="btn btn-primary">
                                            <i class="fas fa-paper-plane"></i>&nbsp; {{ __('Kirim Pesan Broadcast') }}
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection

@section('style')
<style>
    .ck-editor__editable_inline {
        min-height: 300px;
    }
</style>
@endsection

@section('script')
<script src="https://cdn.jsdelivr.net/npm/bs-custom-file-input/dist/bs-custom-file-input.min.js"></script>
<script>
    $(document).ready(function() {
        // Cek apakah CKEditor sudah dimuat
        if (typeof ClassicEditor !== 'undefined') {
            // Initialize CKEditor
            ClassicEditor
                .create(document.querySelector('#message'), {
                    toolbar: ['heading', '|', 'bold', 'italic', 'link', 'bulletedList', 'numberedList', '|', 'outdent', 'indent', '|', 'blockQuote', 'insertTable', 'undo', 'redo'],
                    heading: {
                        options: [
                            { model: 'paragraph', title: 'Paragraph', class: 'ck-heading_paragraph' },
                            { model: 'heading1', view: 'h1', title: 'Heading 1', class: 'ck-heading_heading1' },
                            { model: 'heading2', view: 'h2', title: 'Heading 2', class: 'ck-heading_heading2' },
                            { model: 'heading3', view: 'h3', title: 'Heading 3', class: 'ck-heading_heading3' }
                        ]
                    }
                })
                .catch(error => {
                    console.error(error);
                });
        } else {
            // Jika CKEditor belum dimuat, muat dari CDN
            var script = document.createElement('script');
            script.src = 'https://cdn.ckeditor.com/ckeditor5/36.0.1/classic/ckeditor.js';
            script.onload = function() {
                ClassicEditor
                    .create(document.querySelector('#message'), {
                        toolbar: ['heading', '|', 'bold', 'italic', 'link', 'bulletedList', 'numberedList', '|', 'outdent', 'indent', '|', 'blockQuote', 'insertTable', 'undo', 'redo'],
                        heading: {
                            options: [
                                { model: 'paragraph', title: 'Paragraph', class: 'ck-heading_paragraph' },
                                { model: 'heading1', view: 'h1', title: 'Heading 1', class: 'ck-heading_heading1' },
                                { model: 'heading2', view: 'h2', title: 'Heading 2', class: 'ck-heading_heading2' },
                                { model: 'heading3', view: 'h3', title: 'Heading 3', class: 'ck-heading_heading3' }
                            ]
                        }
                    })
                    .catch(error => {
                        console.error(error);
                    });
            };
            document.head.appendChild(script);
        }

        $('#type').on('change', function() {
            var value = $(this).val();

            if (value === 'informasi') {
                $('#can_reply').prop('checked', false);
                $('#can_reply').prop('disabled', true);
            } else {
                $('#can_reply').prop('disabled', false);
            }
        });

        // bs-custom-file-input
        if (typeof bsCustomFileInput !== 'undefined') {
            bsCustomFileInput.init();
        }
    });
</script>
@endsection
