@extends('backend.layouts.app')

@section('title')
    {{ __('<PERSON>rim Pesan Broadcast') }}
@endsection

@section('content')
    <div class="container-fluid">
        <div class="row">
            <div class="col-md-12">
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title line-height-36">{{ __('Kirim Pesan Broadcast') }}</h3>
                        <a href="{{ route('admin.inbox.index') }}" class="btn bg-primary float-right d-flex align-items-center justify-content-center">
                            <i class="fas fa-arrow-left"></i>&nbsp; {{ __('Kembali ke Daftar Pesan') }}
                        </a>
                    </div>
                    <div class="card-body">
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle mr-2"></i>
                            {{ __('Pesan broadcast akan dikirim ke semua perusahaan atau pencaker yang terdaftar di sistem.') }}
                        </div>

                        <form action="{{ route('admin.inbox.broadcast.send') }}" method="POST">
                            @csrf
                            <div class="row">
                                <div class="col-md-12">
                                    <div class="form-group">
                                        <label for="recipient_type">{{ __('Jenis Penerima') }} <span class="text-danger">*</span></label>
                                        <select name="recipient_type" id="recipient_type" class="form-control @error('recipient_type') is-invalid @enderror" required>
                                            <option value="">{{ __('Pilih Jenis Penerima') }}</option>
                                            <option value="all_companies" {{ old('recipient_type') == 'all_companies' ? 'selected' : '' }}>{{ __('Semua Perusahaan') }}</option>
                                            <option value="all_candidates" {{ old('recipient_type') == 'all_candidates' ? 'selected' : '' }}>{{ __('Semua Pencaker') }}</option>
                                        </select>
                                        @error('recipient_type')
                                            <span class="invalid-feedback" role="alert">
                                                <strong>{{ $message }}</strong>
                                            </span>
                                        @enderror
                                    </div>
                                </div>

                                <div class="col-md-12">
                                    <div class="form-group">
                                        <label for="subject">{{ __('Subjek') }} <span class="text-danger">*</span></label>
                                        <input type="text" name="subject" id="subject" class="form-control @error('subject') is-invalid @enderror" value="{{ old('subject') }}" required>
                                        @error('subject')
                                            <span class="invalid-feedback" role="alert">
                                                <strong>{{ $message }}</strong>
                                            </span>
                                        @enderror
                                    </div>
                                </div>

                                <div class="col-md-12">
                                    <div class="form-group">
                                        <label for="message">{{ __('Pesan') }} <span class="text-danger">*</span></label>
                                        <textarea name="message" id="message" rows="5" class="form-control @error('message') is-invalid @enderror" required>{{ old('message') }}</textarea>
                                        @error('message')
                                            <span class="invalid-feedback" role="alert">
                                                <strong>{{ $message }}</strong>
                                            </span>
                                        @enderror
                                    </div>
                                </div>

                                <div class="col-md-12">
                                    <div class="form-group">
                                        <div class="custom-control custom-switch">
                                            <input type="checkbox" class="custom-control-input" id="can_reply" name="can_reply" value="1">
                                            <label class="custom-control-label" for="can_reply">{{ __('Dapat Dibalas') }}</label>
                                            <small class="form-text text-muted">{{ __('Jika dicentang, penerima dapat membalas pesan ini. Jika tidak dicentang, pesan ini hanya bersifat informasi dan tidak dapat dibalas.') }}</small>
                                        </div>
                                    </div>
                                </div>

                                <div class="col-md-12">
                                    <div class="form-group text-right">
                                        <button type="submit" class="btn btn-primary" onclick="return confirm('{{ __('Apakah Anda yakin ingin mengirim pesan broadcast ini?') }}')">
                                            <i class="fas fa-paper-plane mr-1"></i> {{ __('Kirim Broadcast') }}
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection

@section('style')
    <style>
        .card {
            border-radius: 10px;
            box-shadow: 0 0 15px rgba(0, 0, 0, 0.05);
            border: 2px solid #138C79;
        }

        .card-header {
            background-color: #f8f9fa;
            padding: 15px 20px;
            border-bottom: 2px solid #138C79;
        }

        .card-header h3 {
            margin-bottom: 0;
            color: #333;
            font-weight: 600;
        }
    </style>
@endsection
