@extends('backend.layouts.app')

@section('title')
    {{ __('Kirim Pesan Baru') }}
@endsection

@section('content')
    <div class="container-fluid">
        <div class="row">
            <div class="col-md-12">
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title line-height-36">{{ __('Kirim Pesan Baru') }}</h3>
                        <a href="{{ route('admin.inbox.index') }}" class="btn bg-primary float-right d-flex align-items-center justify-content-center">
                            <i class="fas fa-arrow-left"></i>&nbsp; {{ __('Kembali') }}
                        </a>
                    </div>
                    <div class="card-body">
                        <form action="{{ route('admin.inbox.send') }}" method="POST" enctype="multipart/form-data">
                            @csrf
                            @if(session('error'))
                                <div class="alert alert-danger">
                                    {{ session('error') }}
                                </div>
                            @endif
                            @if(session('success'))
                                <div class="alert alert-success">
                                    {{ session('success') }}
                                </div>
                            @endif
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="recipient_type">{{ __('Jenis Penerima') }} <span class="text-danger">*</span></label>
                                        <select name="recipient_type" id="recipient_type" class="form-control @error('recipient_type') is-invalid @enderror" required>
                                            <option value="">{{ __('Pilih Jenis Penerima') }}</option>
                                            <option value="all_companies">{{ __('Semua Perusahaan') }}</option>
                                            <option value="all_candidates">{{ __('Semua Pencaker') }}</option>
                                            <option value="specific_company">{{ __('Perusahaan Tertentu') }}</option>
                                            <option value="specific_candidate">{{ __('Pencaker Tertentu') }}</option>
                                        </select>
                                        @error('recipient_type')
                                            <span class="invalid-feedback" role="alert">
                                                <strong>{{ $message }}</strong>
                                            </span>
                                        @enderror
                                    </div>
                                </div>
                                <div class="col-md-6 company-select d-none">
                                    <div class="form-group">
                                        <label for="company_id">{{ __('Pilih Perusahaan') }} <span class="text-danger">*</span></label>
                                        <select name="company_id" id="company_id" class="form-control select2 @error('company_id') is-invalid @enderror">
                                            <option value="">{{ __('Pilih Perusahaan') }}</option>
                                            @foreach($companies as $company)
                                                <option value="{{ $company->id }}">{{ $company->user->name }}</option>
                                            @endforeach
                                        </select>
                                        @error('company_id')
                                            <span class="invalid-feedback" role="alert">
                                                <strong>{{ $message }}</strong>
                                            </span>
                                        @enderror
                                    </div>
                                </div>
                                <div class="col-md-6 candidate-select d-none">
                                    <div class="form-group">
                                        <label for="candidate_id">{{ __('Pilih Pencaker') }} <span class="text-danger">*</span></label>
                                        <select name="candidate_id" id="candidate_id" class="form-control select2 @error('candidate_id') is-invalid @enderror">
                                            <option value="">{{ __('Pilih Pencaker') }}</option>
                                            @foreach($candidates as $candidate)
                                                <option value="{{ $candidate->id }}">{{ $candidate->user->name }}</option>
                                            @endforeach
                                        </select>
                                        @error('candidate_id')
                                            <span class="invalid-feedback" role="alert">
                                                <strong>{{ $message }}</strong>
                                            </span>
                                        @enderror
                                    </div>
                                </div>
                                <div class="col-md-12">
                                    <div class="form-group">
                                        <label for="subject">{{ __('Subjek') }} <span class="text-danger">*</span></label>
                                        <input type="text" name="subject" id="subject" class="form-control @error('subject') is-invalid @enderror" value="{{ old('subject') }}" required>
                                        @error('subject')
                                            <span class="invalid-feedback" role="alert">
                                                <strong>{{ $message }}</strong>
                                            </span>
                                        @enderror
                                    </div>
                                </div>
                                <div class="col-md-12">
                                    <div class="form-group">
                                        <label for="message">{{ __('Pesan') }} <span class="text-danger">*</span></label>
                                        <textarea name="message" id="message" class="form-control ckeditor @error('message') is-invalid @enderror" rows="5" required>{{ old('message') }}</textarea>
                                        @error('message')
                                            <span class="invalid-feedback" role="alert">
                                                <strong>{{ $message }}</strong>
                                            </span>
                                        @enderror
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="type">{{ __('Jenis Pesan') }} <span class="text-danger">*</span></label>
                                        <select name="type" id="type" class="form-control @error('type') is-invalid @enderror" required>
                                            <option value="umum">{{ __('Umum (Dapat Dibalas)') }}</option>
                                            <option value="informasi">{{ __('Informasi (Tidak Dapat Dibalas)') }}</option>
                                        </select>
                                        @error('type')
                                            <span class="invalid-feedback" role="alert">
                                                <strong>{{ $message }}</strong>
                                            </span>
                                        @enderror
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="attachment">{{ __('Lampiran') }}</label>
                                        <div class="custom-file">
                                            <input type="file" name="attachment" id="attachment" class="custom-file-input @error('attachment') is-invalid @enderror">
                                            <label class="custom-file-label" for="attachment">{{ __('Pilih file') }}</label>
                                            @error('attachment')
                                                <span class="invalid-feedback" role="alert">
                                                    <strong>{{ $message }}</strong>
                                                </span>
                                            @enderror
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-12">
                                    <div class="form-group">
                                        <div class="custom-control custom-checkbox">
                                            <input type="checkbox" name="can_reply" id="can_reply" class="custom-control-input" value="1" checked>
                                            <label class="custom-control-label" for="can_reply">{{ __('Penerima dapat membalas pesan ini') }}</label>
                                        </div>
                                    </div>

                                    <div class="form-group">
                                        <label for="priority">{{ __('Prioritas Tiket') }}</label>
                                        <select name="priority" id="priority" class="form-control">
                                            <option value="low">{{ __('Rendah') }}</option>
                                            <option value="medium" selected>{{ __('Sedang') }}</option>
                                            <option value="high">{{ __('Tinggi') }}</option>
                                            <option value="urgent">{{ __('Mendesak') }}</option>
                                        </select>
                                    </div>

                                    <div class="form-group">
                                        <label for="category">{{ __('Kategori Tiket') }}</label>
                                        <select name="category" id="category" class="form-control">
                                            <option value="general" selected>{{ __('Umum') }}</option>
                                            <option value="technical">{{ __('Teknis') }}</option>
                                            <option value="billing">{{ __('Pembayaran') }}</option>
                                            <option value="other">{{ __('Lainnya') }}</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="col-md-12">
                                    <div class="form-group">
                                        <button type="submit" class="btn btn-primary">
                                            <i class="fas fa-paper-plane"></i>&nbsp; {{ __('Kirim Pesan') }}
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection

@section('style')
<style>
    .ck-editor__editable_inline {
        min-height: 300px;
    }
</style>
@endsection

@section('script')
<script src="https://cdn.jsdelivr.net/npm/bs-custom-file-input/dist/bs-custom-file-input.min.js"></script>
<script>
    $(document).ready(function() {
        $('.select2').select2();

        // Cek apakah CKEditor sudah dimuat
        if (typeof ClassicEditor !== 'undefined') {
            // Initialize CKEditor
            ClassicEditor
                .create(document.querySelector('#message'), {
                    toolbar: ['heading', '|', 'bold', 'italic', 'link', 'bulletedList', 'numberedList', '|', 'outdent', 'indent', '|', 'blockQuote', 'insertTable', 'undo', 'redo'],
                    heading: {
                        options: [
                            { model: 'paragraph', title: 'Paragraph', class: 'ck-heading_paragraph' },
                            { model: 'heading1', view: 'h1', title: 'Heading 1', class: 'ck-heading_heading1' },
                            { model: 'heading2', view: 'h2', title: 'Heading 2', class: 'ck-heading_heading2' },
                            { model: 'heading3', view: 'h3', title: 'Heading 3', class: 'ck-heading_heading3' }
                        ]
                    }
                })
                .catch(error => {
                    console.error(error);
                });
        } else {
            // Jika CKEditor belum dimuat, muat dari CDN
            var script = document.createElement('script');
            script.src = 'https://cdn.ckeditor.com/ckeditor5/36.0.1/classic/ckeditor.js';
            script.onload = function() {
                ClassicEditor
                    .create(document.querySelector('#message'), {
                        toolbar: ['heading', '|', 'bold', 'italic', 'link', 'bulletedList', 'numberedList', '|', 'outdent', 'indent', '|', 'blockQuote', 'insertTable', 'undo', 'redo'],
                        heading: {
                            options: [
                                { model: 'paragraph', title: 'Paragraph', class: 'ck-heading_paragraph' },
                                { model: 'heading1', view: 'h1', title: 'Heading 1', class: 'ck-heading_heading1' },
                                { model: 'heading2', view: 'h2', title: 'Heading 2', class: 'ck-heading_heading2' },
                                { model: 'heading3', view: 'h3', title: 'Heading 3', class: 'ck-heading_heading3' }
                            ]
                        }
                    })
                    .catch(error => {
                        console.error(error);
                    });
            };
            document.head.appendChild(script);
        }

        $('#recipient_type').on('change', function() {
            var value = $(this).val();

            if (value === 'specific_company') {
                $('.company-select').removeClass('d-none');
                $('.candidate-select').addClass('d-none');
            } else if (value === 'specific_candidate') {
                $('.candidate-select').removeClass('d-none');
                $('.company-select').addClass('d-none');
            } else {
                $('.company-select').addClass('d-none');
                $('.candidate-select').addClass('d-none');
            }
        });

        $('#type').on('change', function() {
            var value = $(this).val();

            if (value === 'informasi') {
                $('#can_reply').prop('checked', false);
                $('#can_reply').prop('disabled', true);
            } else {
                $('#can_reply').prop('disabled', false);
            }
        });

        // bs-custom-file-input
        if (typeof bsCustomFileInput !== 'undefined') {
            bsCustomFileInput.init();
        }
    });
</script>
@endsection
