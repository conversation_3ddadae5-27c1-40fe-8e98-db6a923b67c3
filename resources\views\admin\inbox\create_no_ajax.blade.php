@extends('backend.layouts.app')

@section('title')
    {{ __('Kirim Pesan Baru') }}
@endsection

@section('content')
    <div class="container-fluid">
        <div class="row">
            <div class="col-md-12">
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title line-height-36">{{ __('Kirim Pesan Baru') }}</h3>
                        <a href="{{ route('admin.inbox.index') }}" class="btn bg-primary float-right d-flex align-items-center justify-content-center">
                            <i class="fas fa-arrow-left"></i>&nbsp; {{ __('Kembali ke Daftar Pesan') }}
                        </a>
                    </div>
                    <div class="card-body">
                        <form action="{{ route('admin.inbox.store') }}" method="POST">
                            @csrf
                            <div class="row">
                                <div class="col-md-12">
                                    <div class="form-group">
                                        <label for="recipient_type">{{ __('<PERSON><PERSON>') }} <span class="text-danger">*</span></label>
                                        <select name="recipient_type" id="recipient_type" class="form-control @error('recipient_type') is-invalid @enderror" required>
                                            <option value="">{{ __('Pilih Jenis Penerima') }}</option>
                                            <option value="specific_company">{{ __('Perusahaan Tertentu') }}</option>
                                            <option value="specific_candidate">{{ __('Pencaker Tertentu') }}</option>
                                        </select>
                                        @error('recipient_type')
                                            <span class="invalid-feedback" role="alert">
                                                <strong>{{ $message }}</strong>
                                            </span>
                                        @enderror
                                    </div>
                                </div>

                                <div class="col-md-12 company-recipient" style="display: none;">
                                    <div class="form-group">
                                        <label for="company_id">{{ __('Pilih Perusahaan') }} <span class="text-danger">*</span></label>
                                        <select name="recipient_id" id="company_id" class="form-control select2 @error('recipient_id') is-invalid @enderror">
                                            <option value="">{{ __('Pilih Perusahaan') }}</option>
                                            @foreach($companies as $company)
                                                <option value="{{ $company->id }}">{{ $company->user->name }}</option>
                                            @endforeach
                                        </select>
                                        @error('recipient_id')
                                            <span class="invalid-feedback" role="alert">
                                                <strong>{{ $message }}</strong>
                                            </span>
                                        @enderror
                                    </div>
                                </div>

                                <div class="col-md-12 candidate-recipient" style="display: none;">
                                    <div class="form-group">
                                        <label for="candidate_id">{{ __('Pilih Pencaker') }} <span class="text-danger">*</span></label>
                                        <select name="recipient_id" id="candidate_id" class="form-control select2 @error('recipient_id') is-invalid @enderror">
                                            <option value="">{{ __('Pilih Pencaker') }}</option>
                                            @foreach($candidates as $candidate)
                                                <option value="{{ $candidate->id }}">{{ $candidate->user->name }}</option>
                                            @endforeach
                                        </select>
                                        @error('recipient_id')
                                            <span class="invalid-feedback" role="alert">
                                                <strong>{{ $message }}</strong>
                                            </span>
                                        @enderror
                                    </div>
                                </div>

                                <div class="col-md-12">
                                    <div class="form-group">
                                        <label for="subject">{{ __('Subjek') }} <span class="text-danger">*</span></label>
                                        <input type="text" name="subject" id="subject" class="form-control @error('subject') is-invalid @enderror" value="{{ old('subject') }}" required>
                                        @error('subject')
                                            <span class="invalid-feedback" role="alert">
                                                <strong>{{ $message }}</strong>
                                            </span>
                                        @enderror
                                    </div>
                                </div>

                                <div class="col-md-12">
                                    <div class="form-group">
                                        <label for="message">{{ __('Pesan') }} <span class="text-danger">*</span></label>
                                        <textarea name="message" id="message" rows="5" class="form-control @error('message') is-invalid @enderror" required>{{ old('message') }}</textarea>
                                        @error('message')
                                            <span class="invalid-feedback" role="alert">
                                                <strong>{{ $message }}</strong>
                                            </span>
                                        @enderror
                                    </div>
                                </div>

                                <div class="col-md-12">
                                    <div class="form-group text-right">
                                        <button type="submit" class="btn btn-primary">
                                            <i class="fas fa-paper-plane mr-1"></i> {{ __('Kirim Pesan') }}
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection

@section('style')
    <link rel="stylesheet" href="{{ asset('backend') }}/plugins/select2/css/select2.min.css">
    <link rel="stylesheet" href="{{ asset('backend') }}/plugins/select2-bootstrap4-theme/select2-bootstrap4.min.css">
    <style>
        .card {
            border-radius: 10px;
            box-shadow: 0 0 15px rgba(0, 0, 0, 0.05);
            border: 2px solid #138C79;
        }

        .card-header {
            background-color: #f8f9fa;
            padding: 15px 20px;
            border-bottom: 2px solid #138C79;
        }

        .card-header h3 {
            margin-bottom: 0;
            color: #333;
            font-weight: 600;
        }

        .select2-container--bootstrap4 .select2-selection--single {
            height: calc(2.25rem + 2px) !important;
        }

        .select2-container--bootstrap4 .select2-selection--single .select2-selection__rendered {
            line-height: 2.25rem !important;
        }
    </style>
@endsection

@section('script')
    <script src="{{ asset('backend') }}/plugins/select2/js/select2.full.min.js"></script>
    <script>
        $(document).ready(function() {
            // Initialize Select2
            $('.select2').select2({
                theme: 'bootstrap4'
            });

            // Show/hide recipient fields based on recipient type
            $('#recipient_type').on('change', function() {
                const recipientType = $(this).val();
                
                if (recipientType === 'specific_company') {
                    $('.company-recipient').show();
                    $('.candidate-recipient').hide();
                    $('#candidate_id').prop('disabled', true);
                    $('#company_id').prop('disabled', false);
                } else if (recipientType === 'specific_candidate') {
                    $('.company-recipient').hide();
                    $('.candidate-recipient').show();
                    $('#candidate_id').prop('disabled', false);
                    $('#company_id').prop('disabled', true);
                } else {
                    $('.company-recipient').hide();
                    $('.candidate-recipient').hide();
                    $('#candidate_id').prop('disabled', true);
                    $('#company_id').prop('disabled', true);
                }
            });

            // Trigger change event if there's a value (for form validation errors)
            if ($('#recipient_type').val()) {
                $('#recipient_type').trigger('change');
            }
        });
    </script>
@endsection
