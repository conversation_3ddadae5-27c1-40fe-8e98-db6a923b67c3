@extends('backend.layouts.app')

@section('title')
    {{ __('<PERSON><PERSON>') }}
@endsection

@section('content')
    <div class="container-fluid">
        <div class="row">
            <div class="col-md-12">
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title line-height-36">{{ __('Pesan') }}</h3>
                        <div class="float-right d-flex">
                            <a href="{{ route('admin.inbox.broadcast') }}" class="btn bg-info mr-2 d-flex align-items-center justify-content-center">
                                <i class="fas fa-bullhorn"></i>&nbsp; {{ __('Kirim Broadcast') }}
                            </a>
                            <a href="{{ route('admin.inbox.create') }}" class="btn bg-primary d-flex align-items-center justify-content-center">
                                <i class="fas fa-plus"></i>&nbsp; {{ __('<PERSON><PERSON>') }}
                            </a>
                        </div>
                    </div>
                    <div class="card-body">
                        <!-- Stats Cards -->
                        <div class="row mb-4">
                            <div class="col-md-3">
                                <div class="card bg-light message-stat-card" onclick="applyFilter('all')">
                                    <div class="card-body">
                                        <div class="d-flex justify-content-between align-items-center">
                                            <div>
                                                <h6 class="mb-0">{{ __('Total Pesan') }}</h6>
                                                <h3 class="mt-2 mb-0">{{ \App\Models\MessageThread::count() }}</h3>
                                            </div>
                                            <div class="bg-primary rounded-circle p-3 d-flex align-items-center justify-content-center">
                                                <i class="fas fa-envelope text-white"></i>
                                            </div>
                                        </div>
                                        <div class="mt-2">
                                            <span class="badge bg-primary">{{ \App\Models\Message::where('read', true)->count() }} Dibaca</span>
                                            <span class="badge bg-danger">{{ \App\Models\Message::where('read', false)->count() }} Belum Dibaca</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="card bg-light message-stat-card" onclick="applyFilter('company')">
                                    <div class="card-body">
                                        <div class="d-flex justify-content-between align-items-center">
                                            <div>
                                                <h6 class="mb-0">{{ __('Pesan Perusahaan') }}</h6>
                                                <h3 class="mt-2 mb-0">{{ \App\Models\MessageThread::whereNotNull('company_id')->count() }}</h3>
                                            </div>
                                            <div class="bg-success rounded-circle p-3 d-flex align-items-center justify-content-center">
                                                <i class="fas fa-building text-white"></i>
                                            </div>
                                        </div>
                                        <div class="mt-2">
                                            <span class="text-muted">Klik untuk filter</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="card bg-light message-stat-card" onclick="applyFilter('candidate')">
                                    <div class="card-body">
                                        <div class="d-flex justify-content-between align-items-center">
                                            <div>
                                                <h6 class="mb-0">{{ __('Pesan Pencaker') }}</h6>
                                                <h3 class="mt-2 mb-0">{{ \App\Models\MessageThread::whereNotNull('candidate_id')->count() }}</h3>
                                            </div>
                                            <div class="bg-info rounded-circle p-3 d-flex align-items-center justify-content-center">
                                                <i class="fas fa-user text-white"></i>
                                            </div>
                                        </div>
                                        <div class="mt-2">
                                            <span class="text-muted">Klik untuk filter</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="card bg-light message-stat-card" onclick="applyFilter('admin')">
                                    <div class="card-body">
                                        <div class="d-flex justify-content-between align-items-center">
                                            <div>
                                                <h6 class="mb-0">{{ __('Pesan Admin') }}</h6>
                                                <h3 class="mt-2 mb-0">{{ \App\Models\MessageThread::where('is_admin_thread', true)->count() }}</h3>
                                            </div>
                                            <div class="bg-warning rounded-circle p-3 d-flex align-items-center justify-content-center">
                                                <i class="fas fa-user-shield text-white"></i>
                                            </div>
                                        </div>
                                        <div class="mt-2">
                                            <span class="text-muted">Klik untuk filter</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Search Bar -->
                        <div class="row mb-4">
                            <div class="col-md-12">
                                <div class="input-group">
                                    <input type="text" class="form-control" id="message-search" placeholder="Cari pesan berdasarkan nama pengirim, penerima, judul, atau loker...">
                                    <div class="input-group-append">
                                        <button class="btn btn-primary" type="button" id="search-button">
                                            <i class="fas fa-search"></i>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Message Detail Section -->
                        <div class="row">
                            <div class="col-12" id="message-detail-section" style="display: none;">
                                <div class="card">
                                    <div class="card-header d-flex justify-content-between align-items-center">
                                        <h5 id="message-detail-title">{{ __('Detail Pesan') }}</h5>
                                        <button type="button" class="btn btn-outline-secondary btn-sm" id="back-to-list">
                                            <i class="fas fa-arrow-left mr-1"></i> {{ __('Kembali ke Daftar Pesan') }}
                                        </button>
                                    </div>
                                    <div class="card-body p-0">
                                        <div id="message-container">
                                            <div class="text-center p-3 position-absolute w-100" id="message-loading" style="top: 0; left: 0; z-index: 1000; background-color: rgba(255, 255, 255, 0.9);">
                                                <div class="d-flex justify-content-center">
                                                    <div class="ph-briefcase loading-icon"></div>
                                                </div>
                                                <p class="mt-2">{{ __('Memuat pesan...') }}</p>
                                            </div>
                                            @livewire('message.message-detail', ['initialThreadId' => $threadId ?? null])
                                            @livewire('message.message-reply')
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Message List -->
                            <div class="col-12" id="message-list-section">
                                <div class="card">
                                    <div class="card-header d-flex justify-content-between align-items-center">
                                        <h5>{{ __('Daftar Pesan') }}</h5>
                                        <div class="d-flex align-items-center">
                                            <span id="current-filter-label" class="badge bg-secondary mr-2">Semua Pesan</span>
                                            <button id="refresh-messages" class="btn btn-sm btn-outline-secondary mr-2">
                                                <i class="ph-arrows-clockwise"></i> {{ __('Refresh') }}
                                            </button>
                                            <button id="reset-filter" class="btn btn-sm btn-outline-secondary">
                                                <i class="fas fa-times"></i> Reset Filter
                                            </button>
                                        </div>
                                    </div>
                                    <div class="card-body">
                                        <div id="message-list-container">
                                            @livewire('message.message-list', ['initialThreadId' => $threadId ?? null])
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection

@section('style')
    <style>
        .card {
            overflow: hidden;
        }

        #message-detail-section {
            transition: all 0.3s ease;
        }

        #message-detail-section .card {
            border-radius: 10px;
            box-shadow: 0 0 15px rgba(0, 0, 0, 0.05);
            border: 2px solid #138C79;
        }

        #message-detail-section .card-header {
            background-color: #f8f9fa;
            padding: 15px 20px;
            border-bottom: 2px solid #138C79;
        }

        #message-detail-section .card-header h5 {
            margin-bottom: 0;
            color: #333;
            font-weight: 600;
        }

        #message-detail-section .btn-close {
            cursor: pointer;
        }

        #message-detail-section .card-body {
            padding: 0;
        }

        #message-list-section .card {
            border-radius: 10px;
            box-shadow: 0 0 15px rgba(0, 0, 0, 0.05);
            border: 2px solid #138C79;
        }

        #message-list-section .card-header {
            background-color: #f8f9fa;
            padding: 15px 20px;
            border-bottom: 2px solid #138C79;
        }

        #message-list-section .card-header h5 {
            margin-bottom: 0;
            color: #333;
            font-weight: 600;
        }

        .loading-icon {
            font-size: 2rem;
            animation: bounce 1s infinite;
        }

        @keyframes bounce {
            0%, 100% {
                transform: translateY(0);
            }
            50% {
                transform: translateY(-10px);
            }
        }

        /* Stats Card Styles */
        .message-stat-card {
            transition: all 0.3s ease;
            cursor: pointer;
            border: 2px solid transparent;
        }

        .message-stat-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        }

        .message-stat-card.active {
            border-color: #007bff;
            background-color: #f8f9fa;
        }

        .message-stat-card .rounded-circle {
            width: 50px;
            height: 50px;
        }

        /* Message Table Styles */
        .message-table {
            width: 100%;
        }

        .message-table th {
            background-color: #f8f9fa;
            font-weight: 600;
            padding: 12px;
            border-bottom: 2px solid #dee2e6;
        }

        .message-table td {
            padding: 12px;
            vertical-align: middle;
            border-bottom: 1px solid #dee2e6;
        }

        .message-table tr {
            transition: background-color 0.2s;
        }

        .message-table tr:hover {
            background-color: #f8f9fa;
        }

        .message-table .unread {
            font-weight: 600;
            background-color: #f0f7ff;
        }

        .message-table .avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            object-fit: cover;
        }

        .message-table .message-title {
            font-weight: 500;
            margin-bottom: 5px;
        }

        .message-table .message-job {
            font-size: 0.85rem;
            color: #6c757d;
        }

        .message-table .badge {
            font-size: 0.75rem;
            padding: 0.35em 0.65em;
        }

        .message-table .message-date {
            font-size: 0.85rem;
            color: #6c757d;
            white-space: nowrap;
        }

        /* Pagination Styles */
        .pagination {
            margin-bottom: 0;
        }

        .pagination .page-link {
            color: #007bff;
            border-radius: 0;
        }

        .pagination .page-item.active .page-link {
            background-color: #007bff;
            border-color: #007bff;
        }

        /* Search Bar Styles */
        #message-search {
            border-radius: 4px 0 0 4px;
            border-right: none;
        }

        #search-button {
            border-radius: 0 4px 4px 0;
        }

        @media (max-width: 991.98px) {
            .card .col-lg-4 {
                border-right: none !important;
                border-bottom: 1px solid #dee2e6;
            }

            .card .d-flex.flex-column {
                height: auto !important;
            }

            .message-stat-card {
                margin-bottom: 15px;
            }
        }
    </style>
@endsection

@section('script')
<script>
    // Function to apply filter - defined at the top level
    function applyFilter(filter) {
        // Update active state on cards
        const statCards = document.querySelectorAll('.message-stat-card');
        statCards.forEach(card => {
            if (card.getAttribute('onclick').includes(`applyFilter('${filter}')`)) {
                card.classList.add('active');
            } else {
                card.classList.remove('active');
            }
        });

        // Update filter label
        const filterLabels = {
            'all': 'Semua Pesan',
            'company': 'Pesan Perusahaan',
            'candidate': 'Pesan Pencaker',
            'admin': 'Pesan Admin'
        };

        document.getElementById('current-filter-label').textContent = filterLabels[filter] || 'Semua Pesan';

        // Call Livewire method to filter messages
        window.livewire.emit('filterByType', filter);
    }

    // Function to reset filter
    function resetFilter() {
        document.getElementById('message-search').value = '';
        applyFilter('all');
    }

    // Function to search messages
    function searchMessages(term) {
        // Call Livewire method to search messages
        window.livewire.emit('searchMessages', term);

        // Update filter label
        document.getElementById('current-filter-label').textContent = `Hasil pencarian: "${term}"`;

        // Remove active state from all cards
        const statCards = document.querySelectorAll('.message-stat-card');
        statCards.forEach(card => {
            card.classList.remove('active');
        });
    }

    // Function to show message detail
    function showMessageDetail() {
        document.getElementById('message-detail-section').style.display = 'block';
        document.getElementById('message-list-section').style.display = 'none';

        if (document.getElementById('message-loading')) {
            document.getElementById('message-loading').style.display = 'flex';

            // Hide loading after a short delay
            setTimeout(function() {
                if (document.getElementById('message-loading')) {
                    document.getElementById('message-loading').style.display = 'none';
                }
            }, 1000);
        }

        // Scroll to top
        window.scrollTo({ top: 0, behavior: 'smooth' });
    }

    // Function to hide message detail
    function hideMessageDetail() {
        document.getElementById('message-detail-section').style.display = 'none';
        document.getElementById('message-list-section').style.display = 'block';

        // Remove thread ID from URL
        const url = new URL(window.location);
        url.searchParams.delete('pesan_id');
        window.history.pushState({}, '', url);

        // Refresh the message list
        refreshMessages();
    }

    // Function to refresh messages
    function refreshMessages() {
        if (window.messageState && window.messageState.isLoading) return;

        if (!window.messageState) {
            window.messageState = {
                isLoading: false
            };
        }

        window.messageState.isLoading = true;

        // Show loading indicator
        const listContainer = document.getElementById('message-list-container');
        listContainer.innerHTML = `
            <div class="text-center p-4">
                <div class="d-flex justify-content-center">
                    <div class="ph-briefcase loading-icon"></div>
                </div>
                <p class="mt-2">{{ __('Memuat pesan...') }}</p>
            </div>
        `;

        // Call Livewire method to refresh
        window.livewire.emit('refreshMessageList');

        // Reset loading state after a delay
        setTimeout(() => {
            if (window.messageState) {
                window.messageState.isLoading = false;
            }
        }, 1000);
    }

    // Check if there's a thread ID in the URL and show the detail section
    document.addEventListener('DOMContentLoaded', function() {
        const urlParams = new URLSearchParams(window.location.search);
        const threadId = urlParams.get('pesan_id');
        let currentFilter = urlParams.get('filter') || 'all';

        if (threadId) {
            setTimeout(function() {
                showMessageDetail();
            }, 500);
        }

        // Setup back button
        document.getElementById('back-to-list').addEventListener('click', function() {
            hideMessageDetail();
        });

        // Apply initial filter from URL if exists
        if (currentFilter && currentFilter !== 'all') {
            // Highlight the active card
            const activeCard = document.querySelector(`.message-stat-card[onclick*="applyFilter('${currentFilter}')"]`);
            if (activeCard) {
                activeCard.classList.add('active');
            }

            // Update filter label
            const filterLabels = {
                'all': 'Semua Pesan',
                'company': 'Pesan Perusahaan',
                'candidate': 'Pesan Pencaker',
                'admin': 'Pesan Admin'
            };
            document.getElementById('current-filter-label').textContent = filterLabels[currentFilter] || 'Semua Pesan';
        }

        // Setup reset filter button
        document.getElementById('reset-filter').addEventListener('click', function() {
            resetFilter();
        });

        // Setup refresh button
        document.getElementById('refresh-messages').addEventListener('click', function() {
            refreshMessages();
        });

        // Setup search functionality
        document.getElementById('search-button').addEventListener('click', function() {
            const searchTerm = document.getElementById('message-search').value.trim();
            if (searchTerm) {
                searchMessages(searchTerm);
            }
        });

        // Allow search on Enter key
        document.getElementById('message-search').addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                const searchTerm = this.value.trim();
                if (searchTerm) {
                    searchMessages(searchTerm);
                }
            }
        });
    });

    window.addEventListener('updateUrl', event => {
        const threadId = event.detail.threadId;
        const url = new URL(window.location);
        url.searchParams.set('pesan_id', threadId);
        window.history.pushState({}, '', url);
    });

    window.addEventListener('updateUrlWithoutReload', event => {
        const url = event.detail.url;
        window.history.pushState({}, '', url);
    });

    window.addEventListener('openMessageModal', event => {
        // Show message detail section
        showMessageDetail();
    });

    // Show toast messages
    window.addEventListener('show-toast', event => {
        const { type, message } = event.detail;
        // Implement your toast notification here using Toastr
        toastr[type](message);
    });

    // Show toast messages (alternative event name)
    window.addEventListener('showToast', event => {
        const { type, message } = event.detail;
        // Implement your toast notification here using Toastr
        toastr[type](message);
    });

    // Hide message loading spinner
    window.addEventListener('hideMessageLoading', () => {
        if (document.getElementById('message-loading')) {
            document.getElementById('message-loading').style.display = 'none';
        }
    });
</script>
@endsection
