@extends('backend.layouts.app')

@section('title')
    {{ __('<PERSON>esan') }}
@endsection

@section('content')
    <div class="container-fluid">
        <div class="row">
            <div class="col-md-12">
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title line-height-36">{{ __('Pesan') }}</h3>
                        <div class="float-right d-flex">
                            <a href="{{ route('admin.inbox.broadcast') }}" class="btn bg-info mr-2 d-flex align-items-center justify-content-center">
                                <i class="fas fa-bullhorn"></i>&nbsp; {{ __('Kirim Broadcast') }}
                            </a>
                            <a href="{{ route('admin.inbox.create') }}" class="btn bg-primary d-flex align-items-center justify-content-center">
                                <i class="fas fa-plus"></i>&nbsp; {{ __('<PERSON><PERSON>') }}
                            </a>
                        </div>
                    </div>
                    <div class="card-body">
                        <!-- Stats Cards -->
                        <div class="row mb-4">
                            <div class="col-md-3">
                                <a href="{{ route('admin.inbox.index', ['filter' => 'all']) }}" class="text-decoration-none">
                                    <div class="card bg-light message-stat-card {{ $filter == 'all' ? 'active' : '' }}">
                                        <div class="card-body">
                                            <div class="d-flex justify-content-between align-items-center">
                                                <div>
                                                    <h6 class="mb-0">{{ __('Total Pesan') }}</h6>
                                                    <h3 class="mt-2 mb-0">{{ $stats['total'] }}</h3>
                                                </div>
                                                <div class="bg-primary rounded-circle p-3 d-flex align-items-center justify-content-center">
                                                    <i class="fas fa-envelope text-white"></i>
                                                </div>
                                            </div>
                                            <div class="mt-2">
                                                <span class="badge bg-primary">{{ $stats['read'] }} Dibaca</span>
                                                <span class="badge bg-danger">{{ $stats['unread'] }} Belum Dibaca</span>
                                            </div>
                                        </div>
                                    </div>
                                </a>
                            </div>
                            <div class="col-md-3">
                                <a href="{{ route('admin.inbox.index', ['filter' => 'company']) }}" class="text-decoration-none">
                                    <div class="card bg-light message-stat-card {{ $filter == 'company' ? 'active' : '' }}">
                                        <div class="card-body">
                                            <div class="d-flex justify-content-between align-items-center">
                                                <div>
                                                    <h6 class="mb-0">{{ __('Pesan Perusahaan') }}</h6>
                                                    <h3 class="mt-2 mb-0">{{ $stats['company'] }}</h3>
                                                </div>
                                                <div class="bg-success rounded-circle p-3 d-flex align-items-center justify-content-center">
                                                    <i class="fas fa-building text-white"></i>
                                                </div>
                                            </div>
                                            <div class="mt-2">
                                                <span class="text-muted">Klik untuk filter</span>
                                            </div>
                                        </div>
                                    </div>
                                </a>
                            </div>
                            <div class="col-md-3">
                                <a href="{{ route('admin.inbox.index', ['filter' => 'candidate']) }}" class="text-decoration-none">
                                    <div class="card bg-light message-stat-card {{ $filter == 'candidate' ? 'active' : '' }}">
                                        <div class="card-body">
                                            <div class="d-flex justify-content-between align-items-center">
                                                <div>
                                                    <h6 class="mb-0">{{ __('Pesan Pencaker') }}</h6>
                                                    <h3 class="mt-2 mb-0">{{ $stats['candidate'] }}</h3>
                                                </div>
                                                <div class="bg-info rounded-circle p-3 d-flex align-items-center justify-content-center">
                                                    <i class="fas fa-user text-white"></i>
                                                </div>
                                            </div>
                                            <div class="mt-2">
                                                <span class="text-muted">Klik untuk filter</span>
                                            </div>
                                        </div>
                                    </div>
                                </a>
                            </div>
                            <div class="col-md-3">
                                <a href="{{ route('admin.inbox.index', ['filter' => 'admin']) }}" class="text-decoration-none">
                                    <div class="card bg-light message-stat-card {{ $filter == 'admin' ? 'active' : '' }}">
                                        <div class="card-body">
                                            <div class="d-flex justify-content-between align-items-center">
                                                <div>
                                                    <h6 class="mb-0">{{ __('Pesan Admin') }}</h6>
                                                    <h3 class="mt-2 mb-0">{{ $stats['admin'] }}</h3>
                                                </div>
                                                <div class="bg-warning rounded-circle p-3 d-flex align-items-center justify-content-center">
                                                    <i class="fas fa-user-shield text-white"></i>
                                                </div>
                                            </div>
                                            <div class="mt-2">
                                                <span class="text-muted">Klik untuk filter</span>
                                            </div>
                                        </div>
                                    </div>
                                </a>
                            </div>
                        </div>

                        <!-- Search Bar -->
                        <div class="row mb-4">
                            <div class="col-md-12">
                                <form action="{{ route('admin.inbox.index') }}" method="GET" class="mb-0">
                                    <input type="hidden" name="filter" value="{{ $filter }}">
                                    <div class="input-group">
                                        <input type="text" class="form-control" name="search" value="{{ $search ?? '' }}" placeholder="Cari pesan berdasarkan nama pengirim, penerima, judul, atau loker...">
                                        <div class="input-group-append">
                                            <button class="btn btn-primary" type="submit">
                                                <i class="fas fa-search"></i>
                                            </button>
                                            @if($search)
                                                <a href="{{ route('admin.inbox.index', ['filter' => $filter]) }}" class="btn btn-secondary">
                                                    <i class="fas fa-times"></i>
                                                </a>
                                            @endif
                                        </div>
                                    </div>
                                </form>
                            </div>
                        </div>

                        <div class="row">
                            @if($thread)
                                <!-- Message Detail Section -->
                                <div class="col-12" id="message-detail-section">
                                    <div class="card">
                                        <div class="card-header d-flex justify-content-between align-items-center">
                                            <h5>{{ $thread->subject }}</h5>
                                            <a href="{{ route('admin.inbox.index', ['filter' => $filter, 'search' => $search]) }}" class="btn btn-outline-secondary btn-sm">
                                                <i class="fas fa-arrow-left me-1"></i> {{ __('Kembali ke Daftar Pesan') }}
                                            </a>
                                        </div>
                                        <div class="card-body">
                                            <div class="message-info mb-4">
                                                <div class="d-flex justify-content-between align-items-center mb-3">
                                                    <div>
                                                        @if($thread->company)
                                                            <span class="badge bg-success">Perusahaan: {{ $thread->company->user->name }}</span>
                                                        @endif
                                                        @if($thread->candidate)
                                                            <span class="badge bg-info">Pencaker: {{ $thread->candidate->user->name }}</span>
                                                        @endif
                                                        @if($thread->job)
                                                            <span class="badge bg-secondary">Loker: {{ $thread->job->title }}</span>
                                                        @endif
                                                    </div>
                                                    <div>
                                                        <span class="text-muted">{{ $thread->created_at->format('d M Y H:i') }}</span>
                                                    </div>
                                                </div>
                                            </div>

                                            <div class="message-thread">
                                                @if($hasMoreMessages)
                                                    <div class="text-center mb-3">
                                                        <button type="button" class="btn btn-outline-primary btn-sm load-more-messages"
                                                            data-thread-id="{{ $thread->id }}"
                                                            data-skip="5">
                                                            <i class="fas fa-history me-1"></i> {{ __('Muat Pesan Sebelumnya') }}
                                                            <span class="badge bg-light text-dark ms-1">{{ $totalMessages - 5 }}</span>
                                                        </button>
                                                    </div>
                                                @endif

                                                <div id="message-container">
                                                @foreach($messages as $message)
                                                    @php
                                                        $isSender = $message->sender_id == auth()->guard('admin')->id();
                                                        $senderName = 'Pengguna Tidak Dikenal';
                                                        $avatar = '';

                                                        if ($message->sender_type == 'admin') {
                                                            $senderName = 'Admin';
                                                            $avatar = null; // Akan menggunakan ikon admin
                                                        } elseif ($message->sender) {
                                                            if ($message->sender->role == 'company') {
                                                                // Gunakan helper function formatCompanyName untuk menampilkan nama perusahaan dengan prefix badan hukum
                                                                $senderName = formatCompanyName($message->sender) ?? 'Perusahaan';
                                                                $avatar = $message->sender->company->logo_url ?? '';
                                                            } else {
                                                                $senderName = $message->sender->name ?? 'Pengguna Tidak Dikenal';
                                                                $avatar = $message->sender->image_url ?? '';
                                                            }
                                                        }
                                                    @endphp

                                                    <div class="message-item mb-4">
                                                        <!-- Timestamp di atas nama pengirim -->
                                                        <div class="message-time small text-muted mb-1 {{ $isSender ? 'text-end' : 'text-start' }}">
                                                            {{ $message->created_at->format('d/m/Y - H:i') }}
                                                        </div>

                                                        <div class="d-flex {{ $isSender ? 'justify-content-end' : 'justify-content-start' }}">
                                                            @if(!$isSender)
                                                                <div class="message-avatar me-2">
                                                                    @if($avatar)
                                                                        <img src="{{ $avatar }}" alt="Avatar" class="rounded-circle" width="40" height="40">
                                                                    @else
                                                                        <div class="rounded-circle bg-secondary d-flex align-items-center justify-content-center" style="width: 40px; height: 40px;">
                                                                            <i class="fas fa-user text-white"></i>
                                                                        </div>
                                                                    @endif
                                                                </div>
                                                            @endif

                                                            <div class="message-content" style="max-width: 80%;">
                                                                <div class="message-sender mb-1 {{ $isSender ? 'text-end' : 'text-start' }}">
                                                                    <div class="fw-bold">
                                                                        {{ $senderName }}
                                                                    </div>
                                                                </div>

                                                                <div class="message-bubble p-3 {{ $isSender ? 'bg-primary-light text-dark' : 'bg-white text-dark' }}"
                                                                     style="border-radius: 10px; display: inline-block; border: 1px solid #e0e0e0; box-shadow: 0 1px 2px rgba(0,0,0,0.1);">
                                                                    {!! nl2br(e($message->body)) !!}

                                                                    @if(isset($message->attachment) && !empty($message->attachment))
                                                                        @php
                                                                            // Handle different attachment formats
                                                                            if (is_array($message->attachment)) {
                                                                                if (isset($message->attachment['url'])) {
                                                                                    // Format 1: Single attachment with direct url
                                                                                    $attachmentUrl = $message->attachment['url'];
                                                                                    $attachmentName = $message->attachment['name'] ?? 'Lampiran';
                                                                                    $attachmentType = $message->attachment['type'] ?? '';
                                                                                } elseif (isset($message->attachment[0]) && is_array($message->attachment[0])) {
                                                                                    // Format 2: Array of attachments with path
                                                                                    $attachment = $message->attachment[0];
                                                                                    $attachmentPath = isset($attachment['path']) ? 'storage/' . $attachment['path'] : '';
                                                                                    $attachmentUrl = asset($attachmentPath);
                                                                                    $attachmentName = $attachment['name'] ?? 'Lampiran';
                                                                                    $attachmentType = $attachment['type'] ?? '';
                                                                                } else {
                                                                                    // Unknown format
                                                                                    $attachmentUrl = '';
                                                                                    $attachmentName = 'Lampiran';
                                                                                    $attachmentType = '';
                                                                                }
                                                                            } else {
                                                                                // String format (unlikely but handle it)
                                                                                $attachmentUrl = $message->attachment;
                                                                                $attachmentName = 'Lampiran';
                                                                                $attachmentType = '';
                                                                            }

                                                                            // Determine file type from extension if not provided
                                                                            $extension = strtolower(pathinfo($attachmentUrl, PATHINFO_EXTENSION));
                                                                            $isImage = $attachmentType == 'image' || in_array($extension, ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'svg', 'webp']);
                                                                            $isPdf = $attachmentType == 'document' || in_array($extension, ['pdf']);
                                                                        @endphp

                                                                        @if($isImage)
                                                                            <div class="image-attachment mt-2">
                                                                                <a href="{{ $attachmentUrl }}" class="image-lightbox" data-caption="{{ $attachmentName }}">
                                                                                    <img src="{{ $attachmentUrl }}" alt="{{ $attachmentName }}" class="image-preview img-fluid rounded" style="max-height: 200px;">
                                                                                </a>
                                                                                <div class="mt-1">
                                                                                    <small class="text-muted">{{ $attachmentName }}</small>
                                                                                </div>
                                                                            </div>
                                                                        @elseif($isPdf)
                                                                            <div class="document-attachment">
                                                                                <div class="document-icon">
                                                                                    <i class="fas fa-file-pdf"></i>
                                                                                </div>
                                                                                <div class="document-info">
                                                                                    <div class="text-truncate" title="{{ $attachmentName }}">{{ $attachmentName }}</div>
                                                                                    <small class="text-muted">PDF Document</small>
                                                                                </div>
                                                                                <div class="document-actions">
                                                                                    <button type="button" class="btn btn-sm btn-outline-primary pdf-preview-btn"
                                                                                        data-pdf-url="{{ $attachmentUrl }}"
                                                                                        data-pdf-name="{{ $attachmentName }}">
                                                                                        <i class="fas fa-eye"></i>
                                                                                    </button>
                                                                                    <a href="{{ $attachmentUrl }}" class="btn btn-sm btn-outline-success" download="{{ $attachmentName }}">
                                                                                        <i class="fas fa-download"></i>
                                                                                    </a>
                                                                                </div>
                                                                            </div>
                                                                        @else
                                                                            <div class="message-attachment mt-2 p-2 bg-light rounded border">
                                                                                <a href="{{ $attachmentUrl }}" target="_blank" class="d-flex align-items-center">
                                                                                    <i class="fas fa-paperclip me-2"></i>
                                                                                    <span class="text-truncate" title="{{ $attachmentName }}">{{ $attachmentName }}</span>
                                                                                </a>
                                                                            </div>
                                                                        @endif
                                                                    @endif
                                                                </div>
                                                            </div>

                                                            @if($isSender)
                                                                <div class="message-avatar ms-2">
                                                                    @if($avatar)
                                                                        <img src="{{ $avatar }}" alt="Avatar" class="rounded-circle" width="40" height="40">
                                                                    @else
                                                                        <div class="rounded-circle bg-primary d-flex align-items-center justify-content-center" style="width: 40px; height: 40px;">
                                                                            <i class="fas fa-user-shield text-white"></i>
                                                                        </div>
                                                                    @endif
                                                                </div>
                                                            @endif
                                                        </div>
                                                    </div>
                                                @endforeach
                                                </div>
                                            </div>

                                            <div class="message-reply mt-4">
                                                <form id="reply-form" action="{{ route('admin.inbox.reply') }}" method="POST" enctype="multipart/form-data">
                                                    @csrf
                                                    <input type="hidden" name="thread_id" value="{{ $thread->id }}">
                                                    <div class="form-group">
                                                        <label for="message">{{ __('Balas Pesan') }}</label>
                                                        <textarea class="form-control" id="message" name="message" rows="3" required></textarea>
                                                    </div>

                                                    <div class="form-group">
                                                        <div class="d-flex justify-content-between align-items-center">
                                                            <div class="attachment-options">
                                                                <label for="file-attachment" class="btn btn-outline-secondary btn-sm me-2" data-bs-toggle="tooltip" title="{{ __('Lampirkan Dokumen') }}">
                                                                    <i class="fas fa-file-pdf"></i>
                                                                    <span class="ms-1">{{ __('Dokumen') }}</span>
                                                                </label>
                                                                <input type="file" id="file-attachment" name="document_attachment" accept=".pdf,.doc,.docx" class="d-none">

                                                                <label for="image-attachment" class="btn btn-outline-secondary btn-sm" data-bs-toggle="tooltip" title="{{ __('Lampirkan Gambar') }}">
                                                                    <i class="fas fa-image"></i>
                                                                    <span class="ms-1">{{ __('Gambar') }}</span>
                                                                </label>
                                                                <input type="file" id="image-attachment" name="image_attachment" accept="image/*" class="d-none">

                                                                <div id="attachment-preview" class="d-none ms-2">
                                                                    <span class="badge bg-light text-dark p-2">
                                                                        <i class="fas fa-paperclip me-1"></i>
                                                                        <span id="attachment-name"></span>
                                                                        <button type="button" class="btn-close ms-2" id="remove-attachment" aria-label="Close"></button>
                                                                    </span>
                                                                </div>
                                                            </div>

                                                            <div class="reply-options">
                                                                <div class="form-check form-switch me-3">
                                                                    <input type="checkbox" class="form-check-input" id="can_reply" name="can_reply" value="1" checked>
                                                                    <label class="form-check-label" for="can_reply">{{ __('Dapat Dibalas') }}</label>
                                                                    <small class="d-block text-muted">{{ __('Jika dicentang, penerima dapat membalas pesan ini.') }}</small>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>

                                                    <div class="form-group text-end">
                                                        <button type="submit" id="reply-button" class="btn btn-primary">
                                                            <i class="fas fa-paper-plane me-1"></i> {{ __('Kirim Balasan') }}
                                                        </button>
                                                    </div>
                                                </form>

                                                <div id="sending-indicator" class="d-none text-center mt-3">
                                                    <div class="spinner-border text-primary" role="status">
                                                        <span class="visually-hidden">Loading...</span>
                                                    </div>
                                                    <p class="mt-2">{{ __('Mengirim pesan...') }}</p>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            @else
                                <!-- Message List -->
                                <div class="col-12" id="message-list-section">
                                    <div class="card">
                                        <div class="card-header d-flex justify-content-between align-items-center">
                                            <h5>{{ __('Daftar Pesan') }}</h5>
                                            <div class="d-flex align-items-center">
                                                <span class="badge bg-secondary me-2">
                                                    @if($filter == 'all')
                                                        Semua Pesan
                                                    @elseif($filter == 'company')
                                                        Pesan Perusahaan
                                                    @elseif($filter == 'candidate')
                                                        Pesan Pencaker
                                                    @elseif($filter == 'admin')
                                                        Pesan Admin
                                                    @endif
                                                    @if($search)
                                                        - Pencarian: "{{ $search }}"
                                                    @endif
                                                </span>
                                                <a href="{{ route('admin.inbox.index') }}" class="btn btn-sm btn-outline-secondary">
                                                    <i class="fas fa-sync-alt"></i> {{ __('Refresh') }}
                                                </a>
                                            </div>
                                        </div>
                                        <div class="card-body">
                                            @if($threads->count() > 0)
                                                <div class="table-responsive">
                                                    <table class="table message-table">
                                                        <thead>
                                                            <tr>
                                                                <th width="5%">#</th>
                                                                <th width="40%">{{ __('Subjek') }}</th>
                                                                <th width="20%">{{ __('Pengirim/Penerima') }}</th>
                                                                <th width="15%">{{ __('Tanggal') }}</th>
                                                                <th width="10%">{{ __('Status') }}</th>
                                                                <th width="10%">{{ __('Aksi') }}</th>
                                                            </tr>
                                                        </thead>
                                                        <tbody>
                                                            @foreach($threads as $thread)
                                                                @php
                                                                    $unreadCount = $thread->unreadMessages->count();
                                                                    $isUnread = $unreadCount > 0;
                                                                @endphp
                                                                <tr class="{{ $isUnread ? 'unread' : '' }}">
                                                                    <td>{{ $loop->iteration }}</td>
                                                                    <td>
                                                                        <div class="message-title">
                                                                            {{ $thread->subject }}
                                                                        </div>
                                                                        @if($thread->job)
                                                                            <div class="message-job">
                                                                                <i class="fas fa-briefcase text-muted"></i> {{ $thread->job->title }}
                                                                            </div>
                                                                        @endif
                                                                    </td>
                                                                    <td>
                                                                        @if($thread->company)
                                                                            <div class="d-flex align-items-center">
                                                                                <div class="me-2">
                                                                                    @if($thread->company->logo)
                                                                                        <img src="{{ asset($thread->company->logo) }}" alt="Logo" class="avatar">
                                                                                    @else
                                                                                        <div class="bg-secondary rounded-circle d-flex align-items-center justify-content-center" style="width: 40px; height: 40px;">
                                                                                            <i class="fas fa-building text-white"></i>
                                                                                        </div>
                                                                                    @endif
                                                                                </div>
                                                                                <div>
                                                                                    {{ Str::limit($thread->company->user->name, 20) }}
                                                                                    <div class="small text-muted">Perusahaan</div>
                                                                                </div>
                                                                            </div>
                                                                        @elseif($thread->candidate)
                                                                            <div class="d-flex align-items-center">
                                                                                <div class="me-2">
                                                                                    @if($thread->candidate->photo)
                                                                                        <img src="{{ asset($thread->candidate->photo) }}" alt="Photo" class="avatar">
                                                                                    @else
                                                                                        <div class="bg-info rounded-circle d-flex align-items-center justify-content-center" style="width: 40px; height: 40px;">
                                                                                            <i class="fas fa-user text-white"></i>
                                                                                        </div>
                                                                                    @endif
                                                                                </div>
                                                                                <div>
                                                                                    {{ Str::limit($thread->candidate->user->name, 20) }}
                                                                                    <div class="small text-muted">Pencaker</div>
                                                                                </div>
                                                                            </div>
                                                                        @endif
                                                                    </td>
                                                                    <td>
                                                                        <div class="message-date">
                                                                            {{ $thread->updated_at->format('d M Y') }}
                                                                        </div>
                                                                        <div class="message-time small text-muted">
                                                                            {{ $thread->updated_at->format('H:i') }}
                                                                        </div>
                                                                    </td>
                                                                    <td>
                                                                        @if($isUnread)
                                                                            <span class="badge bg-danger">{{ $unreadCount }} Belum Dibaca</span>
                                                                        @else
                                                                            <span class="badge bg-success">Dibaca</span>
                                                                        @endif
                                                                    </td>
                                                                    <td>
                                                                        <a href="{{ route('admin.inbox.index', ['pesan_id' => $thread->id, 'filter' => $filter, 'search' => $search]) }}" class="btn btn-sm btn-primary">
                                                                            <i class="fas fa-eye"></i>
                                                                        </a>
                                                                    </td>
                                                                </tr>
                                                            @endforeach
                                                        </tbody>
                                                    </table>
                                                </div>

                                                <div class="mt-4">
                                                    {{ $threads->appends(['filter' => $filter, 'search' => $search])->links() }}
                                                </div>
                                            @else
                                                <div class="text-center py-5">
                                                    <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
                                                    <h5>{{ __('Tidak ada pesan ditemukan') }}</h5>
                                                    <p class="text-muted">{{ __('Belum ada pesan yang tersedia atau sesuai dengan filter yang dipilih.') }}</p>
                                                </div>
                                            @endif
                                        </div>
                                    </div>
                                </div>
                            @endif
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!-- Modal untuk preview PDF -->
    <div class="modal fade" id="pdfPreviewModal" tabindex="-1" aria-labelledby="pdfPreviewModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="pdfPreviewModalLabel">Preview PDF: <span id="pdfFileName"></span></h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="ratio ratio-16x9">
                        <iframe id="pdfPreviewFrame" src="" title="PDF Preview" style="width: 100%; height: 500px; border: none;"></iframe>
                    </div>
                </div>
                <div class="modal-footer">
                    <a id="pdfDownloadLink" href="#" class="btn btn-success" download>
                        <i class="fas fa-download"></i> Download
                    </a>
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">{{ __('Tutup') }}</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal untuk lightbox gambar -->
    <div class="modal fade" id="imageLightboxModal" tabindex="-1" aria-labelledby="imageLightboxModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="imageLightboxModalLabel"><span id="lightboxCaption"></span></h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body text-center">
                    <img id="lightboxImage" src="" alt="Preview" class="img-fluid">
                </div>
                <div class="modal-footer">
                    <a id="imageDownloadLink" href="#" class="btn btn-success" download>
                        <i class="fas fa-download"></i> Download
                    </a>
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">{{ __('Tutup') }}</button>
                </div>
            </div>
        </div>
    </div>
@endsection

@section('script')
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Attachment handling
        const fileAttachment = document.getElementById('file-attachment');
        const imageAttachment = document.getElementById('image-attachment');
        const attachmentPreview = document.getElementById('attachment-preview');
        const attachmentName = document.getElementById('attachment-name');
        const removeAttachment = document.getElementById('remove-attachment');

        let currentAttachment = null;
        let currentAttachmentType = null;

        // Handle file attachment
        if (fileAttachment) {
            fileAttachment.addEventListener('change', function(e) {
                if (this.files.length > 0) {
                    const file = this.files[0];
                    if (file.size > 5 * 1024 * 1024) { // 5MB limit
                        alert('{{ __("Ukuran file terlalu besar. Maksimal 5MB.") }}');
                        this.value = '';
                        return;
                    }

                    // Clear any existing image attachment
                    if (imageAttachment) {
                        imageAttachment.value = '';
                    }

                    currentAttachment = file;
                    currentAttachmentType = 'document';
                    attachmentName.textContent = file.name;
                    attachmentPreview.classList.remove('d-none');

                    // Log for debugging
                    console.log('Document attachment selected:', file.name, file.type);
                }
            });
        }

        // Handle image attachment
        if (imageAttachment) {
            imageAttachment.addEventListener('change', function(e) {
                if (this.files.length > 0) {
                    const file = this.files[0];
                    if (file.size > 5 * 1024 * 1024) { // 5MB limit
                        alert('{{ __("Ukuran file terlalu besar. Maksimal 5MB.") }}');
                        this.value = '';
                        return;
                    }

                    // Clear any existing document attachment
                    if (fileAttachment) {
                        fileAttachment.value = '';
                    }

                    currentAttachment = file;
                    currentAttachmentType = 'image';
                    attachmentName.textContent = file.name;
                    attachmentPreview.classList.remove('d-none');

                    // Log for debugging
                    console.log('Image attachment selected:', file.name, file.type);
                }
            });
        }

        // Handle remove attachment
        if (removeAttachment) {
            removeAttachment.addEventListener('click', function() {
                if (currentAttachmentType === 'document' && fileAttachment) {
                    fileAttachment.value = '';
                } else if (currentAttachmentType === 'image' && imageAttachment) {
                    imageAttachment.value = '';
                }

                currentAttachment = null;
                currentAttachmentType = null;
                attachmentPreview.classList.add('d-none');
            });
        }

        // Initialize tooltips
        try {
            // Bootstrap 5
            const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
            tooltipTriggerList.map(function (tooltipTriggerEl) {
                return new bootstrap.Tooltip(tooltipTriggerEl);
            });
        } catch (e) {
            // Bootstrap 4 fallback
            $('[data-toggle="tooltip"]').tooltip();
        }

        // Handle reply form submission with AJAX
        const replyForm = document.getElementById('reply-form');
        const messageInput = document.getElementById('message');
        const replyButton = document.getElementById('reply-button');
        const sendingIndicator = document.getElementById('sending-indicator');
        const messageContainer = document.getElementById('message-container');

        if (replyForm) {
            replyForm.addEventListener('submit', function(e) {
                e.preventDefault();

                // Validate form
                if (!messageInput.value.trim() && !currentAttachment) {
                    alert('{{ __("Silakan ketik pesan atau lampirkan file.") }}');
                    return;
                }

                // Log form data for debugging
                console.log('Form validation passed:');
                console.log('- Message:', messageInput.value.trim() ? 'Yes' : 'No');
                console.log('- Attachment:', currentAttachment ? currentAttachmentType : 'No');

                // Disable form elements and show loading indicator
                messageInput.disabled = true;
                replyButton.disabled = true;
                replyForm.style.opacity = '0.7';
                sendingIndicator.classList.remove('d-none');

                // Create FormData object
                const formData = new FormData(replyForm);

                // Log form data for debugging
                console.log('Sending message with attachment type:', currentAttachmentType);

                // Send AJAX request
                fetch(replyForm.action, {
                    method: 'POST',
                    body: formData,
                    headers: {
                        'X-Requested-With': 'XMLHttpRequest',
                        'X-CSRF-TOKEN': '{{ csrf_token() }}',
                        'Accept': 'application/json'
                    }
                })
                .then(response => {
                    console.log('Response status:', response.status);
                    if (!response.ok) {
                        return response.json().then(errorData => {
                            throw new Error(errorData.message || 'Server error: ' + response.status);
                        });
                    }
                    return response.json();
                })
                .then(data => {
                    console.log('Response data:', data);
                    if (data.success) {
                        // Append new message to container
                        if (messageContainer) {
                            messageContainer.insertAdjacentHTML('beforeend', data.html);
                        }

                        // Reset form
                        messageInput.value = '';
                        if (currentAttachment) {
                            if (currentAttachmentType === 'document' && fileAttachment) {
                                fileAttachment.value = '';
                            } else if (currentAttachmentType === 'image' && imageAttachment) {
                                imageAttachment.value = '';
                            }

                            currentAttachment = null;
                            currentAttachmentType = null;
                            attachmentPreview.classList.add('d-none');
                        }

                        // Scroll to bottom
                        const messageThread = document.querySelector('.message-thread');
                        if (messageThread) {
                            messageThread.scrollTop = messageThread.scrollHeight;
                        }
                    } else {
                        // Show error
                        alert(data.message || 'Gagal mengirim pesan. Silakan coba lagi.');
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert(error.message || 'Terjadi kesalahan. Silakan coba lagi.');
                })
                .finally(() => {
                    // Re-enable form elements and hide loading indicator
                    messageInput.disabled = false;
                    replyButton.disabled = false;
                    replyForm.style.opacity = '1';
                    sendingIndicator.classList.add('d-none');
                });
            });
        }

        // Load More Messages
        const loadMoreBtn = document.querySelector('.load-more-messages');
        if (loadMoreBtn) {
            loadMoreBtn.addEventListener('click', function() {
                const threadId = this.getAttribute('data-thread-id');
                const skip = parseInt(this.getAttribute('data-skip'));
                const button = this;

                // Disable button and show loading
                button.disabled = true;
                const originalText = button.innerHTML;
                button.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Memuat...';

                // Make AJAX request
                fetch(`{{ route('admin.inbox.load-more') }}?thread_id=${threadId}&skip=${skip}`, {
                    headers: {
                        'X-Requested-With': 'XMLHttpRequest',
                        'Accept': 'application/json',
                        'X-CSRF-TOKEN': '{{ csrf_token() }}'
                    }
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        // Prepend messages to container
                        const messageContainer = document.getElementById('message-container');
                        if (messageContainer) {
                            const tempDiv = document.createElement('div');
                            tempDiv.innerHTML = data.html;

                            // Insert before first child
                            while (tempDiv.firstChild) {
                                messageContainer.insertBefore(tempDiv.firstChild, messageContainer.firstChild);
                            }
                        }

                        // Update button
                        if (data.hasMoreMessages) {
                            button.setAttribute('data-skip', data.newSkip);
                            button.innerHTML = `<i class="fas fa-history me-1"></i> {{ __('Muat Pesan Sebelumnya') }} <span class="badge bg-light text-dark ms-1">${data.remainingCount}</span>`;
                        } else {
                            // No more messages, remove button
                            button.parentNode.remove();
                        }
                    } else {
                        // Show error
                        button.innerHTML = originalText;
                        alert('Gagal memuat pesan sebelumnya. Silakan coba lagi.');
                    }

                    // Re-enable button
                    button.disabled = false;
                })
                .catch(error => {
                    console.error('Error:', error);
                    button.innerHTML = originalText;
                    button.disabled = false;
                    alert('Terjadi kesalahan. Silakan coba lagi.');
                });
            });
        }

        // PDF Preview Modal
        const pdfPreviewModal = document.getElementById('pdfPreviewModal');
        if (pdfPreviewModal) {
            let pdfPreviewModalInstance;
            try {
                // Try Bootstrap 5 modal
                pdfPreviewModalInstance = new bootstrap.Modal(pdfPreviewModal);
            } catch (e) {
                // Fallback to jQuery for Bootstrap 4
                pdfPreviewModalInstance = {
                    show: function() {
                        $(pdfPreviewModal).modal('show');
                    },
                    hide: function() {
                        $(pdfPreviewModal).modal('hide');
                    }
                };
            }

            const pdfPreviewFrame = document.getElementById('pdfPreviewFrame');
            const pdfFileName = document.getElementById('pdfFileName');
            const pdfDownloadLink = document.getElementById('pdfDownloadLink');

            // Event listener untuk tombol preview PDF
            document.addEventListener('click', function(e) {
                if (e.target.classList.contains('pdf-preview-btn') || e.target.closest('.pdf-preview-btn')) {
                    const button = e.target.classList.contains('pdf-preview-btn') ? e.target : e.target.closest('.pdf-preview-btn');
                    const pdfUrl = button.getAttribute('data-pdf-url');
                    const pdfName = button.getAttribute('data-pdf-name');

                    console.log('PDF Preview clicked:', pdfUrl, pdfName);

                    if (pdfUrl && pdfPreviewFrame) {
                        // Fix URL if needed (remove any double slashes except after protocol)
                        const fixedUrl = pdfUrl.replace(/(https?:\/\/)|(\/)+/g, function(match, protocol) {
                            if (protocol) return protocol;
                            return '/';
                        });

                        console.log('Loading PDF from URL:', fixedUrl);
                        pdfPreviewFrame.src = fixedUrl;

                        if (pdfFileName) {
                            pdfFileName.textContent = pdfName || 'Dokumen';
                        }
                        if (pdfDownloadLink) {
                            pdfDownloadLink.href = fixedUrl;
                            pdfDownloadLink.setAttribute('download', pdfName || 'dokumen.pdf');
                        }
                        pdfPreviewModalInstance.show();
                    }
                }
            });

            // Clear iframe when modal is closed
            pdfPreviewModal.addEventListener('hidden.bs.modal', function() {
                if (pdfPreviewFrame) {
                    pdfPreviewFrame.src = '';
                }
            });

            // For Bootstrap 4 compatibility
            $(pdfPreviewModal).on('hidden.bs.modal', function() {
                if (pdfPreviewFrame) {
                    pdfPreviewFrame.src = '';
                }
            });
        }

        // Image Lightbox
        const imageLightboxModal = document.getElementById('imageLightboxModal');
        if (imageLightboxModal) {
            let imageLightboxModalInstance;
            try {
                // Try Bootstrap 5 modal
                imageLightboxModalInstance = new bootstrap.Modal(imageLightboxModal);
            } catch (e) {
                // Fallback to jQuery for Bootstrap 4
                imageLightboxModalInstance = {
                    show: function() {
                        $(imageLightboxModal).modal('show');
                    },
                    hide: function() {
                        $(imageLightboxModal).modal('hide');
                    }
                };
            }

            const lightboxImage = document.getElementById('lightboxImage');
            const lightboxCaption = document.getElementById('lightboxCaption');
            const imageDownloadLink = document.getElementById('imageDownloadLink');

            document.addEventListener('click', function(e) {
                if (e.target.closest('.image-lightbox')) {
                    e.preventDefault();
                    const link = e.target.closest('.image-lightbox');
                    const imageUrl = link.getAttribute('href');
                    const caption = link.getAttribute('data-caption');

                    console.log('Image Lightbox clicked:', imageUrl, caption);

                    if (imageUrl && lightboxImage) {
                        // Fix URL if needed (remove any double slashes except after protocol)
                        const fixedUrl = imageUrl.replace(/(https?:\/\/)|(\/)+/g, function(match, protocol) {
                            if (protocol) return protocol;
                            return '/';
                        });

                        console.log('Loading image from URL:', fixedUrl);
                        lightboxImage.src = fixedUrl;

                        if (lightboxCaption) {
                            lightboxCaption.textContent = caption || '';
                        }
                        if (imageDownloadLink) {
                            imageDownloadLink.href = fixedUrl;
                            imageDownloadLink.setAttribute('download', caption || 'image.jpg');
                        }
                        imageLightboxModalInstance.show();
                    }
                }
            });
        }
    });
</script>
@endsection

@section('style')
    <style>
        /* Warna background untuk bubble chat */
        .bg-primary-light {
            background-color: rgba(144, 238, 144, 0.3) !important; /* Light green transparent */
        }

        /* Style untuk pesan */
        .message-item {
            margin-bottom: 20px;
        }

        .message-sender {
            font-weight: bold;
            margin-bottom: 5px;
        }

        .message-time {
            font-size: 0.8rem;
            font-style: italic;
            color: #6c757d;
        }

        .message-bubble {
            max-width: 80%;
            word-wrap: break-word;
        }

        /* Style untuk preview gambar */
        .image-attachment img {
            max-width: 200px;
            max-height: 200px;
            cursor: pointer;
            border-radius: 5px;
            transition: transform 0.2s;
        }

        .image-attachment img:hover {
            transform: scale(1.05);
        }

        /* Style untuk preview dokumen */
        .document-attachment {
            display: flex;
            align-items: center;
            padding: 10px;
            background-color: #f8f9fa;
            border-radius: 5px;
            margin-top: 10px;
            border: 1px solid #dee2e6;
        }

        .document-attachment .document-icon {
            font-size: 24px;
            margin-right: 10px;
            color: #dc3545;
            flex-shrink: 0;
        }

        .document-attachment .document-info {
            flex-grow: 1;
            margin-right: 10px;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        .document-attachment .document-actions {
            display: flex;
            gap: 5px;
            flex-shrink: 0;
        }

        /* Lightbox styles */
        #lightboxImage {
            max-height: 80vh;
            max-width: 100%;
        }

        /* PDF Preview styles */
        #pdfPreviewFrame {
            width: 100%;
            height: 500px;
            border: none;
            display: block;
        }

        .ratio {
            position: relative;
            width: 100%;
        }

        .ratio-16x9 {
            padding-top: 56.25%;
        }

        .ratio > * {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
        }

        .card {
            overflow: hidden;
        }

        #message-detail-section .card {
            border-radius: 10px;
            box-shadow: 0 0 15px rgba(0, 0, 0, 0.05);
            border: 2px solid #138C79;
        }

        #message-detail-section .card-header {
            background-color: #f8f9fa;
            padding: 15px 20px;
            border-bottom: 2px solid #138C79;
        }

        #message-detail-section .card-header h5 {
            margin-bottom: 0;
            color: #333;
            font-weight: 600;
        }

        #message-list-section .card {
            border-radius: 10px;
            box-shadow: 0 0 15px rgba(0, 0, 0, 0.05);
            border: 2px solid #138C79;
        }

        #message-list-section .card-header {
            background-color: #f8f9fa;
            padding: 15px 20px;
            border-bottom: 2px solid #138C79;
        }

        #message-list-section .card-header h5 {
            margin-bottom: 0;
            color: #333;
            font-weight: 600;
        }

        /* Stats Card Styles */
        .message-stat-card {
            transition: all 0.3s ease;
            cursor: pointer;
            border: 2px solid transparent;
        }

        .message-stat-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        }

        .message-stat-card.active {
            border-color: #138C79;
            background-color: #f8f9fa;
        }

        .message-stat-card .rounded-circle {
            width: 50px;
            height: 50px;
        }

        /* Message Table Styles */
        .message-table {
            width: 100%;
        }

        .message-table th {
            background-color: #f8f9fa;
            font-weight: 600;
            padding: 12px;
            border-bottom: 2px solid #dee2e6;
        }

        .message-table td {
            padding: 12px;
            vertical-align: middle;
            border-bottom: 1px solid #dee2e6;
        }

        .message-table tr {
            transition: background-color 0.2s;
        }

        .message-table tr:hover {
            background-color: #f8f9fa;
        }

        .message-table .unread {
            font-weight: 600;
            background-color: #f0f7ff;
        }

        .message-table .avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            object-fit: cover;
        }

        .message-table .message-title {
            font-weight: 500;
            margin-bottom: 5px;
        }

        .message-table .message-job {
            font-size: 0.85rem;
            color: #6c757d;
        }

        .message-table .badge {
            font-size: 0.75rem;
            padding: 0.35em 0.65em;
        }

        .message-table .message-date {
            font-size: 0.85rem;
            color: #6c757d;
            white-space: nowrap;
        }

        /* Pagination Styles */
        .pagination {
            margin-bottom: 0;
        }

        .pagination .page-link {
            color: #138C79;
            border-radius: 0;
        }

        .pagination .page-item.active .page-link {
            background-color: #138C79;
            border-color: #138C79;
        }

        /* Search Bar Styles */
        .form-control {
            border-radius: 4px 0 0 4px;
            border-right: none;
        }

        .input-group-append .btn {
            border-radius: 0 4px 4px 0;
        }

        @media (max-width: 991.98px) {
            .message-stat-card {
                margin-bottom: 15px;
            }
        }
    </style>
@endsection

@section('script')
    <!-- Modal untuk preview PDF -->
    <div class="modal fade" id="pdfPreviewModal" tabindex="-1" aria-labelledby="pdfPreviewModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="pdfPreviewModalLabel">{{ __('Pratinjau Dokumen') }}</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="text-center mb-3" id="pdfFileName"></div>
                    <div class="ratio ratio-16x9">
                        <iframe id="pdfPreviewFrame" src="" frameborder="0" allowfullscreen></iframe>
                    </div>
                </div>
                <div class="modal-footer">
                    <a href="#" class="btn btn-primary" id="pdfDownloadLink" target="_blank" download>
                        <i class="fas fa-download me-1"></i> {{ __('Unduh') }}
                    </a>
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">{{ __('Tutup') }}</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal untuk preview gambar -->
    <div class="modal fade" id="imagePreviewModal" tabindex="-1" aria-labelledby="imagePreviewModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="imagePreviewModalLabel">{{ __('Pratinjau Gambar') }}</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body text-center">
                    <img id="imagePreviewElement" src="" alt="Preview" class="img-fluid">
                </div>
                <div class="modal-footer">
                    <a href="#" class="btn btn-primary" id="imageDownloadLink" target="_blank" download>
                        <i class="fas fa-download me-1"></i> {{ __('Unduh') }}
                    </a>
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">{{ __('Tutup') }}</button>
                </div>
            </div>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Inisialisasi modal
            const pdfPreviewModal = document.getElementById('pdfPreviewModal');
            const pdfPreviewFrame = document.getElementById('pdfPreviewFrame');
            const pdfFileName = document.getElementById('pdfFileName');
            const pdfDownloadLink = document.getElementById('pdfDownloadLink');

            let pdfPreviewModalInstance;
            try {
                // Try Bootstrap 5 modal
                pdfPreviewModalInstance = new bootstrap.Modal(pdfPreviewModal);
            } catch (e) {
                // Fallback to jQuery for Bootstrap 4
                pdfPreviewModalInstance = {
                    show: function() {
                        $(pdfPreviewModal).modal('show');
                    },
                    hide: function() {
                        $(pdfPreviewModal).modal('hide');
                    }
                };
            }

            const imagePreviewModal = document.getElementById('imagePreviewModal');
            const imagePreviewElement = document.getElementById('imagePreviewElement');
            const imagePreviewModalLabel = document.getElementById('imagePreviewModalLabel');
            const imageDownloadLink = document.getElementById('imageDownloadLink');

            let imagePreviewModalInstance;
            try {
                // Try Bootstrap 5 modal
                imagePreviewModalInstance = new bootstrap.Modal(imagePreviewModal);
            } catch (e) {
                // Fallback to jQuery for Bootstrap 4
                imagePreviewModalInstance = {
                    show: function() {
                        $(imagePreviewModal).modal('show');
                    },
                    hide: function() {
                        $(imagePreviewModal).modal('hide');
                    }
                };
            }

            // Event listener untuk tombol preview PDF
            document.addEventListener('click', function(e) {
                if (e.target.classList.contains('pdf-preview-btn') || e.target.closest('.pdf-preview-btn')) {
                    const button = e.target.classList.contains('pdf-preview-btn') ? e.target : e.target.closest('.pdf-preview-btn');
                    const pdfUrl = button.getAttribute('data-pdf-url');
                    const pdfName = button.getAttribute('data-pdf-name');

                    if (pdfUrl && pdfPreviewFrame) {
                        pdfPreviewFrame.src = pdfUrl;
                        if (pdfFileName) {
                            pdfFileName.textContent = pdfName || 'Dokumen';
                        }
                        if (pdfDownloadLink) {
                            pdfDownloadLink.href = pdfUrl;
                            pdfDownloadLink.setAttribute('download', pdfName || 'dokumen.pdf');
                        }
                        pdfPreviewModalInstance.show();
                    }
                }
            });

            // Event listener untuk preview gambar
            document.addEventListener('click', function(e) {
                if (e.target.classList.contains('image-preview') || e.target.closest('.image-preview')) {
                    const image = e.target.classList.contains('image-preview') ? e.target : e.target.closest('.image-preview');
                    const imageUrl = image.getAttribute('src') || image.getAttribute('data-src');
                    const imageName = image.getAttribute('alt') || 'Gambar';

                    if (imageUrl && imagePreviewElement) {
                        imagePreviewElement.src = imageUrl;
                        imagePreviewModalLabel.textContent = 'Pratinjau: ' + imageName;

                        if (imageDownloadLink) {
                            imageDownloadLink.href = imageUrl;
                            imageDownloadLink.setAttribute('download', imageName);
                        }

                        imagePreviewModalInstance.show();
                    }
                }
            });

            // Clear iframe when modal is closed
            pdfPreviewModal.addEventListener('hidden.bs.modal', function() {
                if (pdfPreviewFrame) {
                    pdfPreviewFrame.src = '';
                }
            });

            // For Bootstrap 4 compatibility
            $(pdfPreviewModal).on('hidden.bs.modal', function() {
                if (pdfPreviewFrame) {
                    pdfPreviewFrame.src = '';
                }
            });
        });
    </script>
@endsection
