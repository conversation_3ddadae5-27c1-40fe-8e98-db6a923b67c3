<div class="message-item mb-4">
    <!-- Timestamp di atas nama pengirim -->
    <div class="message-time small text-muted mb-1 {{ $isSender ? 'text-end' : 'text-start' }}">
        {{ $message->created_at->format('d/m/Y - H:i') }}
    </div>

    <div class="d-flex {{ $isSender ? 'justify-content-end' : 'justify-content-start' }}">
        @if(!$isSender)
            <div class="message-avatar me-2">
                @if($avatar)
                    <img src="{{ $avatar }}" alt="Avatar" class="rounded-circle" width="40" height="40" style="object-fit: cover;">
                @else
                    <div class="rounded-circle bg-secondary d-flex align-items-center justify-content-center" style="width: 40px; height: 40px;">
                        <i class="fas fa-user text-white"></i>
                    </div>
                @endif
            </div>
        @endif

        <div class="message-content" style="max-width: 80%;">
            <div class="message-sender mb-1 {{ $isSender ? 'text-end' : 'text-start' }}">
                <div class="fw-bold">
                    {{ $senderName }}
                </div>
            </div>

            <div class="message-bubble p-3 {{ $isSender ? 'bg-primary-light text-dark' : 'bg-white text-dark' }}"
                 style="border-radius: 10px; display: inline-block; border: 1px solid #e0e0e0; box-shadow: 0 1px 2px rgba(0,0,0,0.1);">
                {!! nl2br(e($message->body)) !!}

                @if(isset($message->attachment) && !empty($message->attachment))
                    @php
                        // Handle different attachment formats
                        if (is_array($message->attachment)) {
                            if (isset($message->attachment['url'])) {
                                // Format 1: Single attachment with direct url
                                $attachmentUrl = $message->attachment['url'];
                                $attachmentName = $message->attachment['name'] ?? 'Lampiran';
                                $attachmentType = $message->attachment['type'] ?? '';
                            } elseif (isset($message->attachment[0]) && is_array($message->attachment[0])) {
                                // Format 2: Array of attachments with path
                                $attachment = $message->attachment[0];
                                $attachmentPath = isset($attachment['path']) ? 'public/storage/' . $attachment['path'] : '';
                                $attachmentUrl = asset($attachmentPath);
                                $attachmentName = $attachment['name'] ?? 'Lampiran';
                                $attachmentType = $attachment['type'] ?? '';
                            } else {
                                // Unknown format
                                $attachmentUrl = '';
                                $attachmentName = 'Lampiran';
                                $attachmentType = '';
                            }
                        } else {
                            // String format (unlikely but handle it)
                            $attachmentUrl = $message->attachment;
                            $attachmentName = 'Lampiran';
                            $attachmentType = '';
                        }

                        // Debug attachment info
                        // echo "<pre>Attachment: " . json_encode($message->attachment, JSON_PRETTY_PRINT) . "</pre>";

                        // Determine file type from extension if not provided
                        $extension = strtolower(pathinfo($attachmentUrl, PATHINFO_EXTENSION));
                        $mimeType = $attachmentType;

                        // If type is a MIME type, extract the main type
                        if (strpos($mimeType, '/') !== false) {
                            list($mainType, $subType) = explode('/', $mimeType, 2);
                            if ($mainType === 'image') {
                                $attachmentType = 'image';
                            } elseif ($mainType === 'application' && ($subType === 'pdf' || strpos($subType, 'pdf') !== false)) {
                                $attachmentType = 'document';
                            }
                        }

                        $isImage = $attachmentType === 'image' || in_array($extension, ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'svg', 'webp']);
                        $isPdf = $attachmentType === 'document' || in_array($extension, ['pdf']);
                    @endphp

                    @if($isImage)
                        <div class="image-attachment mt-2">
                            <a href="{{ $attachmentUrl }}" class="image-lightbox" data-caption="{{ $attachmentName }}">
                                <img src="{{ $attachmentUrl }}" alt="{{ $attachmentName }}" class="image-preview img-fluid rounded" style="max-height: 200px;">
                            </a>
                            <div class="mt-1">
                                <small class="text-muted">{{ $attachmentName }}</small>
                            </div>
                        </div>
                    @elseif($isPdf)
                        <div class="document-attachment">
                            <div class="document-icon">
                                <i class="fas fa-file-pdf"></i>
                            </div>
                            <div class="document-info">
                                <div class="text-truncate" title="{{ $attachmentName }}">{{ $attachmentName }}</div>
                                <small class="text-muted">PDF Document</small>
                            </div>
                            <div class="document-actions">
                                <button type="button" class="btn btn-sm btn-outline-primary pdf-preview-btn"
                                    data-pdf-url="{{ $attachmentUrl }}"
                                    data-pdf-name="{{ $attachmentName }}">
                                    <i class="fas fa-eye"></i>
                                </button>
                                <a href="{{ $attachmentUrl }}" class="btn btn-sm btn-outline-success" download="{{ $attachmentName }}">
                                    <i class="fas fa-download"></i>
                                </a>
                            </div>
                        </div>
                    @else
                        <div class="message-attachment mt-2 p-2 bg-light rounded border">
                            <a href="{{ $attachmentUrl }}" target="_blank" class="d-flex align-items-center">
                                <i class="fas fa-paperclip me-2"></i>
                                <span class="text-truncate" title="{{ $attachmentName }}">{{ $attachmentName }}</span>
                            </a>
                        </div>
                    @endif
                @endif
            </div>
        </div>

        @if($isSender)
            <div class="message-avatar ms-2">
                @if($avatar)
                    <img src="{{ $avatar }}" alt="Avatar" class="rounded-circle" width="40" height="40" style="object-fit: cover;">
                @else
                    <div class="rounded-circle bg-primary d-flex align-items-center justify-content-center" style="width: 40px; height: 40px;">
                        <i class="fas fa-user-shield text-white"></i>
                    </div>
                @endif
            </div>
        @endif
    </div>
</div>
