@extends('backend.layouts.app')
@section('title')
    {{ __('Saran dan Ma<PERSON>') }}
@endsection

@section('content')
    <div class="container-fluid">
        <div class="row">
            <div class="col-md-12">
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h3 class="card-title line-height-36">{{ __('Saran dan Masukan') }}</h3>
                        <div id="filter-info" class="d-none">
                            <div class="d-flex align-items-center">
                                <span class="mr-2">Filter aktif: Menampilkan saran tertentu</span>
                                <a href="{{ route('admin.feedback.index') }}" class="btn btn-sm btn-outline-danger">
                                    <i class="fas fa-times"></i> Reset Filter
                                </a>
                            </div>
                        </div>
                    </div>
                    <div class="card-body table-responsive p-0">
                        <div class="row p-3">
                            <div class="col-sm-12 col-md-4 col-lg-2 mb-3">
                                <a href="javascript:void(0)" class="card-link" data-filter="all">
                                    <div class="card bg-primary text-white">
                                        <div class="card-body">
                                            <h5 class="card-title">Total Saran</h5>
                                            <h2 class="card-text" id="total-feedback">0</h2>
                                        </div>
                                    </div>
                                </a>
                            </div>
                            <div class="col-sm-12 col-md-4 col-lg-2 mb-3">
                                <a href="javascript:void(0)" class="card-link" data-filter="candidate">
                                    <div class="card bg-info text-white">
                                        <div class="card-body">
                                            <h5 class="card-title">Pencaker</h5>
                                            <h2 class="card-text" id="candidate-feedback">0</h2>
                                        </div>
                                    </div>
                                </a>
                            </div>
                            <div class="col-sm-12 col-md-4 col-lg-2 mb-3">
                                <a href="javascript:void(0)" class="card-link" data-filter="company">
                                    <div class="card bg-success text-white">
                                        <div class="card-body">
                                            <h5 class="card-title">Perusahaan</h5>
                                            <h2 class="card-text" id="company-feedback">0</h2>
                                        </div>
                                    </div>
                                </a>
                            </div>
                            <div class="col-sm-12 col-md-4 col-lg-2 mb-3">
                                <a href="javascript:void(0)" class="card-link" data-filter="guest">
                                    <div class="card bg-secondary text-white">
                                        <div class="card-body">
                                            <h5 class="card-title">Tamu</h5>
                                            <h2 class="card-text" id="guest-feedback">0</h2>
                                        </div>
                                    </div>
                                </a>
                            </div>
                            <div class="col-sm-12 col-md-4 col-lg-2 mb-3">
                                <div class="card bg-warning text-white">
                                    <div class="card-body">
                                        <h5 class="card-title">Rata-rata</h5>
                                        <h2 class="card-text" id="average-rating">0/5</h2>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <div class="d-flex">
                                    <div class="mr-2">
                                        <select id="user-type-filter" class="form-control">
                                            <option value="">Semua Tipe Pengguna</option>
                                            <option value="candidate">Pencaker</option>
                                            <option value="company">Perusahaan</option>
                                            <option value="guest">Tamu</option>
                                        </select>
                                    </div>
                                    <div>
                                        <select id="status-filter" class="form-control">
                                            <option value="">Semua Status</option>
                                            <option value="read">Dibaca</option>
                                            <option value="unread">Belum Dibaca</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6 text-right">
                                <button id="apply-filters" class="btn btn-primary">Terapkan Filter</button>
                                <button id="reset-filters" class="btn btn-outline-secondary">Reset Filter</button>
                            </div>
                        </div>
                        <table class="table table-hover text-nowrap table-bordered" id="feedbackTable">
                            <thead>
                                <tr>
                                    <th width="5%">{{ __('#') }}</th>
                                    <th>{{ __('Nama') }}</th>
                                    <th>{{ __('Email') }}</th>
                                    <th>{{ __('Tipe Pengguna') }}</th>
                                    <th>{{ __('Penilaian') }}</th>
                                    <th>{{ __('Tanggal') }}</th>
                                    <th>{{ __('Status') }}</th>
                                    <th width="10%">{{ __('Aksi') }}</th>
                                </tr>
                            </thead>
                            <tbody>
                                {{-- Data will be loaded via AJAX --}}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    {{-- Feedback Detail Modal --}}
    <div class="modal fade" id="feedbackDetailModal" tabindex="-1" role="dialog" aria-labelledby="feedbackDetailModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="feedbackDetailModalLabel">Detail Saran dan Masukan</h5>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label class="font-weight-bold">Nama:</label>
                                <p id="detail-name"></p>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label class="font-weight-bold">Email:</label>
                                <p id="detail-email"></p>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label class="font-weight-bold">Telepon:</label>
                                <p id="detail-phone"></p>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label class="font-weight-bold">Tipe Pengguna:</label>
                                <p id="detail-user-type"></p>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label class="font-weight-bold">Penilaian:</label>
                                <div id="detail-rating"></div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label class="font-weight-bold">Tanggal:</label>
                                <p id="detail-date"></p>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-12">
                            <div class="form-group">
                                <label class="font-weight-bold">Pesan:</label>
                                <div id="detail-message" class="p-3 bg-light rounded" style="white-space: pre-wrap;"></div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">Tutup</button>
                    <button type="button" class="btn btn-success" id="mark-as-read-btn">Tandai Dibaca</button>
                </div>
            </div>
        </div>
    </div>

    {{-- Delete Confirmation Modal --}}
    <div class="modal fade" id="deleteModal" tabindex="-1" role="dialog" aria-labelledby="deleteModalLabel" aria-hidden="true">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="deleteModalLabel">Konfirmasi Hapus</h5>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    Apakah Anda yakin ingin menghapus saran ini?
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">Batal</button>
                    <button type="button" class="btn btn-danger" id="confirm-delete-btn">Hapus</button>
                </div>
            </div>
        </div>
    </div>

    {{-- User Profile Modal --}}
    <div class="modal fade" id="userProfileModal" tabindex="-1" role="dialog" aria-labelledby="userProfileModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="userProfileModalLabel">Profil Pengguna</h5>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    <div class="text-center mb-4">
                        <img id="profile-image" src="" alt="Profil Pengguna" class="img-fluid rounded-circle mb-2" style="max-width: 100px;">
                        <h4 id="profile-name" class="mb-0"></h4>
                        <p id="profile-email" class="text-muted"></p>
                    </div>

                    <div id="company-profile-section" class="d-none">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="font-weight-bold">Industri:</label>
                                    <p id="profile-industry"></p>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="font-weight-bold">Tipe Organisasi:</label>
                                    <p id="profile-organization-type"></p>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="font-weight-bold">Tanggal Pendirian:</label>
                                    <p id="profile-establishment-date"></p>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="font-weight-bold">Ukuran Tim:</label>
                                    <p id="profile-team-size"></p>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="font-weight-bold">Website:</label>
                                    <p id="profile-website"></p>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="font-weight-bold">Alamat:</label>
                                    <p id="profile-address"></p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div id="candidate-profile-section" class="d-none">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="font-weight-bold">Profesi:</label>
                                    <p id="profile-profession"></p>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="font-weight-bold">Tanggal Lahir:</label>
                                    <p id="profile-birth-date"></p>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="font-weight-bold">Jenis Kelamin:</label>
                                    <p id="profile-gender"></p>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="font-weight-bold">Pengalaman:</label>
                                    <p id="profile-experience"></p>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="font-weight-bold">Pendidikan:</label>
                                    <p id="profile-education"></p>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="font-weight-bold">Alamat:</label>
                                    <p id="profile-address-candidate"></p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="form-group">
                        <label class="font-weight-bold">Bio:</label>
                        <div id="profile-bio" class="p-3 bg-light rounded" style="white-space: pre-wrap;"></div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">Tutup</button>
                    <a id="view-full-profile" href="#" target="_blank" class="btn btn-primary">Lihat Profil Lengkap</a>
                </div>
            </div>
        </div>
    </div>
@endsection

@section('style')
    <link rel="stylesheet" href="{{ asset('backend') }}/plugins/datatables-bs4/css/dataTables.bootstrap4.min.css">
    <link rel="stylesheet" href="{{ asset('backend') }}/plugins/datatables-responsive/css/responsive.bootstrap4.min.css">
    <style>
        .rating-stars {
            color: #FFD700;
        }
        .badge-read {
            background-color: #28a745;
            color: white;
        }
        .badge-unread {
            background-color: #ffc107;
            color: black;
        }
        .card-link {
            display: block;
            text-decoration: none !important;
            transition: all 0.3s ease;
        }
        .card-link:hover {
            transform: translateY(-5px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        .card-link .card {
            transition: all 0.3s ease;
        }
        .card-link:hover .card {
            box-shadow: 0 10px 20px rgba(0,0,0,0.2);
        }
        .active-filter .card {
            border: 3px solid #fff;
            box-shadow: 0 0 15px rgba(0,0,0,0.3);
        }
    </style>
@endsection

@section('script')
    <script src="{{ asset('backend') }}/plugins/datatables/jquery.dataTables.min.js"></script>
    <script src="{{ asset('backend') }}/plugins/datatables-bs4/js/dataTables.bootstrap4.min.js"></script>
    <script src="{{ asset('backend') }}/plugins/datatables-responsive/js/dataTables.responsive.min.js"></script>
    <script src="{{ asset('backend') }}/plugins/datatables-responsive/js/responsive.bootstrap4.min.js"></script>
    <script>
        $(function() {
            let feedbackId = {{ isset($feedbackId) ? $feedbackId : 'null' }};

            // Tampilkan filter info jika ada feedback ID
            if (feedbackId) {
                $('#filter-info').removeClass('d-none');
            }
            let table = $('#feedbackTable').DataTable({
                processing: true,
                serverSide: true,
                responsive: true,
                ajax: {
                    url: "{{ route('admin.feedback.data') }}",
                    data: function(d) {
                        if (feedbackId) {
                            d.feedback_id = feedbackId;
                        }
                    }
                },
                columns: [{
                        data: 'DT_RowIndex',
                        name: 'DT_RowIndex',
                        orderable: false,
                        searchable: false
                    },
                    {
                        data: 'name',
                        name: 'name'
                    },
                    {
                        data: 'email',
                        name: 'email'
                    },
                    {
                        data: 'user_info',
                        name: 'user_info',
                        orderable: false,
                        searchable: false
                    },
                    {
                        data: 'rating_stars',
                        name: 'rating',
                        orderable: true,
                        searchable: false
                    },
                    {
                        data: 'created_date',
                        name: 'created_at'
                    },
                    {
                        data: function(row) {
                            return row.is_read ?
                                '<span class="badge badge-read">Dibaca</span>' :
                                '<span class="badge badge-unread">Belum Dibaca</span>';
                        },
                        name: 'is_read',
                        orderable: true,
                        searchable: false
                    },
                    {
                        data: 'action',
                        name: 'action',
                        orderable: false,
                        searchable: false
                    }
                ],
                drawCallback: function(settings) {
                    // Update summary counts
                    updateSummary();
                }
            });

            // View feedback details
            $(document).on('click', '.view-feedback-btn', function() {
                const id = $(this).data('id');
                feedbackId = id;

                // Show loading state
                $('#detail-name, #detail-email, #detail-phone, #detail-user-type, #detail-rating, #detail-date, #detail-message').html('<div class="spinner-border spinner-border-sm" role="status"><span class="sr-only">Loading...</span></div>');

                // Fetch feedback details
                $.ajax({
                    url: `/admin/feedback/${id}`,
                    type: 'GET',
                    dataType: 'json',
                    success: function(response) {
                        if (response.success) {
                            const feedback = response.data;

                            // Populate modal with feedback details
                            $('#detail-name').text(feedback.name);
                            $('#detail-email').text(feedback.email);
                            $('#detail-phone').text(feedback.phone || '-');
                            // Konversi tipe pengguna
                            let userType = 'Tamu';
                            if (feedback.user_type === 'candidate') {
                                userType = 'Pencaker';
                            } else if (feedback.user_type === 'company') {
                                userType = 'Perusahaan';
                            }
                            $('#detail-user-type').text(userType);

                            // Display rating as stars
                            let stars = '';
                            for (let i = 1; i <= 5; i++) {
                                if (i <= feedback.rating) {
                                    stars += '<i class="fas fa-star text-warning"></i>';
                                } else {
                                    stars += '<i class="far fa-star text-warning"></i>';
                                }
                            }
                            stars += ` <span class="ml-2">${feedback.rating}/5</span>`;
                            $('#detail-rating').html(stars);

                            // Format date
                            const date = new Date(feedback.created_at);
                            const formattedDate = date.toLocaleDateString('id-ID', {
                                day: 'numeric',
                                month: 'long',
                                year: 'numeric',
                                hour: '2-digit',
                                minute: '2-digit'
                            });
                            $('#detail-date').text(formattedDate);

                            // Display message
                            $('#detail-message').text(feedback.message);

                            // Show/hide mark as read button
                            if (feedback.is_read) {
                                $('#mark-as-read-btn').hide();
                            } else {
                                $('#mark-as-read-btn').show();
                            }

                            // Show modal
                            $('#feedbackDetailModal').modal('show');
                        } else {
                            toastr.error('Gagal memuat detail saran');
                        }
                    },
                    error: function() {
                        toastr.error('Terjadi kesalahan. Silakan coba lagi.');
                    }
                });
            });

            // Mark as read
            $('#mark-as-read-btn').on('click', function() {
                if (!feedbackId) return;

                // Show loading state
                $(this).html('<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> Memproses...');
                $(this).prop('disabled', true);

                $.ajax({
                    url: `/admin/feedback/${feedbackId}/mark-as-read`,
                    type: 'PUT',
                    dataType: 'json',
                    headers: {
                        'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                    },
                    success: function(response) {
                        if (response.success) {
                            // Hide button
                            $('#mark-as-read-btn').hide();

                            // Show success message
                            toastr.success(response.message);

                            // Reload table
                            table.ajax.reload();
                        } else {
                            toastr.error('Gagal menandai saran sebagai dibaca');
                        }
                    },
                    error: function() {
                        toastr.error('Terjadi kesalahan. Silakan coba lagi.');
                    },
                    complete: function() {
                        // Reset button
                        $('#mark-as-read-btn').html('Tandai Dibaca');
                        $('#mark-as-read-btn').prop('disabled', false);
                    }
                });
            });

            // View user profile
            $(document).on('click', '.view-profile-btn', function() {
                const userId = $(this).data('id');
                const userType = $(this).data('type');

                // Reset sections
                $('#company-profile-section, #candidate-profile-section').addClass('d-none');

                // Show loading state
                $('#profile-name, #profile-email, #profile-bio').html('<div class="spinner-border spinner-border-sm" role="status"><span class="sr-only">Loading...</span></div>');
                $('#profile-image').attr('src', '{{ asset("backend/image/default.png") }}');

                // Log data for debugging
                console.log('Viewing profile for:', { userId, userType });

                // Fetch user profile
                $.ajax({
                    url: "{{ route('admin.feedback.user-profile') }}",
                    type: 'GET',
                    data: {
                        user_id: userId,
                        user_type: userType
                    },
                    dataType: 'json',
                    success: function(response) {
                        console.log('Profile response:', response);

                        if (response.success) {
                            const data = response.data;

                            // Set common fields
                            $('#profile-name').text(data.name);
                            $('#profile-email').text(data.email);
                            $('#profile-bio').text(data.bio || 'Tidak ada bio.');

                            // Set profile link
                            $('#view-full-profile').attr('href', data.profile_link);

                            if (response.type === 'company') {
                                // Company profile
                                $('#profile-image').attr('src', data.logo);
                                $('#profile-industry').text(data.industry);
                                $('#profile-organization-type').text(data.organization_type);
                                $('#profile-establishment-date').text(data.establishment_date || '-');
                                $('#profile-team-size').text(data.team_size || '-');
                                $('#profile-website').text(data.website || '-');
                                $('#profile-address').text(data.address || '-');

                                // Show company section
                                $('#company-profile-section').removeClass('d-none');
                            } else if (response.type === 'candidate') {
                                // Candidate profile
                                $('#profile-image').attr('src', data.photo);
                                $('#profile-profession').text(data.profession);
                                $('#profile-birth-date').text(data.birth_date || '-');
                                $('#profile-gender').text(data.gender || '-');
                                $('#profile-experience').text(data.experience);
                                $('#profile-education').text(data.education);
                                $('#profile-address-candidate').text(data.address || '-');

                                // Show candidate section
                                $('#candidate-profile-section').removeClass('d-none');
                            }

                            // Show modal
                            $('#userProfileModal').modal('show');
                        } else {
                            toastr.error(response.message || 'Gagal memuat profil pengguna');
                        }
                    },
                    error: function(xhr, status, error) {
                        console.error('Error fetching profile:', { xhr, status, error });
                        toastr.error('Terjadi kesalahan. Silakan coba lagi.');
                    }
                });
            });

            // Delete feedback
            $(document).on('click', '.delete-feedback-btn', function() {
                feedbackId = $(this).data('id');
                $('#deleteModal').modal('show');
            });

            // Confirm delete
            $('#confirm-delete-btn').on('click', function() {
                if (!feedbackId) return;

                // Show loading state
                $(this).html('<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> Menghapus...');
                $(this).prop('disabled', true);

                $.ajax({
                    url: `/admin/feedback/${feedbackId}`,
                    type: 'DELETE',
                    dataType: 'json',
                    headers: {
                        'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                    },
                    success: function(response) {
                        if (response.success) {
                            // Hide modal
                            $('#deleteModal').modal('hide');

                            // Show success message
                            toastr.success(response.message);

                            // Reload table
                            table.ajax.reload();
                        } else {
                            toastr.error('Gagal menghapus saran');
                        }
                    },
                    error: function() {
                        toastr.error('Terjadi kesalahan. Silakan coba lagi.');
                    },
                    complete: function() {
                        // Reset button
                        $('#confirm-delete-btn').html('Hapus');
                        $('#confirm-delete-btn').prop('disabled', false);
                    }
                });
            });

            // Initialize filters from URL parameters
            function initializeFilters() {
                const urlParams = new URLSearchParams(window.location.search);
                const userType = urlParams.get('user_type');
                const status = urlParams.get('status');

                if (userType) {
                    $('#user-type-filter').val(userType);

                    // Highlight the corresponding card
                    $('.card-link').removeClass('active-filter');
                    $(`.card-link[data-filter="${userType}"]`).addClass('active-filter');
                }

                if (status) {
                    $('#status-filter').val(status);
                }

                // Show filter info if any filter is active
                if (userType || status) {
                    $('#filter-info').removeClass('d-none');

                    let filterText = 'Filter aktif: ';
                    if (userType) {
                        if (userType === 'candidate') {
                            filterText += 'Pencaker';
                        } else if (userType === 'company') {
                            filterText += 'Perusahaan';
                        } else if (userType === 'guest') {
                            filterText += 'Tamu';
                        }
                    }

                    if (status) {
                        if (userType) filterText += ', ';
                        if (status === 'read') {
                            filterText += 'Dibaca';
                        } else if (status === 'unread') {
                            filterText += 'Belum Dibaca';
                        }
                    }

                    $('#filter-info span').text(filterText);
                }
            }

            // Apply filters from dropdown
            $('#apply-filters').on('click', function() {
                const userType = $('#user-type-filter').val();
                const status = $('#status-filter').val();

                // Update URL with parameters
                let url = new URL(window.location.href);
                let params = new URLSearchParams(url.search);

                // Update or remove parameters
                if (userType) {
                    params.set('user_type', userType);
                } else {
                    params.delete('user_type');
                }

                if (status) {
                    params.set('status', status);
                } else {
                    params.delete('status');
                }

                // Remove feedback_id if we're filtering
                params.delete('feedback_id');
                feedbackId = null;

                // Update URL without reloading page
                url.search = params.toString();
                window.history.pushState({}, '', url);

                // Show filter info
                if (userType || status) {
                    $('#filter-info').removeClass('d-none');

                    let filterText = 'Filter aktif: ';
                    if (userType) {
                        if (userType === 'candidate') {
                            filterText += 'Pencaker';
                        } else if (userType === 'company') {
                            filterText += 'Perusahaan';
                        } else if (userType === 'guest') {
                            filterText += 'Tamu';
                        }
                    }

                    if (status) {
                        if (userType) filterText += ', ';
                        if (status === 'read') {
                            filterText += 'Dibaca';
                        } else if (status === 'unread') {
                            filterText += 'Belum Dibaca';
                        }
                    }

                    $('#filter-info span').text(filterText);
                } else {
                    $('#filter-info').addClass('d-none');
                }

                // Update DataTables AJAX parameters
                table.ajax.url("{{ route('admin.feedback.data') }}").search('').order([]).page.len(10);
                table.ajax.params(function(d) {
                    d.user_type = userType;
                    d.status = status;
                });

                // Reload table
                table.ajax.reload();
            });

            // Reset filters
            $('#reset-filters').on('click', function() {
                // Reset dropdowns
                $('#user-type-filter, #status-filter').val('');

                // Reset URL
                let url = new URL(window.location.href);
                let params = new URLSearchParams();
                url.search = params.toString();
                window.history.pushState({}, '', url);

                // Hide filter info
                $('#filter-info').addClass('d-none');

                // Remove active class from all cards
                $('.card-link').removeClass('active-filter');

                // Reset DataTables AJAX parameters
                table.ajax.url("{{ route('admin.feedback.data') }}").search('').order([]).page.len(10);
                table.ajax.params(function(d) {
                    delete d.user_type;
                    delete d.status;
                    delete d.feedback_id;
                    feedbackId = null;
                });

                // Reload table
                table.ajax.reload();
            });

            // Filter by card click
            $(document).on('click', '.card-link', function(e) {
                e.preventDefault();

                const filterType = $(this).data('filter');
                let userType = null;

                // Remove active class from all cards
                $('.card-link').removeClass('active-filter');

                // Add active class to clicked card
                $(this).addClass('active-filter');

                // Show filter info
                $('#filter-info').removeClass('d-none');

                // Set filter text based on type
                let filterText = 'Menampilkan semua saran';

                if (filterType === 'candidate') {
                    userType = 'candidate';
                    filterText = 'Filter aktif: Pencaker';
                } else if (filterType === 'company') {
                    userType = 'company';
                    filterText = 'Filter aktif: Perusahaan';
                } else if (filterType === 'guest') {
                    userType = 'guest';
                    filterText = 'Filter aktif: Tamu';
                } else {
                    filterText = 'Menampilkan semua saran';
                }

                // Update filter info text
                $('#filter-info span').text(filterText);

                // Update URL with parameters
                let url = new URL(window.location.href);
                let params = new URLSearchParams(url.search);

                // Reset status filter in dropdown
                $('#status-filter').val('');

                // Update user type filter in dropdown
                $('#user-type-filter').val(userType);

                // Update or remove parameters
                if (userType) {
                    params.set('user_type', userType);
                } else {
                    params.delete('user_type');
                }

                // Remove status and feedback_id parameters
                params.delete('status');
                params.delete('feedback_id');
                feedbackId = null;

                // Update URL without reloading page
                url.search = params.toString();
                window.history.pushState({}, '', url);

                // Update DataTables AJAX parameters
                table.ajax.url("{{ route('admin.feedback.data') }}").search('').order([]).page.len(10);
                table.ajax.params(function(d) {
                    d.user_type = userType;
                    delete d.status;
                    delete d.feedback_id;
                });

                // Reload table
                table.ajax.reload();
            });

            // Update summary counts
            function updateSummary() {
                $.ajax({
                    url: "{{ route('admin.feedback.data') }}",
                    type: 'GET',
                    dataType: 'json',
                    data: {
                        summary: true
                    },
                    success: function(response) {
                        if (response.summary) {
                            $('#total-feedback').text(response.summary.total);
                            $('#candidate-feedback').text(response.summary.candidate);
                            $('#company-feedback').text(response.summary.company);
                            $('#guest-feedback').text(response.summary.guest);
                            $('#average-rating').text(response.summary.average_rating + '/5');
                        }
                    }
                });
            }

            // Initial summary update
            updateSummary();

            // Auto-open feedback detail if ID is provided
            if (feedbackId) {
                setTimeout(function() {
                    $('.view-feedback-btn[data-id="' + feedbackId + '"]').click();
                }, 1000);
            }

            // Initialize tooltips
            $('[data-toggle="tooltip"]').tooltip();

            // Initialize filters from URL parameters
            initializeFilters();

            // Re-initialize tooltips after DataTables redraws
            table.on('draw', function() {
                $('[data-toggle="tooltip"]').tooltip();
            });
        });
    </script>
@endsection
