@extends('backend.layouts.app')
@section('title')
    {{ __('Edit Flyer Lowongan Kerja') }}
@endsection
@section('content')
    <div class="container-fluid">
        <div class="row">
            <div class="col-md-12">
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title line-height-36">{{ __('Edit Flyer Lowongan Kerja') }}</h3>
                        <a href="{{ route('admin.flyer.index') }}" class="btn bg-primary float-right d-flex align-items-center justify-content-center">
                            <i class="fas fa-arrow-left"></i>&nbsp; {{ __('Kembali') }}
                        </a>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="company_name">{{ __('<PERSON>a Peru<PERSON>haan') }}</label>
                                    <input type="text" class="form-control" id="company_name" value="{{ $flyer->company_name }}" readonly>
                                </div>
                                <div class="form-group">
                                    <label for="hrd_name">{{ __('Nama HRD') }}</label>
                                    <input type="text" class="form-control" id="hrd_name" value="{{ $flyer->hrd_name }}" readonly>
                                </div>
                                <div class="form-group">
                                    <label for="phone_number">{{ __('Nomor HP') }}</label>
                                    <input type="text" class="form-control" id="phone_number" value="{{ $flyer->phone_number }}" readonly>
                                </div>
                                <div class="form-group">
                                    <label for="created_at">{{ __('Tanggal Submit') }}</label>
                                    <input type="text" class="form-control" id="created_at" value="{{ $flyer->created_at->format('d M Y H:i:s') }}" readonly>
                                </div>
                                <div class="form-group">
                                    <label for="additional_info">{{ __('Informasi Tambahan') }}</label>
                                    <textarea class="form-control" id="additional_info" rows="5" readonly>{{ $flyer->additional_info }}</textarea>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="image">{{ __('Gambar Flyer') }}</label>
                                    <div class="text-center">
                                        <a href="{{ asset('uploads/flyers/' . $flyer->image) }}" data-fancybox="gallery">
                                            <img src="{{ asset('uploads/flyers/' . $flyer->image) }}" alt="Flyer" class="img-fluid" style="max-height: 300px;">
                                        </a>
                                    </div>
                                </div>
                                <form action="{{ route('admin.flyer.update', $flyer->id) }}" method="POST">
                                    @csrf
                                    @method('PUT')
                                    <div class="form-group">
                                        <label for="status">{{ __('Status') }} <span class="text-danger">*</span></label>
                                        <select name="status" id="status" class="form-control @error('status') is-invalid @enderror" required>
                                            <option value="active" {{ $flyer->status == 'active' ? 'selected' : '' }}>{{ __('Aktif') }}</option>
                                            <option value="rejected" {{ $flyer->status == 'rejected' ? 'selected' : '' }}>{{ __('Ditolak') }}</option>
                                            <option value="revision" {{ $flyer->status == 'revision' ? 'selected' : '' }}>{{ __('Perlu Revisi') }}</option>
                                        </select>
                                        @error('status')
                                            <span class="invalid-feedback" role="alert">
                                                <strong>{{ $message }}</strong>
                                            </span>
                                        @enderror
                                    </div>
                                    <div class="form-group" id="rejection_reason_group" style="{{ $flyer->status == 'active' ? 'display: none;' : '' }}">
                                        <label for="rejection_reason">{{ __('Alasan Penolakan/Revisi') }} <span class="text-danger">*</span></label>
                                        <textarea name="rejection_reason" id="rejection_reason" class="form-control @error('rejection_reason') is-invalid @enderror" rows="5">{{ old('rejection_reason', $flyer->rejection_reason) }}</textarea>
                                        @error('rejection_reason')
                                            <span class="invalid-feedback" role="alert">
                                                <strong>{{ $message }}</strong>
                                            </span>
                                        @enderror
                                    </div>
                                    <div class="form-group">
                                        <button type="submit" class="btn btn-primary">{{ __('Simpan Perubahan') }}</button>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection

@section('style')
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/@fancyapps/ui@5.0/dist/fancybox/fancybox.css" />
@endsection

@section('script')
    <script src="https://cdn.jsdelivr.net/npm/@fancyapps/ui@5.0/dist/fancybox/fancybox.umd.js"></script>
    <script>
        Fancybox.bind("[data-fancybox]", {
            // Your custom options
        });
        
        $(document).ready(function() {
            $('#status').on('change', function() {
                var status = $(this).val();
                if (status == 'active') {
                    $('#rejection_reason_group').hide();
                    $('#rejection_reason').removeAttr('required');
                } else {
                    $('#rejection_reason_group').show();
                    $('#rejection_reason').attr('required', 'required');
                }
            });
        });
    </script>
@endsection
