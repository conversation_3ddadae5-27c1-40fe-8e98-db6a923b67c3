@extends('backend.layouts.app')
@section('title')
    {{ __('Flyer Lowongan Kerja') }}
@endsection
@section('content')
    <div class="container-fluid">
        <div class="row">
            <div class="col-md-12">
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title line-height-36">{{ __('Flyer Lowongan Kerja') }}</h3>
                    </div>
                    <div class="card-body table-responsive p-0">
                        <div class="row p-3">
                            <div class="col-sm-12">
                                <form action="{{ route('admin.flyer.index') }}" method="GET">
                                    <div class="row">
                                        <div class="col-md-4 mb-2">
                                            <select name="status" class="form-control">
                                                <option value="all" {{ request('status') == 'all' ? 'selected' : '' }}>{{ __('Semua Status') }}</option>
                                                <option value="pending" {{ request('status') == 'pending' ? 'selected' : '' }}>{{ __('Menunggu') }}</option>
                                                <option value="active" {{ request('status') == 'active' ? 'selected' : '' }}>{{ __('Aktif') }}</option>
                                                <option value="rejected" {{ request('status') == 'rejected' ? 'selected' : '' }}>{{ __('Ditolak') }}</option>
                                                <option value="revision" {{ request('status') == 'revision' ? 'selected' : '' }}>{{ __('Perlu Revisi') }}</option>
                                            </select>
                                        </div>
                                        <div class="col-md-4 mb-2">
                                            <input type="text" name="company" class="form-control" placeholder="{{ __('Cari Perusahaan') }}" value="{{ request('company') }}">
                                        </div>
                                        <div class="col-md-4 mb-2">
                                            <button type="submit" class="btn btn-primary">{{ __('Filter') }}</button>
                                            <a href="{{ route('admin.flyer.index') }}" class="btn btn-secondary">{{ __('Reset') }}</a>
                                        </div>
                                    </div>
                                </form>
                            </div>
                        </div>
                        <table class="table table-hover text-nowrap table-bordered">
                            <thead>
                                <tr>
                                    <th>{{ __('No') }}</th>
                                    <th>{{ __('Gambar') }}</th>
                                    <th>{{ __('Perusahaan') }}</th>
                                    <th>{{ __('HRD') }}</th>
                                    <th>{{ __('Nomor HP') }}</th>
                                    <th>{{ __('Tanggal Submit') }}</th>
                                    <th>{{ __('Status') }}</th>
                                    <th>{{ __('Aksi') }}</th>
                                </tr>
                            </thead>
                            <tbody>
                                @forelse ($flyers as $flyer)
                                    <tr>
                                        <td>{{ $loop->iteration }}</td>
                                        <td>
                                            <a href="{{ asset('uploads/flyers/' . $flyer->image) }}" data-fancybox="gallery">
                                                <img src="{{ asset('uploads/flyers/' . $flyer->image) }}" alt="Flyer" class="img-thumbnail" style="width: 100px; height: 100px; object-fit: cover;">
                                            </a>
                                        </td>
                                        <td>{{ $flyer->company_name }}</td>
                                        <td>{{ $flyer->hrd_name }}</td>
                                        <td>{{ $flyer->phone_number }}</td>
                                        <td>{{ $flyer->created_at->format('d M Y') }}</td>
                                        <td>
                                            @if ($flyer->status == 'pending')
                                                <span class="badge bg-warning">{{ __('Menunggu') }}</span>
                                            @elseif ($flyer->status == 'active')
                                                <span class="badge bg-success">{{ __('Aktif') }}</span>
                                            @elseif ($flyer->status == 'rejected')
                                                <span class="badge bg-danger">{{ __('Ditolak') }}</span>
                                                @if ($flyer->rejection_reason)
                                                    <i class="fas fa-exclamation-circle text-danger ml-1" data-toggle="tooltip" title="{{ $flyer->rejection_reason }}"></i>
                                                    <button type="button" class="btn btn-sm btn-link text-danger" data-toggle="modal" data-target="#rejectionModal{{ $flyer->id }}">
                                                        <i class="fas fa-info-circle"></i>
                                                    </button>
                                                    <!-- Modal -->
                                                    <div class="modal fade" id="rejectionModal{{ $flyer->id }}" tabindex="-1" aria-labelledby="rejectionModalLabel{{ $flyer->id }}" aria-hidden="true">
                                                        <div class="modal-dialog">
                                                            <div class="modal-content">
                                                                <div class="modal-header">
                                                                    <h5 class="modal-title" id="rejectionModalLabel{{ $flyer->id }}">{{ __('Alasan Penolakan') }}</h5>
                                                                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                                                                        <span aria-hidden="true">&times;</span>
                                                                    </button>
                                                                </div>
                                                                <div class="modal-body">
                                                                    {{ $flyer->rejection_reason }}
                                                                </div>
                                                                <div class="modal-footer">
                                                                    <button type="button" class="btn btn-secondary" data-dismiss="modal">{{ __('Tutup') }}</button>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                @endif
                                            @elseif ($flyer->status == 'revision')
                                                <span class="badge bg-info">{{ __('Perlu Revisi') }}</span>
                                                @if ($flyer->rejection_reason)
                                                    <i class="fas fa-exclamation-circle text-info ml-1" data-toggle="tooltip" title="{{ $flyer->rejection_reason }}"></i>
                                                    <button type="button" class="btn btn-sm btn-link text-info" data-toggle="modal" data-target="#rejectionModal{{ $flyer->id }}">
                                                        <i class="fas fa-info-circle"></i>
                                                    </button>
                                                    <!-- Modal -->
                                                    <div class="modal fade" id="rejectionModal{{ $flyer->id }}" tabindex="-1" aria-labelledby="rejectionModalLabel{{ $flyer->id }}" aria-hidden="true">
                                                        <div class="modal-dialog">
                                                            <div class="modal-content">
                                                                <div class="modal-header">
                                                                    <h5 class="modal-title" id="rejectionModalLabel{{ $flyer->id }}">{{ __('Alasan Revisi') }}</h5>
                                                                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                                                                        <span aria-hidden="true">&times;</span>
                                                                    </button>
                                                                </div>
                                                                <div class="modal-body">
                                                                    {{ $flyer->rejection_reason }}
                                                                </div>
                                                                <div class="modal-footer">
                                                                    <button type="button" class="btn btn-secondary" data-dismiss="modal">{{ __('Tutup') }}</button>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                @endif
                                            @elseif ($flyer->status == 'processing')
                                                <span class="badge bg-primary">{{ __('Diproses') }}</span>
                                                <i class="fas fa-cog fa-spin text-primary ml-1" data-toggle="tooltip" title="{{ __('Loker sedang diproses oleh admin') }}"></i>
                                            @endif
                                        </td>
                                        <td>
                                            <div class="d-flex gap-1">
                                                <button type="button" class="btn btn-sm btn-info" data-toggle="modal" data-target="#viewModal{{ $flyer->id }}">
                                                    <i class="fas fa-eye"></i>
                                                </button>
                                                <a href="{{ route('admin.flyer.edit', $flyer->id) }}" class="btn btn-sm btn-primary">
                                                    <i class="fas fa-edit"></i>
                                                </a>
                                                <a href="{{ route('admin.flyer.process', $flyer->id) }}" class="btn btn-sm btn-warning" title="Proses Lowongan">
                                                    <i class="fas fa-cogs"></i>
                                                </a>
                                                <form action="{{ route('admin.flyer.destroy', $flyer->id) }}" method="POST" class="d-inline">
                                                    @csrf
                                                    @method('DELETE')
                                                    <button type="submit" class="btn btn-sm btn-danger" onclick="return confirm('{{ __('Apakah Anda yakin ingin menghapus flyer ini?') }}')">
                                                        <i class="fas fa-trash"></i>
                                                    </button>
                                                </form>
                                                @if ($flyer->job_id)
                                                    <a href="{{ route('website.job.details', $flyer->job->slug) }}" target="_blank" class="btn btn-sm btn-success" title="Lihat Lowongan">
                                                        <i class="fas fa-external-link-alt"></i>
                                                    </a>
                                                @endif
                                            </div>

                                            <!-- View Modal -->
                                            <div class="modal fade" id="viewModal{{ $flyer->id }}" tabindex="-1" role="dialog" aria-labelledby="viewModalLabel{{ $flyer->id }}" aria-hidden="true">
                                                <div class="modal-dialog modal-lg" role="document">
                                                    <div class="modal-content">
                                                        <div class="modal-header">
                                                            <h5 class="modal-title" id="viewModalLabel{{ $flyer->id }}">{{ __('Detail Flyer') }}</h5>
                                                            <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                                                                <span aria-hidden="true">&times;</span>
                                                            </button>
                                                        </div>
                                                        <div class="modal-body">
                                                            <div class="row">
                                                                <div class="col-md-6">
                                                                    <div class="form-group">
                                                                        <label>{{ __('Nama Perusahaan') }}:</label>
                                                                        <p>{{ $flyer->company_name }}</p>
                                                                    </div>
                                                                    <div class="form-group">
                                                                        <label>{{ __('Nama HRD') }}:</label>
                                                                        <p>{{ $flyer->hrd_name }}</p>
                                                                    </div>
                                                                    <div class="form-group">
                                                                        <label>{{ __('Nomor HP') }}:</label>
                                                                        <p>{{ $flyer->phone_number }}</p>
                                                                    </div>
                                                                    <div class="form-group">
                                                                        <label>{{ __('Tanggal Submit') }}:</label>
                                                                        <p>{{ $flyer->created_at->format('d M Y H:i:s') }}</p>
                                                                    </div>
                                                                    <div class="form-group">
                                                                        <label>{{ __('Status') }}:</label>
                                                                        <p>
                                                                            @if ($flyer->status == 'pending')
                                                                                <span class="badge bg-warning">{{ __('Menunggu') }}</span>
                                                                            @elseif ($flyer->status == 'active')
                                                                                <span class="badge bg-success">{{ __('Aktif') }}</span>
                                                                            @elseif ($flyer->status == 'rejected')
                                                                                <span class="badge bg-danger">{{ __('Ditolak') }}</span>
                                                                                @if ($flyer->rejection_reason)
                                                                                    <i class="fas fa-exclamation-circle text-danger" data-toggle="tooltip" title="{{ $flyer->rejection_reason }}"></i>
                                                                                @endif
                                                                            @elseif ($flyer->status == 'revision')
                                                                                <span class="badge bg-info">{{ __('Perlu Revisi') }}</span>
                                                                                @if ($flyer->rejection_reason)
                                                                                    <i class="fas fa-exclamation-circle text-info" data-toggle="tooltip" title="{{ $flyer->rejection_reason }}"></i>
                                                                                @endif
                                                                            @endif
                                                                        </p>
                                                                    </div>
                                                                    <div class="form-group">
                                                                        <label>{{ __('Informasi Tambahan') }}:</label>
                                                                        <p>{{ $flyer->additional_info ?: '-' }}</p>
                                                                    </div>
                                                                    @if ($flyer->job_id)
                                                                        <div class="form-group">
                                                                            <label>{{ __('Lowongan Terkait') }}:</label>
                                                                            <p><a href="{{ route('website.job.details', $flyer->job->slug) }}" target="_blank">{{ $flyer->job->title }}</a></p>
                                                                        </div>
                                                                    @endif
                                                                </div>
                                                                <div class="col-md-6">
                                                                    <div class="form-group">
                                                                        <label>{{ __('Gambar Flyer') }}:</label>
                                                                        <div class="text-center">
                                                                            <a href="{{ asset('uploads/flyers/' . $flyer->image) }}" data-fancybox="gallery">
                                                                                <img src="{{ asset('uploads/flyers/' . $flyer->image) }}" alt="Flyer" class="img-fluid" style="max-height: 300px;">
                                                                            </a>
                                                                        </div>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </div>
                                                        <div class="modal-footer">
                                                            <a href="{{ route('admin.flyer.edit', $flyer->id) }}" class="btn btn-primary">
                                                                <i class="fas fa-edit"></i> {{ __('Edit') }}
                                                            </a>
                                                            <a href="{{ route('admin.flyer.process', $flyer->id) }}" class="btn btn-warning">
                                                                <i class="fas fa-cogs"></i> {{ __('Proses Lowongan') }}
                                                            </a>
                                                            <button type="button" class="btn btn-success" data-toggle="modal" data-target="#activateModal{{ $flyer->id }}" data-dismiss="modal">
                                                                <i class="fas fa-check"></i> {{ __('Aktifkan') }}
                                                            </button>
                                                            <button type="button" class="btn btn-danger" onclick="rejectFlyer({{ $flyer->id }}, 'rejected')">
                                                                <i class="fas fa-times"></i> {{ __('Tolak') }}
                                                            </button>
                                                            <button type="button" class="btn btn-info" onclick="rejectFlyer({{ $flyer->id }}, 'revision')">
                                                                <i class="fas fa-redo"></i> {{ __('Perlu Revisi') }}
                                                            </button>
                                                            <form id="delete-form-{{ $flyer->id }}" action="{{ route('admin.flyer.destroy', $flyer->id) }}" method="POST" class="d-inline">
                                                                @csrf
                                                                @method('DELETE')
                                                                <button type="button" class="btn btn-danger" onclick="if(confirm('{{ __('Apakah Anda yakin ingin menghapus flyer ini?') }}')) document.getElementById('delete-form-{{ $flyer->id }}').submit();">
                                                                    <i class="fas fa-trash"></i> {{ __('Hapus') }}
                                                                </button>
                                                            </form>
                                                            <button type="button" class="btn btn-secondary" data-dismiss="modal">
                                                                <i class="fas fa-times"></i> {{ __('Tutup') }}
                                                            </button>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>

                                            <!-- Activate Modal -->
                                            <div class="modal fade" id="activateModal{{ $flyer->id }}" tabindex="-1" role="dialog" aria-labelledby="activateModalLabel{{ $flyer->id }}" aria-hidden="true">
                                                <div class="modal-dialog" role="document">
                                                    <div class="modal-content">
                                                        <div class="modal-header">
                                                            <h5 class="modal-title" id="activateModalLabel{{ $flyer->id }}">{{ __('Aktifkan Flyer') }}</h5>
                                                            <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                                                                <span aria-hidden="true">&times;</span>
                                                            </button>
                                                        </div>
                                                        <form action="{{ route('admin.flyer.update', $flyer->id) }}" method="POST">
                                                            @csrf
                                                            @method('PUT')
                                                            <div class="modal-body">
                                                                <input type="hidden" name="status" value="active">
                                                                <div class="form-group">
                                                                    <label for="job_id{{ $flyer->id }}">{{ __('Pilih Lowongan Kerja') }} <span class="text-danger">*</span></label>
                                                                    <select name="job_id" id="job_id{{ $flyer->id }}" class="form-control select2" required>
                                                                        <option value="">{{ __('-- Pilih Lowongan Kerja --') }}</option>
                                                                        @foreach(\App\Models\Job::where('company_id', $flyer->company_id)->latest()->get() as $job)
                                                                            <option value="{{ $job->id }}" {{ $flyer->job_id == $job->id ? 'selected' : '' }}>{{ $job->title }}</option>
                                                                        @endforeach
                                                                    </select>
                                                                </div>
                                                            </div>
                                                            <div class="modal-footer">
                                                                <button type="button" class="btn btn-secondary" data-dismiss="modal">{{ __('Batal') }}</button>
                                                                <button type="submit" class="btn btn-success">{{ __('Aktifkan') }}</button>
                                                            </div>
                                                        </form>
                                                    </div>
                                                </div>
                                            </div>
                                        </td>
                                    </tr>
                                @empty
                                    <tr>
                                        <td colspan="8" class="text-center">{{ __('Tidak ada data') }}</td>
                                    </tr>
                                @endforelse
                            </tbody>
                        </table>
                    </div>
                    <div class="card-footer">
                        <div class="d-flex justify-content-center">
                            {{ $flyers->links() }}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection

@section('style')
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/@fancyapps/ui@5.0/dist/fancybox/fancybox.css" />
@endsection

@section('script')
    <script src="https://cdn.jsdelivr.net/npm/@fancyapps/ui@5.0/dist/fancybox/fancybox.umd.js"></script>
    <script>
        Fancybox.bind("[data-fancybox]", {
            // Your custom options
        });

        $(document).ready(function() {
            // Initialize Select2
            $('.select2').select2({
                width: '100%',
                dropdownParent: $('.modal')
            });

            // Initialize tooltips
            $('[data-toggle="tooltip"]').tooltip();
        });

        // Function to handle reject and revision
        function rejectFlyer(id, status) {
            var reason = prompt("Masukkan alasan " + (status === 'rejected' ? 'penolakan' : 'revisi') + ":");
            if (reason !== null) {
                // Create a form and submit it
                var form = document.createElement('form');
                form.method = 'POST';
                form.action = '{{ route("admin.flyer.update", ":id") }}'.replace(':id', id);
                form.style.display = 'none';

                var csrfToken = document.createElement('input');
                csrfToken.type = 'hidden';
                csrfToken.name = '_token';
                csrfToken.value = '{{ csrf_token() }}';
                form.appendChild(csrfToken);

                var methodField = document.createElement('input');
                methodField.type = 'hidden';
                methodField.name = '_method';
                methodField.value = 'PUT';
                form.appendChild(methodField);

                var statusField = document.createElement('input');
                statusField.type = 'hidden';
                statusField.name = 'status';
                statusField.value = status;
                form.appendChild(statusField);

                var reasonField = document.createElement('input');
                reasonField.type = 'hidden';
                reasonField.name = 'rejection_reason';
                reasonField.value = reason;
                form.appendChild(reasonField);

                document.body.appendChild(form);
                form.submit();
            }
        }
    </script>
@endsection
