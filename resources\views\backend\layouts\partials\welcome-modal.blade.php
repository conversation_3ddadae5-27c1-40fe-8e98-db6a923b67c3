<!-- Welcome Modal -->
<div class="modal fade" id="welcomeModal" tabindex="-1" role="dialog" aria-labelledby="welcomeModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="welcomeModalLabel">{{ __('Selamat Datang Kembali') }}</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <div class="text-center mb-4">
                    <img src="{{ auth('admin')->user()->image_url }}" alt="Admin" class="img-circle" width="100" height="100">
                    <h4 class="mt-2">{{ auth('admin')->user()->name }}</h4>
                </div>

                <div class="row">
                    <div class="col-md-12">
                        <div class="info-box bg-light">
                            <div class="info-box-content">
                                <span class="info-box-text text-muted">{{ __('Login Terakhir') }}</span>
                                <span class="info-box-number text-muted mb-0">
                                    @if(session('last_login'))
                                        {{ session('last_login')->login_at ? session('last_login')->login_at->format('d M Y H:i:s') : 'N/A' }}
                                    @else
                                        {{ __('Ini adalah login pertama Anda') }}
                                    @endif
                                </span>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="info-box bg-light">
                            <div class="info-box-content">
                                <span class="info-box-text text-muted">{{ __('IP Address') }}</span>
                                <span class="info-box-number text-muted mb-0">
                                    @if(session('last_login'))
                                        {{ session('last_login')->ip_address ?? 'Unknown' }}
                                    @else
                                        {{ __('N/A') }}
                                    @endif
                                </span>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="info-box bg-light">
                            <div class="info-box-content">
                                <span class="info-box-text text-muted">{{ __('Lokasi') }}</span>
                                <span class="info-box-number text-muted mb-0">
                                    @if(session('last_login'))
                                        {{ session('last_login')->location ?? 'Unknown' }}
                                    @else
                                        {{ __('N/A') }}
                                    @endif
                                </span>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="info-box bg-light">
                            <div class="info-box-content">
                                <span class="info-box-text text-muted">{{ __('Perangkat') }}</span>
                                <span class="info-box-number text-muted mb-0">
                                    @if(session('last_login'))
                                        {{ session('last_login')->device ?? 'Unknown' }} / {{ session('last_login')->browser ?? 'Unknown' }}
                                    @else
                                        {{ __('N/A') }}
                                    @endif
                                </span>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="alert alert-warning mt-3">
                    <i class="fas fa-shield-alt mr-2"></i> {{ __('Jika ini bukan Anda, segera hubungi administrator.') }}
                </div>
            </div>
            <div class="modal-footer">
                <div class="d-flex justify-content-between w-100">
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="dontShowAgain">
                        <label class="form-check-label" for="dontShowAgain">
                            {{ __('Jangan tampilkan lagi') }}
                        </label>
                    </div>
                    <button type="button" class="btn btn-secondary" id="closeWelcomeModal" data-dismiss="modal">{{ __('Tutup') }}</button>
                </div>
            </div>
        </div>
    </div>
</div>
