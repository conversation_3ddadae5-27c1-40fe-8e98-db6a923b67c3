@extends('backend.layouts.app')

@section('title')
    {{ __('Activity Logs') }}
@endsection

@section('content')
    <div class="container-fluid">
        <div class="row">
            <div class="col-md-12">
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title line-height-36">{{ __('Activity Logs') }}</h3>
                    </div>
                    <div class="card-body table-responsive p-0">
                        <table class="table table-hover text-nowrap table-bordered">
                            <thead>
                                <tr>
                                    <th width="5%">#</th>
                                    <th>{{ __('Admin') }}</th>
                                    <th>{{ __('Action') }}</th>
                                    <th>{{ __('Module') }}</th>
                                    <th>{{ __('Description') }}</th>
                                    <th>{{ __('IP Address') }}</th>
                                    <th>{{ __('Device') }}</th>
                                    <th>{{ __('Time') }}</th>
                                </tr>
                            </thead>
                            <tbody>
                                @forelse ($logs as $log)
                                    <tr>
                                        <td>{{ $loop->iteration }}</td>
                                        <td>{{ $log->admin->name ?? 'N/A' }}</td>
                                        <td>{{ $log->action }}</td>
                                        <td>{{ $log->module }}</td>
                                        <td>{{ $log->description }}</td>
                                        <td>
                                            {{ $log->ip_address }}
                                            @if($log->location)
                                                <br><small class="text-muted">({{ $log->location }})</small>
                                            @endif
                                        </td>
                                        <td>{{ $log->device }}</td>
                                        <td>{{ $log->created_at->format('d M Y H:i:s') }}</td>
                                    </tr>
                                @empty
                                    <tr>
                                        <td colspan="8" class="text-center">{{ __('No logs found!') }}</td>
                                    </tr>
                                @endforelse
                            </tbody>
                        </table>
                    </div>
                    <div class="card-footer">
                        <div class="d-flex justify-content-center">
                            {{ $logs->links() }}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection
