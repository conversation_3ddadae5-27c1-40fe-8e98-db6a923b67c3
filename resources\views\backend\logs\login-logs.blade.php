@extends('backend.layouts.app')

@section('title')
    {{ __('Login Logs') }}
@endsection

@section('content')
    <div class="container-fluid">
        <div class="row">
            <div class="col-md-12">
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title line-height-36">{{ __('Login Logs') }}</h3>
                    </div>
                    <div class="card-body table-responsive p-0">
                        <table class="table table-hover text-nowrap table-bordered">
                            <thead>
                                <tr>
                                    <th width="5%">#</th>
                                    <th>{{ __('Admin') }}</th>
                                    <th>{{ __('IP Address') }}</th>
                                    <th>{{ __('Device') }}</th>
                                    <th>{{ __('Browser') }}</th>
                                    <th>{{ __('Location') }}</th>
                                    <th>{{ __('Login Time') }}</th>
                                    <th>{{ __('Logout Time') }}</th>
                                    <th>{{ __('Status') }}</th>
                                </tr>
                            </thead>
                            <tbody>
                                @forelse ($logs as $log)
                                    <tr>
                                        <td>{{ $loop->iteration }}</td>
                                        <td>{{ $log->user->name ?? 'N/A' }}</td>
                                        <td>{{ $log->ip_address }}</td>
                                        <td>{{ $log->device }}</td>
                                        <td>{{ $log->browser }}</td>
                                        <td>{{ $log->location }}</td>
                                        <td>{{ $log->login_at ? $log->login_at->format('d M Y H:i:s') : 'N/A' }}</td>
                                        <td>{{ $log->logout_at ? $log->logout_at->format('d M Y H:i:s') : 'N/A' }}</td>
                                        <td>
                                            @if ($log->is_active)
                                                <span class="badge bg-success">{{ __('Active') }}</span>
                                            @else
                                                <span class="badge bg-secondary">{{ __('Inactive') }}</span>
                                            @endif
                                        </td>
                                    </tr>
                                @empty
                                    <tr>
                                        <td colspan="9" class="text-center">{{ __('No logs found!') }}</td>
                                    </tr>
                                @endforelse
                            </tbody>
                        </table>
                    </div>
                    <div class="card-footer">
                        <div class="d-flex justify-content-center">
                            {{ $logs->links() }}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection
