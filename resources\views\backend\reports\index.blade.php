@extends('backend.layouts.app')

@section('title')
    {{ __('Laporan') }}
@endsection

@section('content')
    <div class="container-fluid">
        <div class="row">
            <div class="col-md-12">
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title line-height-36">{{ __('Query Reporting - Laporan Statistik Data') }}</h3>
                        <div class="d-flex flex-column flex-md-row align-items-md-center mt-2 mt-md-0">
                            <div class="form-group mr-md-2 mb-2 mb-md-0 w-100 w-md-auto">
                                <select id="year-filter" class="form-control">
                                    <option value="">Pilih <PERSON></option>
                                    @foreach($years as $year)
                                        <option value="{{ $year }}" {{ $year == $currentYear ? 'selected' : '' }}>
                                            {{ $year }}
                                        </option>
                                    @endforeach
                                </select>
                            </div>
                            <div class="form-group mr-md-2 mb-2 mb-md-0 w-100 w-md-auto">
                                <select id="location-filter" class="form-control">
                                    <option value="all">Semua Lokasi</option>
                                    <option value="tangsel">Tangerang Selatan</option>
                                    <option value="non-tangsel">Non-Tangerang Selatan</option>
                                </select>
                            </div>
                            <button class="btn btn-success w-100 w-md-auto mr-2" id="select-reports-btn">
                                <i class="fas fa-check-square"></i> {{ __('Pilih Laporan') }}
                            </button>
                            <button class="btn btn-primary w-100 w-md-auto" id="export-pdf-btn">
                                <i class="fas fa-file-export"></i> {{ __('Ekspor PDF') }}
                            </button>
                        </div>
                    </div>
                    <div class="card-body">
                        <!-- Pesan Instruksi -->
                        <div id="instruction-message" class="alert alert-info text-center">
                            <i class="fas fa-info-circle"></i>
                            {{ __('Klik tombol "Pilih Laporan" untuk memilih laporan yang ingin ditampilkan.') }}
                        </div>

                        <div class="row" id="reports-container">
                            <!-- Perbandingan Jumlah Pencaker dan Perusahaan -->
                            <div class="col-12 col-md-6 mb-4 report-card" data-report="user_comparison" style="display: none;">
                                <div class="card h-100">
                                    <div class="card-header">
                                        <h5 class="card-title">{{ __('Perbandingan Jumlah Pencaker dan Perusahaan') }}</h5>
                                    </div>
                                    <div class="card-body">
                                        <div class="chart-container" style="position: relative; height:300px;">
                                            <canvas id="userComparisonChart"></canvas>
                                        </div>
                                        <div class="text-center mt-3 chart-loading">
                                            <div class="d-flex flex-column align-items-center">
                                                <div class="spinner-border text-primary mb-2" role="status">
                                                    <span class="sr-only">Loading...</span>
                                                </div>
                                                <small class="text-muted">Memuat data laporan...</small>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Data Loker per Bulan -->
                            <div class="col-12 col-md-6 mb-4 report-card" data-report="jobs_by_month" style="display: none;">
                                <div class="card h-100">
                                    <div class="card-header">
                                        <h5 class="card-title">{{ __('Data Loker per Bulan') }}</h5>
                                    </div>
                                    <div class="card-body">
                                        <div class="chart-container" style="position: relative; height:300px;">
                                            <canvas id="jobsByMonthChart"></canvas>
                                        </div>
                                        <div class="text-center mt-3 chart-loading"><div class="d-flex flex-column align-items-center"><div class="spinner-border text-primary mb-2" role="status"><span class="sr-only">Loading...</span></div><small class="text-muted">Memuat data laporan...</small></div></div>
                                    </div>
                                </div>
                            </div>

                            <!-- Status Lamaran Kerja Pencaker -->
                            <div class="col-12 col-md-6 mb-4 report-card" data-report="application_status" style="display: none;">
                                <div class="card h-100">
                                    <div class="card-header">
                                        <h5 class="card-title">{{ __('Status Lamaran Kerja Pencaker') }}</h5>
                                    </div>
                                    <div class="card-body">
                                        <div class="chart-container" style="position: relative; height:300px;">
                                            <canvas id="applicationStatusChart"></canvas>
                                        </div>
                                        <div class="text-center mt-3 chart-loading"><div class="d-flex flex-column align-items-center"><div class="spinner-border text-primary mb-2" role="status"><span class="sr-only">Loading...</span></div><small class="text-muted">Memuat data laporan...</small></div></div>
                                    </div>
                                </div>
                            </div>

                            <!-- Serapan Lowongan Kerja (Diterima) -->
                            <div class="col-12 col-md-6 mb-4 report-card" data-report="job_absorption" style="display: none;">
                                <div class="card h-100">
                                    <div class="card-header">
                                        <h5 class="card-title">{{ __('Serapan Lowongan Kerja (Diterima)') }}</h5>
                                    </div>
                                    <div class="card-body">
                                        <div class="chart-container" style="position: relative; height:300px;">
                                            <canvas id="jobAbsorptionChart"></canvas>
                                        </div>
                                        <div class="text-center mt-3 chart-loading"><div class="d-flex flex-column align-items-center"><div class="spinner-border text-primary mb-2" role="status"><span class="sr-only">Loading...</span></div><small class="text-muted">Memuat data laporan...</small></div></div>
                                    </div>
                                </div>
                            </div>

                            <!-- Distribusi Pendidikan Pencaker -->
                            <div class="col-12 col-md-6 mb-4 report-card" data-report="education_distribution" style="display: none;">
                                <div class="card h-100">
                                    <div class="card-header">
                                        <h5 class="card-title">{{ __('Distribusi Pendidikan Pencaker') }}</h5>
                                    </div>
                                    <div class="card-body">
                                        <div class="chart-container" style="position: relative; height:300px;">
                                            <canvas id="educationDistributionChart"></canvas>
                                        </div>
                                        <div class="text-center mt-3 chart-loading"><div class="d-flex flex-column align-items-center"><div class="spinner-border text-primary mb-2" role="status"><span class="sr-only">Loading...</span></div><small class="text-muted">Memuat data laporan...</small></div></div>
                                    </div>
                                </div>
                            </div>

                            <!-- Distribusi Umur Pencaker -->
                            <div class="col-12 col-md-6 mb-4 report-card" data-report="age_distribution" style="display: none;">
                                <div class="card h-100">
                                    <div class="card-header">
                                        <h5 class="card-title">{{ __('Distribusi Umur Pencaker') }}</h5>
                                    </div>
                                    <div class="card-body">
                                        <div class="chart-container" style="position: relative; height:300px;">
                                            <canvas id="ageDistributionChart"></canvas>
                                        </div>
                                        <div class="text-center mt-3 chart-loading"><div class="d-flex flex-column align-items-center"><div class="spinner-border text-primary mb-2" role="status"><span class="sr-only">Loading...</span></div><small class="text-muted">Memuat data laporan...</small></div></div>
                                    </div>
                                </div>
                            </div>

                            <!-- Tren Lamaran Kerja per Bulan -->
                            <div class="col-12 col-md-6 mb-4 report-card" data-report="application_trends" style="display: none;">
                                <div class="card h-100">
                                    <div class="card-header">
                                        <h5 class="card-title">{{ __('Tren Lamaran Kerja per Bulan') }}</h5>
                                    </div>
                                    <div class="card-body">
                                        <div class="chart-container" style="position: relative; height:300px;">
                                            <canvas id="applicationTrendsChart"></canvas>
                                        </div>
                                        <div class="text-center mt-3 chart-loading"><div class="d-flex flex-column align-items-center"><div class="spinner-border text-primary mb-2" role="status"><span class="sr-only">Loading...</span></div><small class="text-muted">Memuat data laporan...</small></div></div>
                                    </div>
                                </div>
                            </div>

                            <!-- Perbandingan Jenis Pekerjaan -->
                            <div class="col-12 col-md-6 mb-4 report-card" data-report="job_type_comparison" style="display: none;">
                                <div class="card h-100">
                                    <div class="card-header">
                                        <h5 class="card-title">{{ __('Perbandingan Jenis Pekerjaan') }}</h5>
                                    </div>
                                    <div class="card-body">
                                        <div class="chart-container" style="position: relative; height:300px;">
                                            <canvas id="jobTypeComparisonChart"></canvas>
                                        </div>
                                        <div class="text-center mt-3 chart-loading"><div class="d-flex flex-column align-items-center"><div class="spinner-border text-primary mb-2" role="status"><span class="sr-only">Loading...</span></div><small class="text-muted">Memuat data laporan...</small></div></div>
                                    </div>
                                </div>
                            </div>

                            <!-- Analisis Gaji Lowongan -->
                            <div class="col-12 col-md-6 mb-4 report-card" data-report="salary_analysis" style="display: none;">
                                <div class="card h-100">
                                    <div class="card-header">
                                        <h5 class="card-title">{{ __('Analisis Gaji Lowongan') }}</h5>
                                    </div>
                                    <div class="card-body">
                                        <div class="chart-container" style="position: relative; height:300px;">
                                            <canvas id="salaryAnalysisChart"></canvas>
                                        </div>
                                        <div class="text-center mt-3 chart-loading"><div class="d-flex flex-column align-items-center"><div class="spinner-border text-primary mb-2" role="status"><span class="sr-only">Loading...</span></div><small class="text-muted">Memuat data laporan...</small></div></div>
                                    </div>
                                </div>
                            </div>

                            <!-- Distribusi Gender Pencaker -->
                            <div class="col-12 col-md-6 mb-4 report-card" data-report="gender_distribution" style="display: none;">
                                <div class="card h-100">
                                    <div class="card-header">
                                        <h5 class="card-title">{{ __('Distribusi Gender Pencaker') }}</h5>
                                    </div>
                                    <div class="card-body">
                                        <div class="chart-container" style="position: relative; height:300px;">
                                            <canvas id="genderDistributionChart"></canvas>
                                        </div>
                                        <div class="text-center mt-3 chart-loading"><div class="d-flex flex-column align-items-center"><div class="spinner-border text-primary mb-2" role="status"><span class="sr-only">Loading...</span></div><small class="text-muted">Memuat data laporan...</small></div></div>
                                    </div>
                                </div>
                            </div>

                            <!-- Analisis Kategori Pekerjaan -->
                            <div class="col-12 col-md-6 mb-4 report-card" data-report="job_category_analysis" style="display: none;">
                                <div class="card h-100">
                                    <div class="card-header">
                                        <h5 class="card-title">{{ __('Analisis Kategori Pekerjaan') }}</h5>
                                    </div>
                                    <div class="card-body">
                                        <div class="chart-container" style="position: relative; height:300px;">
                                            <canvas id="jobCategoryAnalysisChart"></canvas>
                                        </div>
                                        <div class="text-center mt-3 chart-loading"><div class="d-flex flex-column align-items-center"><div class="spinner-border text-primary mb-2" role="status"><span class="sr-only">Loading...</span></div><small class="text-muted">Memuat data laporan...</small></div></div>
                                    </div>
                                </div>
                            </div>

                            <!-- Analisis Sebaran Lokasi -->
                            <div class="col-12 col-md-6 mb-4 report-card" data-report="location_analysis" style="display: none;">
                                <div class="card h-100">
                                    <div class="card-header">
                                        <h5 class="card-title">{{ __('Analisis Sebaran Lokasi') }}</h5>
                                    </div>
                                    <div class="card-body">
                                        <div class="chart-container" style="position: relative; height:300px;">
                                            <canvas id="locationAnalysisChart"></canvas>
                                        </div>
                                        <div class="text-center mt-3 chart-loading"><div class="d-flex flex-column align-items-center"><div class="spinner-border text-primary mb-2" role="status"><span class="sr-only">Loading...</span></div><small class="text-muted">Memuat data laporan...</small></div></div>
                                    </div>
                                </div>
                            </div>

                            <!-- Analisis Pekerjaan Remote -->
                            <div class="col-12 col-md-6 mb-4 report-card" data-report="remote_work_analysis" style="display: none;">
                                <div class="card h-100">
                                    <div class="card-header">
                                        <h5 class="card-title">{{ __('Analisis Pekerjaan Remote') }}</h5>
                                    </div>
                                    <div class="card-body">
                                        <div class="chart-container" style="position: relative; height:300px;">
                                            <canvas id="remoteWorkAnalysisChart"></canvas>
                                        </div>
                                        <div class="text-center mt-3 chart-loading"><div class="d-flex flex-column align-items-center"><div class="spinner-border text-primary mb-2" role="status"><span class="sr-only">Loading...</span></div><small class="text-muted">Memuat data laporan...</small></div></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal Pilih Laporan -->
    <div class="modal fade" id="selectReportsModal" tabindex="-1" role="dialog" aria-labelledby="selectReportsModalLabel" aria-hidden="true">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="selectReportsModalLabel">{{ __('Pilih Laporan yang Akan Ditampilkan') }}</h5>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    <form id="selectReportsForm">
                        <div class="form-group">
                            <label>{{ __('Pilih Laporan') }}</label>
                            <div class="custom-control custom-checkbox">
                                <input type="checkbox" class="custom-control-input" id="showUserComparison" name="showReports[]" value="user_comparison">
                                <label class="custom-control-label" for="showUserComparison">{{ __('Perbandingan Jumlah Pencaker dan Perusahaan') }}</label>
                            </div>
                            <div class="custom-control custom-checkbox">
                                <input type="checkbox" class="custom-control-input" id="showJobsByMonth" name="showReports[]" value="jobs_by_month">
                                <label class="custom-control-label" for="showJobsByMonth">{{ __('Data Loker per Bulan') }}</label>
                            </div>
                            <div class="custom-control custom-checkbox">
                                <input type="checkbox" class="custom-control-input" id="showApplicationStatus" name="showReports[]" value="application_status">
                                <label class="custom-control-label" for="showApplicationStatus">{{ __('Status Lamaran Kerja Pencaker') }}</label>
                            </div>
                            <div class="custom-control custom-checkbox">
                                <input type="checkbox" class="custom-control-input" id="showJobAbsorption" name="showReports[]" value="job_absorption">
                                <label class="custom-control-label" for="showJobAbsorption">{{ __('Serapan Lowongan Kerja (Diterima)') }}</label>
                            </div>
                            <div class="custom-control custom-checkbox">
                                <input type="checkbox" class="custom-control-input" id="showEducationDistribution" name="showReports[]" value="education_distribution">
                                <label class="custom-control-label" for="showEducationDistribution">{{ __('Distribusi Pendidikan Pencaker') }}</label>
                            </div>
                            <div class="custom-control custom-checkbox">
                                <input type="checkbox" class="custom-control-input" id="showAgeDistribution" name="showReports[]" value="age_distribution">
                                <label class="custom-control-label" for="showAgeDistribution">{{ __('Distribusi Umur Pencaker') }}</label>
                            </div>
                            <div class="custom-control custom-checkbox">
                                <input type="checkbox" class="custom-control-input" id="showApplicationTrends" name="showReports[]" value="application_trends">
                                <label class="custom-control-label" for="showApplicationTrends">{{ __('Tren Lamaran Kerja per Bulan') }}</label>
                            </div>
                            <div class="custom-control custom-checkbox">
                                <input type="checkbox" class="custom-control-input" id="showJobTypeComparison" name="showReports[]" value="job_type_comparison">
                                <label class="custom-control-label" for="showJobTypeComparison">{{ __('Perbandingan Jenis Pekerjaan') }}</label>
                            </div>
                            <div class="custom-control custom-checkbox">
                                <input type="checkbox" class="custom-control-input" id="showSalaryAnalysis" name="showReports[]" value="salary_analysis">
                                <label class="custom-control-label" for="showSalaryAnalysis">{{ __('Analisis Gaji Lowongan') }}</label>
                            </div>
                            <div class="custom-control custom-checkbox">
                                <input type="checkbox" class="custom-control-input" id="showGenderDistribution" name="showReports[]" value="gender_distribution">
                                <label class="custom-control-label" for="showGenderDistribution">{{ __('Distribusi Gender Pencaker') }}</label>
                            </div>
                            <div class="custom-control custom-checkbox">
                                <input type="checkbox" class="custom-control-input" id="showJobCategoryAnalysis" name="showReports[]" value="job_category_analysis">
                                <label class="custom-control-label" for="showJobCategoryAnalysis">{{ __('Analisis Kategori Pekerjaan') }}</label>
                            </div>
                            <div class="custom-control custom-checkbox">
                                <input type="checkbox" class="custom-control-input" id="showLocationAnalysis" name="showReports[]" value="location_analysis">
                                <label class="custom-control-label" for="showLocationAnalysis">{{ __('Analisis Sebaran Lokasi') }}</label>
                            </div>
                            <div class="custom-control custom-checkbox">
                                <input type="checkbox" class="custom-control-input" id="showRemoteWorkAnalysis" name="showReports[]" value="remote_work_analysis">
                                <label class="custom-control-label" for="showRemoteWorkAnalysis">{{ __('Analisis Pekerjaan Remote') }}</label>
                            </div>
                        </div>
                        <div class="form-group">
                            <button type="button" class="btn btn-outline-primary btn-sm" id="selectAllReports">{{ __('Pilih Semua') }}</button>
                            <button type="button" class="btn btn-outline-secondary btn-sm" id="deselectAllReports">{{ __('Hapus Semua') }}</button>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">{{ __('Batal') }}</button>
                    <button type="button" class="btn btn-success" id="applyReportSelection">{{ __('Tampilkan Laporan') }}</button>
                </div>
            </div>
        </div>
    </div>
    <!-- Modal Ekspor PDF -->
    <div class="modal fade" id="exportPdfModal" tabindex="-1" role="dialog" aria-labelledby="exportPdfModalLabel" aria-hidden="true">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="exportPdfModalLabel">{{ __('Ekspor Laporan ke PDF') }}</h5>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    <form id="exportPdfForm">
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle"></i>
                            {{ __('PDF akan berisi laporan yang sedang ditampilkan saat ini') }}
                        </div>
                        <div class="form-group">
                            <label>{{ __('Laporan yang Akan Diekspor') }}</label>
                            <div id="selectedReportsForExport">
                                <p class="text-muted">{{ __('Belum ada laporan yang dipilih') }}</p>
                            </div>
                        </div>
                        <div class="form-group">
                            <label for="exportYears">{{ __('Tahun') }}</label>
                            <select id="exportYears" name="years[]" class="form-control select2" multiple>
                                @foreach($years as $year)
                                    <option value="{{ $year }}" {{ $year == $currentYear ? 'selected' : '' }}>
                                        {{ $year }}
                                    </option>
                                @endforeach
                            </select>
                            <small class="form-text text-muted">{{ __('Pilih beberapa tahun dengan menekan tombol Ctrl/Cmd') }}</small>
                        </div>
                        <div class="form-group">
                            <label>{{ __('Rentang Bulan') }}</label>
                            <div class="row">
                                <div class="col-md-6">
                                    <select id="exportMonthStart" name="month_start" class="form-control">
                                        <option value="1">{{ __('Januari') }}</option>
                                        <option value="2">{{ __('Februari') }}</option>
                                        <option value="3">{{ __('Maret') }}</option>
                                        <option value="4">{{ __('April') }}</option>
                                        <option value="5">{{ __('Mei') }}</option>
                                        <option value="6">{{ __('Juni') }}</option>
                                        <option value="7">{{ __('Juli') }}</option>
                                        <option value="8">{{ __('Agustus') }}</option>
                                        <option value="9">{{ __('September') }}</option>
                                        <option value="10">{{ __('Oktober') }}</option>
                                        <option value="11">{{ __('November') }}</option>
                                        <option value="12">{{ __('Desember') }}</option>
                                    </select>
                                    <small class="form-text text-muted">{{ __('Dari bulan') }}</small>
                                </div>
                                <div class="col-md-6">
                                    <select id="exportMonthEnd" name="month_end" class="form-control">
                                        <option value="1">{{ __('Januari') }}</option>
                                        <option value="2">{{ __('Februari') }}</option>
                                        <option value="3">{{ __('Maret') }}</option>
                                        <option value="4">{{ __('April') }}</option>
                                        <option value="5">{{ __('Mei') }}</option>
                                        <option value="6">{{ __('Juni') }}</option>
                                        <option value="7">{{ __('Juli') }}</option>
                                        <option value="8">{{ __('Agustus') }}</option>
                                        <option value="9">{{ __('September') }}</option>
                                        <option value="10">{{ __('Oktober') }}</option>
                                        <option value="11">{{ __('November') }}</option>
                                        <option value="12" selected>{{ __('Desember') }}</option>
                                    </select>
                                    <small class="form-text text-muted">{{ __('Sampai bulan') }}</small>
                                </div>
                            </div>
                        </div>
                        <div class="form-group">
                            <label>{{ __('Lokasi') }}</label>
                            <div class="custom-control custom-checkbox">
                                <input type="checkbox" class="custom-control-input" id="exportLocationAll" name="locations[]" value="all" checked>
                                <label class="custom-control-label" for="exportLocationAll">{{ __('Semua Lokasi') }}</label>
                            </div>
                            <div class="custom-control custom-checkbox">
                                <input type="checkbox" class="custom-control-input" id="exportLocationTangsel" name="locations[]" value="tangsel">
                                <label class="custom-control-label" for="exportLocationTangsel">{{ __('Tangerang Selatan') }}</label>
                            </div>
                            <div class="custom-control custom-checkbox">
                                <input type="checkbox" class="custom-control-input" id="exportLocationNonTangsel" name="locations[]" value="non-tangsel">
                                <label class="custom-control-label" for="exportLocationNonTangsel">{{ __('Non-Tangerang Selatan') }}</label>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">{{ __('Batal') }}</button>
                    <button type="button" class="btn btn-success" id="exportExcelBtn"><i class="fas fa-file-excel"></i> {{ __('Excel') }}</button>
                    <button type="button" class="btn btn-danger" id="confirmExportBtn"><i class="fas fa-file-pdf"></i> {{ __('PDF') }}</button>
                </div>
            </div>
        </div>
    </div>
@endsection

@section('style')
    <link rel="stylesheet" href="{{ asset('backend') }}/plugins/select2/css/select2.min.css">
    <link rel="stylesheet" href="{{ asset('backend') }}/plugins/select2-bootstrap4-theme/select2-bootstrap4.min.css">
    <style>
        .chart-container {
            position: relative;
            height: 300px;
            width: 100%;
        }
        .chart-loading {
            display: none;
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            z-index: 10;
            background: rgba(255, 255, 255, 0.9);
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }

        .chart-loading .spinner-border {
            width: 2rem;
            height: 2rem;
        }

        .chart-loading small {
            font-size: 0.875rem;
            color: #6c757d;
        }
        .select2-container--default .select2-selection--multiple .select2-selection__choice {
            background-color: #007bff;
            border-color: #006fe6;
            color: #fff;
        }

        /* Responsive styles */
        @media (max-width: 767.98px) {
            .chart-container {
                height: 250px;
            }

            .card-header {
                flex-direction: column;
                align-items: flex-start;
            }

            .card-title {
                margin-bottom: 10px;
                font-size: 16px;
            }

            .comparison-table {
                overflow-x: auto;
            }

            .comparison-table table {
                font-size: 12px;
            }

            .modal-dialog {
                margin: 0.5rem;
            }
        }

        /* Tablet styles */
        @media (min-width: 768px) and (max-width: 991.98px) {
            .chart-container {
                height: 280px;
            }

            .comparison-table table {
                font-size: 13px;
            }
        }
    </style>
@endsection

@section('script')
    <script src="https://cdn.jsdelivr.net/npm/chart.js@3.7.1/dist/chart.min.js"></script>
    <script src="{{ asset('backend') }}/plugins/select2/js/select2.full.min.js"></script>
    <script>
        // Warna untuk chart
        const chartColors = [
            'rgba(54, 162, 235, 0.7)',
            'rgba(255, 99, 132, 0.7)',
            'rgba(75, 192, 192, 0.7)',
            'rgba(255, 206, 86, 0.7)',
            'rgba(153, 102, 255, 0.7)',
            'rgba(255, 159, 64, 0.7)',
            'rgba(199, 199, 199, 0.7)',
            'rgba(83, 102, 255, 0.7)',
            'rgba(40, 159, 64, 0.7)',
            'rgba(210, 199, 199, 0.7)',
        ];

        // Variabel untuk menyimpan instance chart
        let userComparisonChart = null;
        let jobsByMonthChart = null;
        let applicationStatusChart = null;
        let educationDistributionChart = null;
        let ageDistributionChart = null;
        let applicationTrendsChart = null;
        let jobAbsorptionChart = null;
        let jobTypeComparisonChart = null;

        // Fungsi untuk memuat data chart - DISABLED untuk Query Reporting
        // Sekarang menggunakan loadSelectedReports() yang hanya memuat chart yang dipilih
        /*
        function loadChartData() {
            const year = $('#year-filter').val();
            const location = $('#location-filter').val();

            console.log('Loading chart data for year:', year, 'location:', location);

            // Tampilkan loading spinner
            $('.chart-loading').show();

            // Perbandingan Jumlah Pencaker dan Perusahaan
            loadUserComparisonChart(year, location);

            // Data Loker per Bulan
            loadJobsByMonthChart(year, location);

            // Status Lamaran Kerja Pencaker
            loadApplicationStatusChart(year, location);

            // Distribusi Pendidikan Pencaker
            loadEducationDistributionChart(year, location);

            // Distribusi Umur Pencaker
            loadAgeDistributionChart(year, location);

            // Tren Lamaran Kerja per Bulan
            loadApplicationTrendsChart(year, location);

            // Serapan Lowongan Kerja (Diterima)
            console.log('Loading job absorption chart...');
            loadJobAbsorptionChart(year, location);

            // Perbandingan Jenis Pekerjaan
            console.log('Loading job type comparison chart...');
            loadJobTypeComparisonChart(year, location);

        }
        */

        // Fungsi untuk memuat chart perbandingan jumlah pencaker dan perusahaan
        function loadUserComparisonChart(year, location) {
            $.ajax({
                url: "{{ route('admin.reports.user-comparison') }}",
                type: "GET",
                data: {
                    year: year,
                    location: location
                },
                success: function(response) {
                    if (response.success) {
                        const ctx = document.getElementById('userComparisonChart').getContext('2d');

                        // Hancurkan chart sebelumnya jika ada
                        if (userComparisonChart) {
                            userComparisonChart.destroy();
                        }

                        userComparisonChart = new Chart(ctx, {
                            type: 'bar',
                            data: {
                                labels: response.months,
                                datasets: [
                                    {
                                        label: 'Pencaker',
                                        data: response.candidates,
                                        backgroundColor: chartColors[0],
                                        borderColor: chartColors[0],
                                        borderWidth: 1
                                    },
                                    {
                                        label: 'Perusahaan',
                                        data: response.companies,
                                        backgroundColor: chartColors[1],
                                        borderColor: chartColors[1],
                                        borderWidth: 1
                                    }
                                ]
                            },
                            options: {
                                responsive: true,
                                maintainAspectRatio: false,
                                scales: {
                                    x: {
                                        stacked: true
                                    },
                                    y: {
                                        stacked: true,
                                        beginAtZero: true,
                                        ticks: {
                                            precision: 0
                                        }
                                    }
                                },
                                plugins: {
                                    tooltip: {
                                        mode: 'index',
                                        intersect: false,
                                        callbacks: {
                                            afterTitle: function(tooltipItems) {
                                                // Tambahkan rasio pencaker:perusahaan
                                                const candidateValue = response.candidates[tooltipItems[0].dataIndex];
                                                const companyValue = response.companies[tooltipItems[0].dataIndex];

                                                if (companyValue > 0) {
                                                    const ratio = (candidateValue / companyValue).toFixed(1);
                                                    return `Rasio: ${ratio}:1`;
                                                }
                                                return 'Rasio: N/A';
                                            }
                                        }
                                    }
                                }
                            }
                        });

                        // Tambahkan tabel perbandingan jika semua lokasi dipilih
                        const $cardBody = $('#userComparisonChart').closest('.card-body');
                        $cardBody.find('.comparison-table').remove();

                        if (location === 'all') {
                            // Hitung total untuk pencaker dan perusahaan
                            const totalCandidates = response.candidates.reduce((a, b) => a + b, 0);
                            const totalCompanies = response.companies.reduce((a, b) => a + b, 0);
                            const ratio = totalCompanies > 0 ? (totalCandidates / totalCompanies).toFixed(1) : 'N/A';

                            // Buat tabel perbandingan
                            let tableHtml = `
                                <div class="comparison-table mt-3">
                                    <h6>Perbandingan Total</h6>
                                    <table class="table table-sm table-bordered">
                                        <thead>
                                            <tr>
                                                <th>Total Pencaker</th>
                                                <th>Total Perusahaan</th>
                                                <th>Rasio</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <tr>
                                                <td>${totalCandidates}</td>
                                                <td>${totalCompanies}</td>
                                                <td>${ratio}:1</td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>
                            `;

                            // Tambahkan tabel perbandingan Tangsel vs Non-Tangsel
                            // Ambil data untuk Tangsel
                            $.ajax({
                                url: "{{ route('admin.reports.user-comparison') }}",
                                type: "GET",
                                async: false,
                                data: {
                                    year: year,
                                    location: 'tangsel'
                                },
                                success: function(tangselResponse) {
                                    if (tangselResponse.success) {
                                        // Ambil data untuk Non-Tangsel
                                        $.ajax({
                                            url: "{{ route('admin.reports.user-comparison') }}",
                                            type: "GET",
                                            async: false,
                                            data: {
                                                year: year,
                                                location: 'non-tangsel'
                                            },
                                            success: function(nonTangselResponse) {
                                                if (nonTangselResponse.success) {
                                                    // Hitung total untuk Tangsel
                                                    const totalTangselCandidates = tangselResponse.candidates.reduce((a, b) => a + b, 0);
                                                    const totalTangselCompanies = tangselResponse.companies.reduce((a, b) => a + b, 0);
                                                    const tangselRatio = totalTangselCompanies > 0 ? (totalTangselCandidates / totalTangselCompanies).toFixed(1) : 'N/A';

                                                    // Hitung total untuk Non-Tangsel
                                                    const totalNonTangselCandidates = nonTangselResponse.candidates.reduce((a, b) => a + b, 0);
                                                    const totalNonTangselCompanies = nonTangselResponse.companies.reduce((a, b) => a + b, 0);
                                                    const nonTangselRatio = totalNonTangselCompanies > 0 ? (totalNonTangselCandidates / totalNonTangselCompanies).toFixed(1) : 'N/A';

                                                    // Tambahkan tabel perbandingan
                                                    tableHtml += `
                                                        <div class="comparison-table mt-3">
                                                            <h6>Perbandingan Lokasi</h6>
                                                            <table class="table table-sm table-bordered">
                                                                <thead>
                                                                    <tr>
                                                                        <th>Lokasi</th>
                                                                        <th>Total Pencaker</th>
                                                                        <th>Total Perusahaan</th>
                                                                        <th>Rasio</th>
                                                                    </tr>
                                                                </thead>
                                                                <tbody>
                                                                    <tr>
                                                                        <td>Tangerang Selatan</td>
                                                                        <td>${totalTangselCandidates}</td>
                                                                        <td>${totalTangselCompanies}</td>
                                                                        <td>${tangselRatio}:1</td>
                                                                    </tr>
                                                                    <tr>
                                                                        <td>Non-Tangerang Selatan</td>
                                                                        <td>${totalNonTangselCandidates}</td>
                                                                        <td>${totalNonTangselCompanies}</td>
                                                                        <td>${nonTangselRatio}:1</td>
                                                                    </tr>
                                                                    <tr class="font-weight-bold">
                                                                        <td>Semua Lokasi</td>
                                                                        <td>${totalCandidates}</td>
                                                                        <td>${totalCompanies}</td>
                                                                        <td>${ratio}:1</td>
                                                                    </tr>
                                                                </tbody>
                                                            </table>
                                                        </div>
                                                    `;
                                                }
                                            }
                                        });
                                    }
                                }
                            });

                            $cardBody.append(tableHtml);
                        }

                        // Sembunyikan loading spinner
                        $cardBody.find('.chart-loading').hide();
                    }
                },
                error: function(error) {
                    console.error("Error loading user comparison data:", error);
                    $('#userComparisonChart').closest('.card-body').find('.chart-loading').hide();
                }
            });
        }

        // Fungsi untuk memuat chart data loker per bulan
        function loadJobsByMonthChart(year, location) {
            $.ajax({
                url: "{{ route('admin.reports.jobs-by-month') }}",
                type: "GET",
                data: {
                    year: year,
                    location: location
                },
                success: function(response) {
                    if (response.success) {
                        const ctx = document.getElementById('jobsByMonthChart').getContext('2d');

                        // Hancurkan chart sebelumnya jika ada
                        if (jobsByMonthChart) {
                            jobsByMonthChart.destroy();
                        }

                        jobsByMonthChart = new Chart(ctx, {
                            type: 'bar',
                            data: {
                                labels: response.months,
                                datasets: [
                                    {
                                        label: 'Jumlah Loker',
                                        data: response.jobs,
                                        backgroundColor: chartColors[2],
                                        borderColor: chartColors[2],
                                        borderWidth: 1
                                    }
                                ]
                            },
                            options: {
                                responsive: true,
                                maintainAspectRatio: false,
                                scales: {
                                    y: {
                                        beginAtZero: true,
                                        ticks: {
                                            precision: 0
                                        }
                                    }
                                },
                                plugins: {
                                    tooltip: {
                                        mode: 'index',
                                        intersect: false,
                                        callbacks: {
                                            label: function(context) {
                                                return `Jumlah Loker: ${context.raw}`;
                                            }
                                        }
                                    }
                                }
                            }
                        });

                        // Tambahkan tabel perbandingan jika semua lokasi dipilih
                        const $cardBody = $('#jobsByMonthChart').closest('.card-body');
                        $cardBody.find('.comparison-table').remove();

                        if (location === 'all') {
                            // Hitung total loker
                            const totalJobs = response.jobs.reduce((a, b) => a + b, 0);

                            // Buat tabel perbandingan
                            let tableHtml = `
                                <div class="comparison-table mt-3">
                                    <h6>Total Loker</h6>
                                    <table class="table table-sm table-bordered">
                                        <thead>
                                            <tr>
                                                <th>Total Loker</th>
                                                <th>Rata-rata per Bulan</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <tr>
                                                <td>${totalJobs}</td>
                                                <td>${(totalJobs / response.months.length).toFixed(1)}</td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>
                            `;

                            // Tambahkan tabel perbandingan Tangsel vs Non-Tangsel
                            // Ambil data untuk Tangsel
                            $.ajax({
                                url: "{{ route('admin.reports.jobs-by-month') }}",
                                type: "GET",
                                async: false,
                                data: {
                                    year: year,
                                    location: 'tangsel'
                                },
                                success: function(tangselResponse) {
                                    if (tangselResponse.success) {
                                        // Ambil data untuk Non-Tangsel
                                        $.ajax({
                                            url: "{{ route('admin.reports.jobs-by-month') }}",
                                            type: "GET",
                                            async: false,
                                            data: {
                                                year: year,
                                                location: 'non-tangsel'
                                            },
                                            success: function(nonTangselResponse) {
                                                if (nonTangselResponse.success) {
                                                    // Hitung total untuk Tangsel
                                                    const totalTangselJobs = tangselResponse.jobs.reduce((a, b) => a + b, 0);
                                                    const avgTangselJobs = (totalTangselJobs / tangselResponse.months.length).toFixed(1);

                                                    // Hitung total untuk Non-Tangsel
                                                    const totalNonTangselJobs = nonTangselResponse.jobs.reduce((a, b) => a + b, 0);
                                                    const avgNonTangselJobs = (totalNonTangselJobs / nonTangselResponse.months.length).toFixed(1);

                                                    // Tambahkan tabel perbandingan
                                                    tableHtml += `
                                                        <div class="comparison-table mt-3">
                                                            <h6>Perbandingan Lokasi</h6>
                                                            <table class="table table-sm table-bordered">
                                                                <thead>
                                                                    <tr>
                                                                        <th>Lokasi</th>
                                                                        <th>Total Loker</th>
                                                                        <th>Rata-rata per Bulan</th>
                                                                    </tr>
                                                                </thead>
                                                                <tbody>
                                                                    <tr>
                                                                        <td>Tangerang Selatan</td>
                                                                        <td>${totalTangselJobs}</td>
                                                                        <td>${avgTangselJobs}</td>
                                                                    </tr>
                                                                    <tr>
                                                                        <td>Non-Tangerang Selatan</td>
                                                                        <td>${totalNonTangselJobs}</td>
                                                                        <td>${avgNonTangselJobs}</td>
                                                                    </tr>
                                                                    <tr class="font-weight-bold">
                                                                        <td>Semua Lokasi</td>
                                                                        <td>${totalJobs}</td>
                                                                        <td>${(totalJobs / response.months.length).toFixed(1)}</td>
                                                                    </tr>
                                                                </tbody>
                                                            </table>
                                                        </div>
                                                    `;
                                                }
                                            }
                                        });
                                    }
                                }
                            });

                            $cardBody.append(tableHtml);
                        }

                        // Sembunyikan loading spinner
                        $cardBody.find('.chart-loading').hide();
                    }
                },
                error: function(error) {
                    console.error("Error loading jobs by month data:", error);
                    $('#jobsByMonthChart').closest('.card-body').find('.chart-loading').hide();
                }
            });
        }

        // Fungsi untuk memuat chart status lamaran kerja
        function loadApplicationStatusChart(year, location) {
            $.ajax({
                url: "{{ route('admin.reports.application-status') }}",
                type: "GET",
                data: {
                    year: year,
                    location: location
                },
                success: function(response) {
                    if (response.success) {
                        const ctx = document.getElementById('applicationStatusChart').getContext('2d');

                        // Hancurkan chart sebelumnya jika ada
                        if (applicationStatusChart) {
                            applicationStatusChart.destroy();
                        }

                        const labels = response.statusData.map(item => item.name);
                        const data = response.statusData.map(item => item.count);
                        const backgroundColors = chartColors.slice(0, labels.length);

                        applicationStatusChart = new Chart(ctx, {
                            type: 'pie',
                            data: {
                                labels: labels,
                                datasets: [
                                    {
                                        data: data,
                                        backgroundColor: backgroundColors,
                                        borderColor: backgroundColors,
                                        borderWidth: 1
                                    }
                                ]
                            },
                            options: {
                                responsive: true,
                                maintainAspectRatio: false,
                                plugins: {
                                    legend: {
                                        position: 'right'
                                    },
                                    tooltip: {
                                        callbacks: {
                                            label: function(context) {
                                                const label = context.label || '';
                                                const value = context.raw || 0;
                                                const total = context.dataset.data.reduce((a, b) => a + b, 0);
                                                const percentage = total > 0 ? Math.round((value / total) * 100) : 0;
                                                return `${label}: ${value} (${percentage}%)`;
                                            }
                                        }
                                    }
                                }
                            }
                        });

                        // Tambahkan tabel perbandingan jika semua lokasi dipilih
                        const $cardBody = $('#applicationStatusChart').closest('.card-body');
                        $cardBody.find('.comparison-table').remove();

                        if (location === 'all') {
                            // Hitung total lamaran
                            const totalApplications = data.reduce((a, b) => a + b, 0);

                            // Buat tabel perbandingan
                            let tableHtml = `
                                <div class="comparison-table mt-3">
                                    <h6>Distribusi Status Lamaran</h6>
                                    <table class="table table-sm table-bordered">
                                        <thead>
                                            <tr>
                                                <th>Status</th>
                                                <th>Jumlah</th>
                                                <th>Persentase</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                            `;

                            response.statusData.forEach(item => {
                                const percentage = totalApplications > 0 ? ((item.count / totalApplications) * 100).toFixed(1) : 0;
                                tableHtml += `
                                    <tr>
                                        <td>${item.name}</td>
                                        <td>${item.count}</td>
                                        <td>${percentage}%</td>
                                    </tr>
                                `;
                            });

                            tableHtml += `
                                            <tr class="font-weight-bold">
                                                <td>Total</td>
                                                <td>${totalApplications}</td>
                                                <td>100%</td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>
                            `;

                            $cardBody.append(tableHtml);
                        }

                        // Sembunyikan loading spinner
                        $('#applicationStatusChart').closest('.card-body').find('.chart-loading').hide();
                    }
                },
                error: function(error) {
                    console.error("Error loading application status data:", error);
                    $('#applicationStatusChart').closest('.card-body').find('.chart-loading').hide();
                }
            });
        }

        // Fungsi untuk memuat chart distribusi pendidikan
        function loadEducationDistributionChart(year, location) {
            $.ajax({
                url: "{{ route('admin.reports.education-distribution') }}",
                type: "GET",
                data: {
                    year: year,
                    location: location
                },
                success: function(response) {
                    if (response.success) {
                        const ctx = document.getElementById('educationDistributionChart').getContext('2d');

                        // Hancurkan chart sebelumnya jika ada
                        if (educationDistributionChart) {
                            educationDistributionChart.destroy();
                        }

                        const labels = response.educationData.map(item => item.pendidikan_terakhir);
                        const data = response.educationData.map(item => item.count);
                        const backgroundColors = chartColors.slice(0, labels.length);

                        educationDistributionChart = new Chart(ctx, {
                            type: 'bar',
                            data: {
                                labels: labels,
                                datasets: [
                                    {
                                        label: 'Jumlah Pencaker',
                                        data: data,
                                        backgroundColor: backgroundColors,
                                        borderColor: backgroundColors,
                                        borderWidth: 1
                                    }
                                ]
                            },
                            options: {
                                responsive: true,
                                maintainAspectRatio: false,
                                indexAxis: 'y',
                                scales: {
                                    x: {
                                        beginAtZero: true,
                                        ticks: {
                                            precision: 0
                                        }
                                    }
                                },
                                plugins: {
                                    legend: {
                                        display: false
                                    },
                                    tooltip: {
                                        callbacks: {
                                            label: function(context) {
                                                const label = context.dataset.label || '';
                                                const value = context.raw || 0;
                                                const total = context.dataset.data.reduce((a, b) => a + b, 0);
                                                const percentage = total > 0 ? Math.round((value / total) * 100) : 0;
                                                return `${label}: ${value} (${percentage}%)`;
                                            }
                                        }
                                    }
                                }
                            }
                        });

                        // Tambahkan tabel perbandingan jika semua lokasi dipilih
                        const $cardBody = $('#educationDistributionChart').closest('.card-body');
                        $cardBody.find('.comparison-table').remove();

                        if (location === 'all') {
                            // Hitung total pencaker
                            const totalCandidates = data.reduce((a, b) => a + b, 0);

                            // Buat tabel perbandingan
                            let tableHtml = `
                                <div class="comparison-table mt-3">
                                    <h6>Distribusi Pendidikan</h6>
                                    <table class="table table-sm table-bordered">
                                        <thead>
                                            <tr>
                                                <th>Pendidikan</th>
                                                <th>Jumlah</th>
                                                <th>Persentase</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                            `;

                            response.educationData.forEach((item, index) => {
                                const percentage = totalCandidates > 0 ? ((item.count / totalCandidates) * 100).toFixed(1) : 0;
                                tableHtml += `
                                    <tr>
                                        <td>${item.pendidikan_terakhir}</td>
                                        <td>${item.count}</td>
                                        <td>${percentage}%</td>
                                    </tr>
                                `;
                            });

                            tableHtml += `
                                            <tr class="font-weight-bold">
                                                <td>Total</td>
                                                <td>${totalCandidates}</td>
                                                <td>100%</td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>
                            `;

                            $cardBody.append(tableHtml);
                        }

                        // Sembunyikan loading spinner
                        $('#educationDistributionChart').closest('.card-body').find('.chart-loading').hide();
                    }
                },
                error: function(error) {
                    console.error("Error loading education distribution data:", error);
                    $('#educationDistributionChart').closest('.card-body').find('.chart-loading').hide();
                }
            });
        }

        // Fungsi untuk memuat chart distribusi umur
        function loadAgeDistributionChart(year, location) {
            $.ajax({
                url: "{{ route('admin.reports.age-distribution') }}",
                type: "GET",
                data: {
                    year: year,
                    location: location
                },
                success: function(response) {
                    if (response.success) {
                        const ctx = document.getElementById('ageDistributionChart').getContext('2d');

                        // Hancurkan chart sebelumnya jika ada
                        if (ageDistributionChart) {
                            ageDistributionChart.destroy();
                        }

                        const labels = response.ageData.map(item => item.name || item.age_group);
                        const data = response.ageData.map(item => item.count);
                        const backgroundColors = chartColors.slice(0, labels.length);

                        ageDistributionChart = new Chart(ctx, {
                            type: 'bar',
                            data: {
                                labels: labels,
                                datasets: [
                                    {
                                        label: 'Jumlah Pencaker',
                                        data: data,
                                        backgroundColor: backgroundColors,
                                        borderColor: backgroundColors,
                                        borderWidth: 1
                                    }
                                ]
                            },
                            options: {
                                responsive: true,
                                maintainAspectRatio: false,
                                indexAxis: 'y',
                                scales: {
                                    x: {
                                        beginAtZero: true,
                                        ticks: {
                                            precision: 0
                                        }
                                    }
                                },
                                plugins: {
                                    legend: {
                                        display: false
                                    },
                                    tooltip: {
                                        callbacks: {
                                            label: function(context) {
                                                const label = context.dataset.label || '';
                                                const value = context.raw || 0;
                                                const total = context.dataset.data.reduce((a, b) => a + b, 0);
                                                const percentage = total > 0 ? Math.round((value / total) * 100) : 0;
                                                return `${label}: ${value} (${percentage}%)`;
                                            }
                                        }
                                    }
                                }
                            }
                        });

                        // Tambahkan tabel perbandingan jika semua lokasi dipilih
                        const $cardBody = $('#ageDistributionChart').closest('.card-body');
                        $cardBody.find('.comparison-table').remove();

                        if (location === 'all') {
                            // Hitung total pencaker
                            const totalCandidates = data.reduce((a, b) => a + b, 0);

                            // Buat tabel perbandingan
                            let tableHtml = `
                                <div class="comparison-table mt-3">
                                    <h6>Distribusi Umur</h6>
                                    <table class="table table-sm table-bordered">
                                        <thead>
                                            <tr>
                                                <th>Kelompok Umur</th>
                                                <th>Jumlah</th>
                                                <th>Persentase</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                            `;

                            response.ageData.forEach((item, index) => {
                                const percentage = totalCandidates > 0 ? ((item.count / totalCandidates) * 100).toFixed(1) : 0;
                                tableHtml += `
                                    <tr>
                                        <td>${item.name || item.age_group}</td>
                                        <td>${item.count}</td>
                                        <td>${percentage}%</td>
                                    </tr>
                                `;
                            });

                            tableHtml += `
                                            <tr class="font-weight-bold">
                                                <td>Total</td>
                                                <td>${totalCandidates}</td>
                                                <td>100%</td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>
                            `;

                            $cardBody.append(tableHtml);
                        }

                        // Sembunyikan loading spinner
                        $('#ageDistributionChart').closest('.card-body').find('.chart-loading').hide();
                    }
                },
                error: function(error) {
                    console.error("Error loading age distribution data:", error);
                    $('#ageDistributionChart').closest('.card-body').find('.chart-loading').hide();
                }
            });
        }

        // Fungsi untuk memuat chart tren lamaran kerja
        function loadApplicationTrendsChart(year, location) {
            $.ajax({
                url: "{{ route('admin.reports.application-trends') }}",
                type: "GET",
                data: {
                    year: year,
                    location: location
                },
                success: function(response) {
                    if (response.success) {
                        const ctx = document.getElementById('applicationTrendsChart').getContext('2d');

                        // Hancurkan chart sebelumnya jika ada
                        if (applicationTrendsChart) {
                            applicationTrendsChart.destroy();
                        }

                        applicationTrendsChart = new Chart(ctx, {
                            type: 'line',
                            data: {
                                labels: response.months,
                                datasets: [
                                    {
                                        label: 'Jumlah Lamaran',
                                        data: response.trendsData,
                                        backgroundColor: chartColors[5],
                                        borderColor: chartColors[5],
                                        borderWidth: 2,
                                        tension: 0.3,
                                        fill: false,
                                        pointRadius: 4,
                                        pointHoverRadius: 6
                                    }
                                ]
                            },
                            options: {
                                responsive: true,
                                maintainAspectRatio: false,
                                scales: {
                                    y: {
                                        beginAtZero: true,
                                        ticks: {
                                            precision: 0
                                        }
                                    }
                                },
                                plugins: {
                                    tooltip: {
                                        mode: 'index',
                                        intersect: false,
                                        callbacks: {
                                            label: function(context) {
                                                return `Jumlah Lamaran: ${context.raw}`;
                                            }
                                        }
                                    }
                                }
                            }
                        });

                        // Tambahkan tabel perbandingan jika semua lokasi dipilih
                        const $cardBody = $('#applicationTrendsChart').closest('.card-body');
                        $cardBody.find('.comparison-table').remove();

                        if (location === 'all') {
                            // Hitung total dan rata-rata lamaran
                            const totalApplications = response.trendsData.reduce((a, b) => a + b, 0);
                            const avgApplications = (totalApplications / response.months.length).toFixed(1);

                            // Cari bulan dengan lamaran tertinggi dan terendah
                            let maxMonth = 0;
                            let minMonth = 0;

                            for (let i = 1; i < response.trendsData.length; i++) {
                                if (response.trendsData[i] > response.trendsData[maxMonth]) {
                                    maxMonth = i;
                                }
                                if (response.trendsData[i] < response.trendsData[minMonth]) {
                                    minMonth = i;
                                }
                            }

                            // Buat tabel perbandingan
                            const tableHtml = `
                                <div class="comparison-table mt-3">
                                    <h6>Statistik Tren Lamaran</h6>
                                    <table class="table table-sm table-bordered">
                                        <thead>
                                            <tr>
                                                <th>Total Lamaran</th>
                                                <th>Rata-rata per Bulan</th>
                                                <th>Bulan Tertinggi</th>
                                                <th>Bulan Terendah</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <tr>
                                                <td>${totalApplications}</td>
                                                <td>${avgApplications}</td>
                                                <td>${response.months[maxMonth]} (${response.trendsData[maxMonth]})</td>
                                                <td>${response.months[minMonth]} (${response.trendsData[minMonth]})</td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>
                            `;

                            $cardBody.append(tableHtml);
                        }

                        // Sembunyikan loading spinner
                        $('#applicationTrendsChart').closest('.card-body').find('.chart-loading').hide();
                    }
                },
                error: function(error) {
                    console.error("Error loading application trends data:", error);
                    $('#applicationTrendsChart').closest('.card-body').find('.chart-loading').hide();
                }
            });
        }

        // Fungsi untuk memuat chart serapan lowongan kerja (diterima)
        function loadJobAbsorptionChart(year, location) {
            $.ajax({
                url: "{{ route('admin.reports.job-absorption') }}",
                type: "GET",
                data: {
                    year: year,
                    location: location
                },
                success: function(response) {
                    if (response.success) {
                        const ctx = document.getElementById('jobAbsorptionChart');
                        if (!ctx) {
                            console.error('Canvas element jobAbsorptionChart not found');
                            return;
                        }
                        const context = ctx.getContext('2d');

                        // Hancurkan chart sebelumnya jika ada
                        if (jobAbsorptionChart) {
                            jobAbsorptionChart.destroy();
                        }

                        jobAbsorptionChart = new Chart(context, {
                            type: 'line',
                            data: {
                                labels: response.months,
                                datasets: [
                                    {
                                        label: 'Tangerang Selatan',
                                        data: response.tangsel_data,
                                        borderColor: chartColors[0],
                                        backgroundColor: 'transparent',
                                        borderWidth: 2,
                                        pointBackgroundColor: chartColors[0],
                                        tension: 0.3
                                    },
                                    {
                                        label: 'Non-Tangerang Selatan',
                                        data: response.non_tangsel_data,
                                        borderColor: chartColors[1],
                                        backgroundColor: 'transparent',
                                        borderWidth: 2,
                                        pointBackgroundColor: chartColors[1],
                                        tension: 0.3
                                    }
                                ]
                            },
                            options: {
                                responsive: true,
                                maintainAspectRatio: false,
                                scales: {
                                    y: {
                                        beginAtZero: true,
                                        ticks: {
                                            precision: 0
                                        },
                                        title: {
                                            display: true,
                                            text: 'Jumlah Pencaker Diterima'
                                        }
                                    },
                                    x: {
                                        title: {
                                            display: true,
                                            text: 'Bulan'
                                        }
                                    }
                                },
                                plugins: {
                                    tooltip: {
                                        mode: 'index',
                                        intersect: false
                                    },
                                    legend: {
                                        position: 'top'
                                    },
                                    title: {
                                        display: true,
                                        text: 'Perbandingan Serapan Tenaga Kerja'
                                    }
                                }
                            }
                        });

                        // Tambahkan tabel perbandingan
                        const $cardBody = $('#jobAbsorptionChart').closest('.card-body');
                        $cardBody.find('.comparison-table').remove();
                        $cardBody.find('.alert').remove();

                        // Hitung total untuk masing-masing lokasi
                        const totalTangsel = response.tangsel_data.reduce((a, b) => a + b, 0);
                        const totalNonTangsel = response.non_tangsel_data.reduce((a, b) => a + b, 0);
                        const totalAll = totalTangsel + totalNonTangsel;

                        // Tampilkan pesan jika tidak ada data
                        if (totalAll === 0) {
                            $cardBody.append(`
                                <div class="alert alert-info mt-3">
                                    <i class="fas fa-info-circle mr-1"></i> Tidak ada data serapan lowongan kerja untuk periode yang dipilih.
                                </div>
                            `);
                        }

                        // Buat tabel perbandingan
                        let tableHtml = `
                            <div class="comparison-table mt-3">
                                <h6>Perbandingan Serapan Tenaga Kerja</h6>
                                <table class="table table-sm table-bordered">
                                    <thead>
                                        <tr>
                                            <th>Lokasi</th>
                                            <th>Total Diterima</th>
                                            <th>Persentase</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                            <td>Tangerang Selatan</td>
                                            <td>${totalTangsel}</td>
                                            <td>${totalAll > 0 ? ((totalTangsel / totalAll) * 100).toFixed(1) : 0}%</td>
                                        </tr>
                                        <tr>
                                            <td>Non-Tangerang Selatan</td>
                                            <td>${totalNonTangsel}</td>
                                            <td>${totalAll > 0 ? ((totalNonTangsel / totalAll) * 100).toFixed(1) : 0}%</td>
                                        </tr>
                                        <tr class="font-weight-bold">
                                            <td>Total</td>
                                            <td>${totalAll}</td>
                                            <td>100%</td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        `;

                        $cardBody.append(tableHtml);

                        // Sembunyikan loading spinner
                        $cardBody.find('.chart-loading').hide();
                    }
                },
                error: function(error) {
                    console.error("Error loading job absorption data:", error);
                    $('#jobAbsorptionChart').closest('.card-body').find('.chart-loading').hide();
                }
            });
        }

        // Fungsi untuk memuat chart perbandingan jenis pekerjaan
        function loadJobTypeComparisonChart(year, location) {
            $.ajax({
                url: "{{ route('admin.reports.job-type-comparison') }}",
                type: "GET",
                data: {
                    year: year,
                    location: location
                },
                success: function(response) {
                    if (response.success) {
                        const ctx = document.getElementById('jobTypeComparisonChart');
                        if (!ctx) {
                            console.error('Canvas element jobTypeComparisonChart not found');
                            return;
                        }
                        const context = ctx.getContext('2d');

                        // Hancurkan chart sebelumnya jika ada
                        if (jobTypeComparisonChart) {
                            jobTypeComparisonChart.destroy();
                        }

                        const labels = response.jobTypes.map(item => item.name);
                        const data = response.jobTypes.map(item => item.count);
                        const backgroundColors = chartColors.slice(0, labels.length);

                        jobTypeComparisonChart = new Chart(context, {
                            type: 'doughnut',
                            data: {
                                labels: labels,
                                datasets: [
                                    {
                                        data: data,
                                        backgroundColor: backgroundColors,
                                        borderColor: backgroundColors,
                                        borderWidth: 1
                                    }
                                ]
                            },
                            options: {
                                responsive: true,
                                maintainAspectRatio: false,
                                plugins: {
                                    legend: {
                                        position: 'right'
                                    },
                                    tooltip: {
                                        callbacks: {
                                            label: function(context) {
                                                const label = context.label || '';
                                                const value = context.raw || 0;
                                                const total = context.dataset.data.reduce((a, b) => a + b, 0);
                                                const percentage = total > 0 ? Math.round((value / total) * 100) : 0;
                                                return `${label}: ${value} (${percentage}%)`;
                                            }
                                        }
                                    }
                                }
                            }
                        });

                        // Tambahkan tabel perbandingan
                        const $cardBody = $('#jobTypeComparisonChart').closest('.card-body');
                        $cardBody.find('.comparison-table').remove();
                        $cardBody.find('.alert').remove();

                        // Hitung total lowongan
                        const totalJobs = data.reduce((a, b) => a + b, 0);

                        // Tampilkan pesan jika tidak ada data
                        if (totalJobs === 0) {
                            $cardBody.append(`
                                <div class="alert alert-info mt-3">
                                    <i class="fas fa-info-circle mr-1"></i> Tidak ada data jenis pekerjaan untuk periode yang dipilih.
                                </div>
                            `);
                        }

                        // Buat tabel perbandingan
                        let tableHtml = `
                            <div class="comparison-table mt-3">
                                <h6>Distribusi Jenis Pekerjaan</h6>
                                <table class="table table-sm table-bordered">
                                    <thead>
                                        <tr>
                                            <th>Jenis Pekerjaan</th>
                                            <th>Jumlah</th>
                                            <th>Persentase</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                        `;

                        response.jobTypes.forEach(item => {
                            const percentage = totalJobs > 0 ? ((item.count / totalJobs) * 100).toFixed(1) : 0;
                            tableHtml += `
                                <tr>
                                    <td>${item.name}</td>
                                    <td>${item.count}</td>
                                    <td>${percentage}%</td>
                                </tr>
                            `;
                        });

                        tableHtml += `
                                        <tr class="font-weight-bold">
                                            <td>Total</td>
                                            <td>${totalJobs}</td>
                                            <td>100%</td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        `;

                        $cardBody.append(tableHtml);

                        // Sembunyikan loading spinner
                        $cardBody.find('.chart-loading').hide();
                    }
                },
                error: function(error) {
                    console.error("Error loading job type comparison data:", error);
                    $('#jobTypeComparisonChart').closest('.card-body').find('.chart-loading').hide();
                }
            });
        }

        // Fungsi untuk memuat chart analisis gaji
        function loadSalaryAnalysisChart(year, location) {
            // Tampilkan loading
            $('#salaryAnalysisChart').closest('.card-body').find('.chart-loading').show();

            $.ajax({
                url: "{{ route('admin.reports.salary-analysis') }}",
                type: "GET",
                data: {
                    year: year,
                    location: location
                },
                success: function(response) {
                    if (response.success) {
                        // Implementasi chart salary analysis
                        console.log('Salary analysis data loaded:', response);
                        // TODO: Implement chart rendering
                    }
                    $('#salaryAnalysisChart').closest('.card-body').find('.chart-loading').hide();
                },
                error: function(error) {
                    console.error("Error loading salary analysis data:", error);
                    $('#salaryAnalysisChart').closest('.card-body').find('.chart-loading').hide();
                }
            });
        }

        // Fungsi untuk memuat chart distribusi gender
        function loadGenderDistributionChart(year, location) {
            // Tampilkan loading
            $('#genderDistributionChart').closest('.card-body').find('.chart-loading').show();

            $.ajax({
                url: "{{ route('admin.reports.gender-distribution') }}",
                type: "GET",
                data: {
                    year: year,
                    location: location
                },
                success: function(response) {
                    if (response.success) {
                        // Implementasi chart gender distribution
                        console.log('Gender distribution data loaded:', response);
                        // TODO: Implement chart rendering
                    }
                    $('#genderDistributionChart').closest('.card-body').find('.chart-loading').hide();
                },
                error: function(error) {
                    console.error("Error loading gender distribution data:", error);
                    $('#genderDistributionChart').closest('.card-body').find('.chart-loading').hide();
                }
            });
        }

        // Fungsi untuk memuat chart analisis kategori pekerjaan
        function loadJobCategoryAnalysisChart(year, location) {
            // Tampilkan loading
            $('#jobCategoryAnalysisChart').closest('.card-body').find('.chart-loading').show();

            $.ajax({
                url: "{{ route('admin.reports.job-category-analysis') }}",
                type: "GET",
                data: {
                    year: year,
                    location: location
                },
                success: function(response) {
                    if (response.success) {
                        // Implementasi chart job category analysis
                        console.log('Job category analysis data loaded:', response);
                        // TODO: Implement chart rendering
                    }
                    $('#jobCategoryAnalysisChart').closest('.card-body').find('.chart-loading').hide();
                },
                error: function(error) {
                    console.error("Error loading job category analysis data:", error);
                    $('#jobCategoryAnalysisChart').closest('.card-body').find('.chart-loading').hide();
                }
            });
        }

        // Fungsi untuk memuat chart analisis lokasi
        function loadLocationAnalysisChart(year, location) {
            // Tampilkan loading
            $('#locationAnalysisChart').closest('.card-body').find('.chart-loading').show();

            $.ajax({
                url: "{{ route('admin.reports.location-analysis') }}",
                type: "GET",
                data: {
                    year: year,
                    location: location
                },
                success: function(response) {
                    if (response.success) {
                        // Implementasi chart location analysis
                        console.log('Location analysis data loaded:', response);
                        // TODO: Implement chart rendering
                    }
                    $('#locationAnalysisChart').closest('.card-body').find('.chart-loading').hide();
                },
                error: function(error) {
                    console.error("Error loading location analysis data:", error);
                    $('#locationAnalysisChart').closest('.card-body').find('.chart-loading').hide();
                }
            });
        }

        // Fungsi untuk memuat chart analisis remote work
        function loadRemoteWorkAnalysisChart(year, location) {
            // Tampilkan loading
            $('#remoteWorkAnalysisChart').closest('.card-body').find('.chart-loading').show();

            $.ajax({
                url: "{{ route('admin.reports.remote-work-analysis') }}",
                type: "GET",
                data: {
                    year: year,
                    location: location
                },
                success: function(response) {
                    if (response.success) {
                        // Implementasi chart remote work analysis
                        console.log('Remote work analysis data loaded:', response);
                        // TODO: Implement chart rendering
                    }
                    $('#remoteWorkAnalysisChart').closest('.card-body').find('.chart-loading').hide();
                },
                error: function(error) {
                    console.error("Error loading remote work analysis data:", error);
                    $('#remoteWorkAnalysisChart').closest('.card-body').find('.chart-loading').hide();
                }
            });
        }

        // Event listener untuk filter tahun dan lokasi - DISABLED
        // Sekarang ditangani oleh loadSelectedReports() di dalam applyReportSelection
        // $('#year-filter, #location-filter').on('change', function() {
        //     loadChartData();
        // });

        // Event listener untuk tombol ekspor PDF
        $('#export-pdf-btn').on('click', function() {
            // Cek laporan yang sedang ditampilkan
            const visibleReports = [];
            const reportNames = {
                'user_comparison': 'Perbandingan Jumlah Pencaker dan Perusahaan',
                'jobs_by_month': 'Data Loker per Bulan',
                'application_status': 'Status Lamaran Kerja Pencaker',
                'job_absorption': 'Serapan Lowongan Kerja (Diterima)',
                'education_distribution': 'Distribusi Pendidikan Pencaker',
                'age_distribution': 'Distribusi Umur Pencaker',
                'application_trends': 'Tren Lamaran Kerja per Bulan',
                'job_type_comparison': 'Perbandingan Jenis Pekerjaan',
                'salary_analysis': 'Analisis Gaji Lowongan',
                'gender_distribution': 'Distribusi Gender Pencaker',
                'job_category_analysis': 'Analisis Kategori Pekerjaan',
                'location_analysis': 'Analisis Sebaran Lokasi',
                'remote_work_analysis': 'Analisis Pekerjaan Remote'
            };

            $('.report-card:visible').each(function() {
                const reportType = $(this).data('report');
                visibleReports.push({
                    type: reportType,
                    name: reportNames[reportType]
                });
            });

            // Update tampilan laporan yang akan diekspor
            if (visibleReports.length > 0) {
                let reportList = '<ul class="list-unstyled">';
                visibleReports.forEach(function(report) {
                    reportList += `<li><i class="fas fa-check text-success"></i> ${report.name}</li>`;
                });
                reportList += '</ul>';
                $('#selectedReportsForExport').html(reportList);
            } else {
                $('#selectedReportsForExport').html('<p class="text-warning"><i class="fas fa-exclamation-triangle"></i> Belum ada laporan yang dipilih untuk ditampilkan</p>');
            }

            // Sinkronkan nilai filter dengan modal
            $('#exportYear').val($('#year-filter').val());
            $('#exportLocation').val($('#location-filter').val());

            // Tampilkan modal
            $('#exportPdfModal').modal('show');
        });

        // Fungsi untuk membuat form ekspor
        function createExportForm(action) {
            // Buat form untuk submit
            const form = $('<form></form>');
            form.attr('method', 'post');
            form.attr('action', action);
            form.attr('target', '_blank');

            // Tambahkan CSRF token
            const csrfToken = $('<input>');
            csrfToken.attr('type', 'hidden');
            csrfToken.attr('name', '_token');
            csrfToken.attr('value', $('meta[name="csrf-token"]').attr('content'));
            form.append(csrfToken);

            // Ambil laporan yang sedang ditampilkan
            const visibleReports = [];
            $('.report-card:visible').each(function() {
                visibleReports.push($(this).data('report'));
            });

            // Tambahkan data laporan yang dipilih
            visibleReports.forEach(function(reportType) {
                const hiddenInput = $('<input>');
                hiddenInput.attr('type', 'hidden');
                hiddenInput.attr('name', 'exportData[]');
                hiddenInput.attr('value', reportType);
                form.append(hiddenInput);
            });

            // Tambahkan filter data
            const yearFilter = $('#year-filter').val();
            const locationFilter = $('#location-filter').val();

            if (yearFilter) {
                const yearInput = $('<input>');
                yearInput.attr('type', 'hidden');
                yearInput.attr('name', 'year');
                yearInput.attr('value', yearFilter);
                form.append(yearInput);
            }

            if (locationFilter) {
                const locationInput = $('<input>');
                locationInput.attr('type', 'hidden');
                locationInput.attr('name', 'location');
                locationInput.attr('value', locationFilter);
                form.append(locationInput);
            }

            // Ambil data tambahan dari form modal
            $('#exportPdfForm').find('input, select').each(function() {
                const input = $(this);
                const name = input.attr('name');

                if (name) {
                    if (input.is(':checkbox')) {
                        if (input.is(':checked')) {
                            const hiddenInput = $('<input>');
                            hiddenInput.attr('type', 'hidden');
                            hiddenInput.attr('name', name);
                            hiddenInput.attr('value', input.val());
                            form.append(hiddenInput);
                        }
                    } else if (input.is('select[multiple]')) {
                        input.find('option:selected').each(function() {
                            const hiddenInput = $('<input>');
                            hiddenInput.attr('type', 'hidden');
                            hiddenInput.attr('name', name);
                            hiddenInput.attr('value', $(this).val());
                            form.append(hiddenInput);
                        });
                    } else {
                        const hiddenInput = $('<input>');
                        hiddenInput.attr('type', 'hidden');
                        hiddenInput.attr('name', name);
                        hiddenInput.attr('value', input.val());
                        form.append(hiddenInput);
                    }
                }
            });

            return form;
        }

        // Event listener untuk tombol ekspor PDF
        $('#confirmExportBtn').on('click', function() {
            // Tampilkan loading spinner
            $(this).html('<i class="fas fa-spinner fa-spin"></i> Mengekspor...');
            $(this).prop('disabled', true);

            // Buat dan submit form
            const form = createExportForm("{{ route('admin.reports.export-pdf') }}");
            $('body').append(form);
            form.submit();
            form.remove();

            // Reset button setelah 3 detik
            setTimeout(function() {
                $('#confirmExportBtn').html('<i class="fas fa-file-pdf"></i> {{ __("PDF") }}');
                $('#confirmExportBtn').prop('disabled', false);

                // Tutup modal
                $('#exportPdfModal').modal('hide');

                // Tampilkan pesan sukses
                toastr.success('Laporan berhasil diekspor ke PDF');
            }, 3000);
        });

        // Event listener untuk tombol ekspor Excel
        $('#exportExcelBtn').on('click', function() {
            // Tampilkan loading spinner
            $(this).html('<i class="fas fa-spinner fa-spin"></i> Mengekspor...');
            $(this).prop('disabled', true);

            // Buat dan submit form
            const form = createExportForm("{{ route('admin.reports.export-excel') }}");
            $('body').append(form);
            form.submit();
            form.remove();

            // Reset button setelah 3 detik
            setTimeout(function() {
                $('#exportExcelBtn').html('<i class="fas fa-file-excel"></i> {{ __("Excel") }}');
                $('#exportExcelBtn').prop('disabled', false);

                // Tutup modal
                $('#exportPdfModal').modal('hide');

                // Tampilkan pesan sukses
                toastr.success('Laporan berhasil diekspor ke Excel');
            }, 3000);
        });

        // Tambahkan tombol preview PDF
        $('#exportPdfModal .modal-footer').prepend(
            $('<button type="button" class="btn btn-info" id="previewPdfBtn"><i class="fas fa-eye"></i> {{ __("Preview") }}</button>')
        );

        // Event listener untuk tombol preview PDF
        $('#previewPdfBtn').on('click', function() {
            // Tampilkan loading spinner
            $(this).html('<i class="fas fa-spinner fa-spin"></i> Memuat...');
            $(this).prop('disabled', true);

            // Buat dan submit form
            const form = createExportForm("{{ route('admin.reports.preview-pdf') }}");
            $('body').append(form);
            form.submit();
            form.remove();

            // Reset button setelah 3 detik
            setTimeout(function() {
                $('#previewPdfBtn').html('<i class="fas fa-eye"></i> {{ __("Preview") }}');
                $('#previewPdfBtn').prop('disabled', false);
            }, 3000);
        });

        // Fungsi untuk menangani pemilihan laporan
        $('#select-reports-btn').on('click', function() {
            $('#selectReportsModal').modal('show');
        });

        // Fungsi untuk pilih semua laporan
        $('#selectAllReports').on('click', function() {
            $('#selectReportsForm input[type="checkbox"]').prop('checked', true);
        });

        // Fungsi untuk hapus semua pilihan laporan
        $('#deselectAllReports').on('click', function() {
            $('#selectReportsForm input[type="checkbox"]').prop('checked', false);
        });

        // Fungsi untuk menerapkan pilihan laporan
        $('#applyReportSelection').on('click', function() {
            const selectedReports = [];
            $('#selectReportsForm input[type="checkbox"]:checked').each(function() {
                selectedReports.push($(this).val());
            });

            // Sembunyikan semua card laporan
            $('.report-card').hide();

            // Tampilkan card laporan yang dipilih
            if (selectedReports.length > 0) {
                $('#instruction-message').hide();
                selectedReports.forEach(function(reportType) {
                    $(`.report-card[data-report="${reportType}"]`).show();
                });

                // Load data untuk laporan yang dipilih
                loadSelectedReports(selectedReports);
            } else {
                $('#instruction-message').show();
            }

            // Tutup modal
            $('#selectReportsModal').modal('hide');
        });

        // Fungsi untuk memuat data laporan yang dipilih
        function loadSelectedReports(selectedReports) {
            const year = $('#year-filter').val() || new Date().getFullYear();
            const location = $('#location-filter').val() || 'all';

            selectedReports.forEach(function(reportType) {
                switch(reportType) {
                    case 'user_comparison':
                        loadUserComparisonChart(year, location);
                        break;
                    case 'jobs_by_month':
                        loadJobsByMonthChart(year, location);
                        break;
                    case 'application_status':
                        loadApplicationStatusChart(year, location);
                        break;
                    case 'job_absorption':
                        loadJobAbsorptionChart(year, location);
                        break;
                    case 'education_distribution':
                        loadEducationDistributionChart(year, location);
                        break;
                    case 'age_distribution':
                        loadAgeDistributionChart(year, location);
                        break;
                    case 'application_trends':
                        loadApplicationTrendsChart(year, location);
                        break;
                    case 'job_type_comparison':
                        loadJobTypeComparisonChart(year, location);
                        break;
                    case 'salary_analysis':
                        loadSalaryAnalysisChart(year, location);
                        break;
                    case 'gender_distribution':
                        loadGenderDistributionChart(year, location);
                        break;
                    case 'job_category_analysis':
                        loadJobCategoryAnalysisChart(year, location);
                        break;
                    case 'location_analysis':
                        loadLocationAnalysisChart(year, location);
                        break;
                    case 'remote_work_analysis':
                        loadRemoteWorkAnalysisChart(year, location);
                        break;
                }
            });
        }

        // Update filter change handlers untuk hanya memuat laporan yang terlihat
        $('#year-filter, #location-filter').on('change', function() {
            const visibleReports = [];
            $('.report-card:visible').each(function() {
                visibleReports.push($(this).data('report'));
            });

            if (visibleReports.length > 0) {
                loadSelectedReports(visibleReports);
            }
        });
        // Inisialisasi Select2
        function initSelect2() {
            $('#exportYears').select2({
                theme: 'bootstrap4',
                placeholder: 'Pilih tahun',
                allowClear: true
            });

            // Inisialisasi select untuk filter tahun
            $('#year-filter').select2({
                theme: 'bootstrap4',
                placeholder: 'Pilih Tahun',
                allowClear: false
            });

            // Jika tidak ada tahun yang dipilih, pilih tahun 2025
            if (!$('#year-filter').val()) {
                $('#year-filter').val('2025').trigger('change');
            }
        }

        // Logika checkbox lokasi
        function handleLocationCheckboxes() {
            // Ketika checkbox "Semua Lokasi" dicentang/dilepas
            $('#exportLocationAll').on('change', function() {
                if ($(this).is(':checked')) {
                    // Nonaktifkan checkbox lainnya
                    $('#exportLocationTangsel, #exportLocationNonTangsel').prop('checked', false);
                }
            });

            // Ketika checkbox lokasi spesifik dicentang
            $('#exportLocationTangsel, #exportLocationNonTangsel').on('change', function() {
                // Jika salah satu dicentang, lepaskan centang "Semua Lokasi"
                if ($(this).is(':checked')) {
                    $('#exportLocationAll').prop('checked', false);
                }

                // Jika tidak ada yang dicentang, centang "Semua Lokasi"
                if (!$('#exportLocationTangsel').is(':checked') && !$('#exportLocationNonTangsel').is(':checked')) {
                    $('#exportLocationAll').prop('checked', true);
                }
            });
        }

        // Validasi rentang bulan
        function validateMonthRange() {
            $('#exportMonthStart, #exportMonthEnd').on('change', function() {
                const startMonth = parseInt($('#exportMonthStart').val());
                const endMonth = parseInt($('#exportMonthEnd').val());

                // Jika bulan awal lebih besar dari bulan akhir
                if (startMonth > endMonth) {
                    // Set bulan akhir sama dengan bulan awal
                    $('#exportMonthEnd').val(startMonth);
                }
            });
        }

        // Inisialisasi halaman tanpa memuat data chart
        $(document).ready(function() {
            // Jangan load chart data di awal - hanya load ketika laporan dipilih
            initSelect2();
            handleLocationCheckboxes();
            validateMonthRange();
        });
    </script>
@endsection

