<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Laporan Statistik Data</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 10px;
            color: #333;
            font-size: 10pt;
        }
        .header {
            text-align: center;
            margin-bottom: 10px;
        }
        .header h1 {
            margin: 0;
            color: #2c3e50;
            font-size: 16pt;
        }
        .header p {
            margin: 2px 0;
            color: #7f8c8d;
            font-size: 10pt;
        }
        .section {
            margin-bottom: 15px;
            page-break-inside: avoid;
        }
        .section h2 {
            color: #3498db;
            font-size: 14pt;
            border-bottom: 1px solid #eee;
            padding-bottom: 3px;
            margin-bottom: 10px;
        }
        .section h3 {
            font-size: 12pt;
            margin-top: 10px;
            margin-bottom: 5px;
        }
        .section h4 {
            font-size: 11pt;
            margin-top: 8px;
            margin-bottom: 5px;
        }
        .section h5 {
            font-size: 10pt;
            margin-top: 8px;
            margin-bottom: 5px;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 10px;
            font-size: 9pt;
        }
        table, th, td {
            border: 1px solid #ddd;
        }
        th, td {
            padding: 4px;
            text-align: left;
        }
        th {
            background-color: #f2f2f2;
        }
        .chart-container {
            width: 100%;
            height: 200px;
            margin-bottom: 10px;
        }
        .footer {
            margin-top: 15px;
            text-align: center;
            font-size: 8pt;
            color: #7f8c8d;
            border-top: 1px solid #eee;
            padding-top: 5px;
        }
        .page-break {
            page-break-after: always;
        }
        .compact-table {
            font-size: 8pt;
        }
        .compact-table th, .compact-table td {
            padding: 3px;
        }
        .versus-table {
            margin-top: 5px;
            margin-bottom: 10px;
        }
        .report-description {
            margin-bottom: 20px;
            font-size: 9pt;
            text-align: justify;
        }
        .report-description p {
            margin: 5px 0;
        }
        .report-description ul {
            margin: 5px 0;
            padding-left: 20px;
        }
        .report-description li {
            margin-bottom: 3px;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>Laporan Statistik Data</h1>
        <p>Periode: {{ $period }}</p>
        <p>Lokasi: {{ $location }}</p>
        <p>Tanggal Ekspor: {{ $export_date }}</p>
    </div>

    <div class="report-description">
        <br />
        <br />
        <p>Laporan ini berisi data statistik dari website Anggur (Anti Nganggur) yang dikelola oleh Dinas Tenaga Kerja Kota Tangerang Selatan. Data yang disajikan mencakup:</p>

        <ul>
            <li><strong>Perbandingan Jumlah Pencaker dan Perusahaan</strong> - Menampilkan perbandingan jumlah pencari kerja dan perusahaan yang terdaftar per bulan.</li>
            <li><strong>Data Loker per Bulan</strong> - Menampilkan jumlah lowongan kerja yang dipublikasikan per bulan.</li>
            <li><strong>Status Lamaran Kerja Pencaker</strong> - Menampilkan distribusi status lamaran kerja (semua lamaran, interview, diterima, ditolak).</li>
            <li><strong>Distribusi Pendidikan Pencaker</strong> - Menampilkan distribusi tingkat pendidikan terakhir pencari kerja.</li>
            <li><strong>Distribusi Umur Pencaker</strong> - Menampilkan distribusi kelompok umur pencari kerja.</li>
            <li><strong>Tren Lamaran Kerja per Bulan</strong> - Menampilkan tren jumlah lamaran kerja yang diajukan per bulan.</li>
        </ul>

        <p>Untuk lokasi Tangerang Selatan dan Non-Tangerang Selatan, laporan ini juga menyajikan perbandingan data antara kedua lokasi tersebut.</p>

        <p>Laporan ini dapat digunakan untuk:</p>
        <ul>
            <li>Menganalisis tren pasar kerja di Kota Tangerang Selatan</li>
            <li>Mengevaluasi efektivitas program ketenagakerjaan</li>
            <li>Merencanakan strategi pengembangan ketenagakerjaan</li>
            <li>Mengidentifikasi kebutuhan pelatihan dan pengembangan SDM</li>
        </ul>
    </div>

    @foreach($data as $yearData)
    <div class="section">
        <h2>Tahun {{ $yearData['year'] }}</h2>

        @foreach($yearData['reports'] as $locationData)
        <h3>{{ $locationData['location'] }}</h3>

        @if(isset($locationData['data']['user_comparison']))
        <div class="section">
            <h4>{{ $locationData['data']['user_comparison']['title'] }}</h4>
            <table>
                <thead>
                    <tr>
                        <th>Bulan</th>
                        <th>Pencaker</th>
                        <th>Perusahaan</th>
                        @if(isset($locationData['data']['user_comparison']['is_versus']) && $locationData['data']['user_comparison']['is_versus'])
                        <th>Rasio</th>
                        @endif
                    </tr>
                </thead>
                <tbody>
                    @foreach($locationData['data']['user_comparison']['months'] as $index => $month)
                    <tr>
                        <td>{{ $month }}</td>
                        <td>{{ $locationData['data']['user_comparison']['candidates'][$index] ?? 0 }}</td>
                        <td>{{ $locationData['data']['user_comparison']['companies'][$index] ?? 0 }}</td>
                        @if(isset($locationData['data']['user_comparison']['is_versus']) && $locationData['data']['user_comparison']['is_versus'])
                        <td>
                            @php
                                $candidates = $locationData['data']['user_comparison']['candidates'][$index] ?? 0;
                                $companies = $locationData['data']['user_comparison']['companies'][$index] ?? 0;
                            @endphp
                            @if($companies > 0)
                                {{ number_format($candidates / $companies, 1) }} : 1
                            @else
                                N/A
                            @endif
                        </td>
                        @endif
                    </tr>
                    @endforeach
                </tbody>
            </table>

            @if(isset($locationData['data']['user_comparison']['versus_data']))
            <h5 class="mt-4">Perbandingan Lokasi</h5>
            <table>
                <thead>
                    <tr>
                        <th>Lokasi</th>
                        <th>Total Pencaker</th>
                        <th>Total Perusahaan</th>
                        <th>Rasio</th>
                    </tr>
                </thead>
                <tbody>
                    @foreach($locationData['data']['user_comparison']['versus_data'] as $versus)
                    <tr>
                        <td>{{ $versus['location'] ?? 'N/A' }}</td>
                        <td>{{ $versus['total_candidates'] ?? 0 }}</td>
                        <td>{{ $versus['total_companies'] ?? 0 }}</td>
                        <td>
                            @php
                                $totalCandidates = $versus['total_candidates'] ?? 0;
                                $totalCompanies = $versus['total_companies'] ?? 0;
                            @endphp
                            @if($totalCompanies > 0)
                                {{ number_format($totalCandidates / $totalCompanies, 1) }} : 1
                            @else
                                N/A
                            @endif
                        </td>
                    </tr>
                    @endforeach
                </tbody>
            </table>
            @endif
        </div>
        @endif

        @if(isset($locationData['data']['jobs_by_month']))
        <div class="section">
            <h4>{{ $locationData['data']['jobs_by_month']['title'] }}</h4>
            <table>
                <thead>
                    <tr>
                        <th>Bulan</th>
                        <th>Jumlah Loker</th>
                    </tr>
                </thead>
                <tbody>
                    @foreach($locationData['data']['jobs_by_month']['months'] as $index => $month)
                    <tr>
                        <td>{{ $month }}</td>
                        <td>{{ $locationData['data']['jobs_by_month']['jobs'][$index] ?? 0 }}</td>
                    </tr>
                    @endforeach
                </tbody>
            </table>

            @if(isset($locationData['data']['jobs_by_month']['versus_data']))
            <h5 class="mt-4">Perbandingan Lokasi</h5>
            <table>
                <thead>
                    <tr>
                        <th>Lokasi</th>
                        <th>Total Loker</th>
                        <th>Rata-rata per Bulan</th>
                    </tr>
                </thead>
                <tbody>
                    @foreach($locationData['data']['jobs_by_month']['versus_data'] as $versus)
                    <tr>
                        <td>{{ $versus['location'] }}</td>
                        <td>{{ $versus['total_jobs'] }}</td>
                        <td>{{ number_format($versus['monthly_avg'], 1) }}</td>
                    </tr>
                    @endforeach
                </tbody>
            </table>
            @endif
        </div>
        @endif

        @if(isset($locationData['data']['application_status']))
        <div class="section">
            <h4>{{ $locationData['data']['application_status']['title'] }}</h4>
            <table>
                <thead>
                    <tr>
                        <th>Status</th>
                        <th>Jumlah</th>
                    </tr>
                </thead>
                <tbody>
                    @foreach($locationData['data']['application_status']['statusData'] as $status)
                    <tr>
                        <td>{{ $status['name'] ?? $status['status'] ?? 'N/A' }}</td>
                        <td>{{ $status['count'] ?? 0 }}</td>
                    </tr>
                    @endforeach
                </tbody>
            </table>

            @if(isset($locationData['data']['application_status']['versus_data']))
            <h5 class="mt-4">Perbandingan Lokasi</h5>
            <table>
                <thead>
                    <tr>
                        <th>Lokasi</th>
                        <th>Total Lamaran</th>
                    </tr>
                </thead>
                <tbody>
                    @foreach($locationData['data']['application_status']['versus_data'] as $versus)
                    <tr>
                        <td>{{ $versus['location'] }}</td>
                        <td>{{ $versus['total_applications'] }}</td>
                    </tr>
                    @endforeach
                </tbody>
            </table>
            @endif
        </div>
        @endif

        @if(isset($locationData['data']['education_distribution']))
        <div class="section">
            <h4>{{ $locationData['data']['education_distribution']['title'] }}</h4>
            <table>
                <thead>
                    <tr>
                        <th>Pendidikan</th>
                        <th>Jumlah</th>
                    </tr>
                </thead>
                <tbody>
                    @foreach($locationData['data']['education_distribution']['educationData'] as $education)
                    <tr>
                        <td>{{ $education['pendidikan_terakhir'] ?? $education['name'] ?? 'N/A' }}</td>
                        <td>{{ $education['count'] ?? 0 }}</td>
                    </tr>
                    @endforeach
                </tbody>
            </table>

            @if(isset($locationData['data']['education_distribution']['versus_data']))
            <h5 class="mt-4">Perbandingan Lokasi</h5>
            <table>
                <thead>
                    <tr>
                        <th>Lokasi</th>
                        <th>Total Pencaker</th>
                    </tr>
                </thead>
                <tbody>
                    @foreach($locationData['data']['education_distribution']['versus_data'] as $versus)
                    <tr>
                        <td>{{ $versus['location'] }}</td>
                        <td>{{ $versus['total_candidates'] }}</td>
                    </tr>
                    @endforeach
                </tbody>
            </table>
            @endif
        </div>
        @endif

        @if(isset($locationData['data']['age_distribution']))
        <div class="section">
            <h4>{{ $locationData['data']['age_distribution']['title'] }}</h4>
            <table>
                <thead>
                    <tr>
                        <th>Kelompok Umur</th>
                        <th>Jumlah</th>
                    </tr>
                </thead>
                <tbody>
                    @foreach($locationData['data']['age_distribution']['ageData'] as $age)
                    <tr>
                        <td>{{ $age['name'] ?? $age['age_group'] ?? 'N/A' }}</td>
                        <td>{{ $age['count'] ?? 0 }}</td>
                    </tr>
                    @endforeach
                </tbody>
            </table>

            @if(isset($locationData['data']['age_distribution']['versus_data']))
            <h5 class="mt-4">Perbandingan Lokasi</h5>
            <table>
                <thead>
                    <tr>
                        <th>Lokasi</th>
                        <th>Total Pencaker</th>
                    </tr>
                </thead>
                <tbody>
                    @foreach($locationData['data']['age_distribution']['versus_data'] as $versus)
                    <tr>
                        <td>{{ $versus['location'] }}</td>
                        <td>{{ $versus['total_candidates'] }}</td>
                    </tr>
                    @endforeach
                </tbody>
            </table>
            @endif
        </div>
        @endif

        @if(isset($locationData['data']['application_trends']))
        <div class="section">
            <h4>{{ $locationData['data']['application_trends']['title'] }}</h4>
            <table>
                <thead>
                    <tr>
                        <th>Bulan</th>
                        <th>Jumlah Lamaran</th>
                    </tr>
                </thead>
                <tbody>
                    @foreach($locationData['data']['application_trends']['months'] as $index => $month)
                    <tr>
                        <td>{{ $month }}</td>
                        <td>{{ $locationData['data']['application_trends']['trendsData'][$index] ?? 0 }}</td>
                    </tr>
                    @endforeach
                </tbody>
            </table>

            @if(isset($locationData['data']['application_trends']['versus_data']))
            <h5 class="mt-4">Perbandingan Lokasi</h5>
            <table>
                <thead>
                    <tr>
                        <th>Lokasi</th>
                        <th>Total Lamaran</th>
                        <th>Rata-rata per Bulan</th>
                    </tr>
                </thead>
                <tbody>
                    @foreach($locationData['data']['application_trends']['versus_data'] as $versus)
                    <tr>
                        <td>{{ $versus['location'] }}</td>
                        <td>{{ $versus['total_applications'] }}</td>
                        <td>{{ number_format($versus['monthly_avg'], 1) }}</td>
                    </tr>
                    @endforeach
                </tbody>
            </table>
            @endif
        </div>
        @endif

        @if(!$loop->last)
        <div class="page-break"></div>
        @endif
        @endforeach
    </div>

    @if(!$loop->last)
    <div class="page-break"></div>
    @endif
    @endforeach

    <div class="footer">
        <p>© 2025 Anggur by Disnaker Kota Tangerang Selatan - Laporan Statistik Data Website Anggur</p>
    </div>
</body>
</html>
