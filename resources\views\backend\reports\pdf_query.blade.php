<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Laporan Query Reporting</title>
    <style>
        body {
            font-family: 'DejaVu Sans', sans-serif;
            font-size: 12px;
            line-height: 1.4;
            color: #333;
            margin: 0;
            padding: 20px;
        }
        
        .header {
            text-align: center;
            margin-bottom: 30px;
            border-bottom: 2px solid #007bff;
            padding-bottom: 20px;
        }
        
        .header h1 {
            color: #007bff;
            font-size: 24px;
            margin: 0 0 10px 0;
            font-weight: bold;
        }
        
        .header .subtitle {
            font-size: 14px;
            color: #666;
            margin: 5px 0;
        }
        
        .info-box {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 15px;
            margin-bottom: 20px;
        }
        
        .info-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 8px;
        }
        
        .info-row:last-child {
            margin-bottom: 0;
        }
        
        .info-label {
            font-weight: bold;
            color: #495057;
        }
        
        .info-value {
            color: #007bff;
        }
        
        .report-section {
            margin-bottom: 40px;
            page-break-inside: avoid;
        }
        
        .report-title {
            background-color: #007bff;
            color: white;
            padding: 12px 15px;
            margin: 0 0 15px 0;
            font-size: 16px;
            font-weight: bold;
            border-radius: 5px;
        }
        
        .data-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
        }
        
        .data-table th,
        .data-table td {
            border: 1px solid #dee2e6;
            padding: 8px 12px;
            text-align: left;
        }
        
        .data-table th {
            background-color: #f8f9fa;
            font-weight: bold;
            color: #495057;
        }
        
        .data-table tr:nth-child(even) {
            background-color: #f8f9fa;
        }
        
        .summary-stats {
            display: flex;
            justify-content: space-around;
            margin: 20px 0;
        }
        
        .stat-box {
            text-align: center;
            padding: 15px;
            background-color: #f8f9fa;
            border-radius: 5px;
            border: 1px solid #dee2e6;
            min-width: 120px;
        }
        
        .stat-number {
            font-size: 24px;
            font-weight: bold;
            color: #007bff;
            display: block;
        }
        
        .stat-label {
            font-size: 11px;
            color: #666;
            margin-top: 5px;
        }
        
        .no-data {
            text-align: center;
            color: #6c757d;
            font-style: italic;
            padding: 20px;
        }
        
        .footer {
            position: fixed;
            bottom: 20px;
            left: 20px;
            right: 20px;
            text-align: center;
            font-size: 10px;
            color: #666;
            border-top: 1px solid #dee2e6;
            padding-top: 10px;
        }
        
        .page-break {
            page-break-before: always;
        }
        
        @media print {
            body {
                margin: 0;
                padding: 15px;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <div class="header">
        <h1>LAPORAN QUERY REPORTING</h1>
        <div class="subtitle">Sistem Informasi Ketenagakerjaan</div>
        <div class="subtitle">Dinas Komunikasi dan Informatika Kota Tangerang Selatan</div>
    </div>

    <!-- Info Box -->
    <div class="info-box">
        <div class="info-row">
            <span class="info-label">Periode:</span>
            <span class="info-value">Tahun {{ $year }}</span>
        </div>
        <div class="info-row">
            <span class="info-label">Lokasi:</span>
            <span class="info-value">{{ $location }}</span>
        </div>
        <div class="info-row">
            <span class="info-label">Jumlah Laporan:</span>
            <span class="info-value">{{ $total_reports }} Laporan</span>
        </div>
        <div class="info-row">
            <span class="info-label">Tanggal Ekspor:</span>
            <span class="info-value">{{ $export_date }}</span>
        </div>
    </div>

    <!-- Reports -->
    @foreach($reports as $index => $report)
        @if($index > 0)
            <div class="page-break"></div>
        @endif
        
        <div class="report-section">
            <h2 class="report-title">{{ $report['title'] }}</h2>
            
            @if($report['data']['success'] ?? false)
                @php
                    $data = $report['data'];
                    $reportType = $report['type'];
                @endphp
                
                @if($reportType === 'user_comparison')
                    <div class="summary-stats">
                        <div class="stat-box">
                            <span class="stat-number">{{ $data['candidateCount'] ?? 0 }}</span>
                            <div class="stat-label">Total Pencaker</div>
                        </div>
                        <div class="stat-box">
                            <span class="stat-number">{{ $data['companyCount'] ?? 0 }}</span>
                            <div class="stat-label">Total Perusahaan</div>
                        </div>
                    </div>
                    
                @elseif($reportType === 'jobs_by_month')
                    <table class="data-table">
                        <thead>
                            <tr>
                                <th>Bulan</th>
                                <th>Jumlah Lowongan</th>
                            </tr>
                        </thead>
                        <tbody>
                            @if(isset($data['monthlyData']))
                                @foreach($data['monthlyData'] as $monthData)
                                    <tr>
                                        <td>{{ $monthData['month'] ?? '-' }}</td>
                                        <td>{{ $monthData['count'] ?? 0 }}</td>
                                    </tr>
                                @endforeach
                            @endif
                        </tbody>
                    </table>
                    
                @elseif($reportType === 'salary_analysis')
                    <table class="data-table">
                        <thead>
                            <tr>
                                <th>Range Gaji</th>
                                <th>Jumlah Lowongan</th>
                            </tr>
                        </thead>
                        <tbody>
                            @if(isset($data['salaryRanges']))
                                @foreach($data['salaryRanges'] as $range => $count)
                                    <tr>
                                        <td>{{ $range }}</td>
                                        <td>{{ $count }}</td>
                                    </tr>
                                @endforeach
                            @endif
                        </tbody>
                    </table>
                    
                @elseif($reportType === 'gender_distribution')
                    <table class="data-table">
                        <thead>
                            <tr>
                                <th>Gender</th>
                                <th>Jumlah Pencaker</th>
                            </tr>
                        </thead>
                        <tbody>
                            @if(isset($data['genderDistribution']))
                                @foreach($data['genderDistribution'] as $gender => $count)
                                    <tr>
                                        <td>{{ $gender }}</td>
                                        <td>{{ $count }}</td>
                                    </tr>
                                @endforeach
                            @endif
                        </tbody>
                    </table>
                    
                @else
                    <!-- Generic data display for other report types -->
                    @if(isset($data) && is_array($data))
                        <table class="data-table">
                            <thead>
                                <tr>
                                    <th>Item</th>
                                    <th>Nilai</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach($data as $key => $value)
                                    @if($key !== 'success' && !is_array($value))
                                        <tr>
                                            <td>{{ ucfirst(str_replace('_', ' ', $key)) }}</td>
                                            <td>{{ $value }}</td>
                                        </tr>
                                    @endif
                                @endforeach
                            </tbody>
                        </table>
                    @endif
                @endif
                
            @else
                <div class="no-data">
                    Data tidak tersedia untuk laporan ini
                </div>
            @endif
        </div>
    @endforeach

    <!-- Footer -->
    <div class="footer">
        <p>Dokumen ini dibuat secara otomatis oleh Sistem Informasi Ketenagakerjaan - {{ $export_date }}</p>
    </div>
</body>
</html>
