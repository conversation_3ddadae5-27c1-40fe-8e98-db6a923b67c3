@extends('backend.settings.setting-layout')

@section('title')
    {{ __('Tools Database') }}
@endsection

@section('breadcrumbs')
    <div class="row mb-2 mt-4">
        <div class="col-sm-6">
            <h1 class="m-0">{{ __('Tools Database') }}</h1>
        </div>
        <div class="col-sm-6">
            <ol class="breadcrumb float-sm-right">
                <li class="breadcrumb-item"><a href="{{ route('admin.dashboard') }}">{{ __('home') }}</a></li>
                <li class="breadcrumb-item">{{ __('settings') }}</li>
                <li class="breadcrumb-item active">{{ __('Tools Database') }}</li>
            </ol>
        </div>
    </div>
@endsection

@section('website-settings')
<div class="container-fluid">
    <div class="row">
        <!-- Backup & Restore Section -->
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">{{ __('Backup & Restore') }}</h3>
                </div>
                <div class="card-body">
                    <div class="mb-4">
                        <h5>{{ __('Buat Backup') }}</h5>
                        <form action="{{ route('settings.database-tools.backup.create') }}" method="POST">
                            @csrf
                            <div class="form-group">
                                <label for="backup_type">{{ __('Tipe Backup') }}</label>
                                <select name="backup_type" id="backup_type" class="form-control">
                                    <option value="db">{{ __('Database Saja') }}</option>
                                    <option value="full">{{ __('Backup Lengkap (Database & File)') }}</option>
                                </select>
                            </div>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-download mr-1"></i> {{ __('Buat Backup') }}
                            </button>
                        </form>
                    </div>

                    <div class="mb-4">
                        <h5>{{ __('Restore dari File') }}</h5>
                        <form action="{{ route('settings.database-tools.backup.restore-upload') }}" method="POST" enctype="multipart/form-data">
                            @csrf
                            <div class="form-group">
                                <label for="backup_file">{{ __('File Backup') }}</label>
                                <div class="custom-file">
                                    <input type="file" class="custom-file-input" id="backup_file" name="backup_file" accept=".zip,.sql" required>
                                    <label class="custom-file-label" for="backup_file">{{ __('Pilih file...') }}</label>
                                    <small class="form-text text-muted">{{ __('Format yang didukung: .zip, .sql') }}</small>
                                </div>
                            </div>
                            <button type="button" class="btn btn-warning" onclick="confirmRestoreUpload()">
                                <i class="fas fa-undo mr-1"></i> {{ __('Restore') }}
                            </button>
                        </form>
                    </div>

                    <div class="mt-4">
                        <h5>{{ __('Backup Tersedia') }}</h5>
                        <div class="table-responsive">
                            <table class="table table-bordered table-striped">
                                <thead>
                                    <tr>
                                        <th>{{ __('Nama File') }}</th>
                                        <th>{{ __('Ukuran') }}</th>
                                        <th>{{ __('Tanggal') }}</th>
                                        <th>{{ __('Aksi') }}</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @if(count($backups) > 0)
                                        @foreach($backups as $backup)
                                            <tr>
                                                <td>{{ $backup['name'] }}</td>
                                                <td>{{ $backup['size'] }}</td>
                                                <td>{{ $backup['date'] }}</td>
                                                <td class="d-flex">
                                                    <form action="{{ route('settings.database-tools.backup.download') }}" method="POST" class="mr-1">
                                                        @csrf
                                                        <input type="hidden" name="path" value="{{ $backup['path'] }}">
                                                        <button type="submit" class="btn btn-sm btn-info" data-toggle="tooltip" title="Download Backup">
                                                            <i class="fas fa-download"></i>
                                                        </button>
                                                    </form>
                                                    <button type="button" class="btn btn-sm btn-success mr-1" data-toggle="tooltip" title="Restore Backup" onclick="confirmRestore('restoreModal{{ $loop->index }}')">
                                                        <i class="fas fa-undo"></i>
                                                    </button>
                                                    <button type="button" class="btn btn-sm btn-danger" data-toggle="tooltip" title="Hapus Backup" onclick="confirmDelete('deleteModal{{ $loop->index }}')">
                                                        <i class="fas fa-trash"></i>
                                                    </button>
                                                </td>
                                            </tr>

                                            <!-- Restore Modal -->
                                            <div class="modal fade" id="restoreModal{{ $loop->index }}" tabindex="-1" role="dialog" aria-labelledby="restoreModalLabel" aria-hidden="true">
                                                <div class="modal-dialog" role="document">
                                                    <div class="modal-content">
                                                        <div class="modal-header bg-warning">
                                                            <h5 class="modal-title" id="restoreModalLabel">{{ __('Restore Backup') }}</h5>
                                                            <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                                                                <span aria-hidden="true">&times;</span>
                                                            </button>
                                                        </div>
                                                        <form action="{{ route('settings.database-tools.backup.restore') }}" method="POST">
                                                            @csrf
                                                            <div class="modal-body">
                                                                <input type="hidden" name="path" value="{{ $backup['path'] }}">
                                                                <div class="alert alert-warning">
                                                                    <i class="fas fa-exclamation-triangle mr-1"></i>
                                                                    <strong>{{ __('Peringatan!') }}</strong> {{ __('Restore backup akan menimpa database dan file yang ada saat ini. Tindakan ini tidak dapat dibatalkan.') }}
                                                                </div>
                                                                <div class="form-group">
                                                                    <label for="confirmation">{{ __('Ketik "RESTORE" untuk konfirmasi:') }}</label>
                                                                    <input type="text" name="confirmation" id="confirmation" class="form-control" required>
                                                                </div>
                                                                <div class="form-group">
                                                                    <div class="custom-control custom-checkbox">
                                                                        <input type="checkbox" class="custom-control-input" id="accept_risks{{ $loop->index }}" name="accept_risks" required>
                                                                        <label class="custom-control-label" for="accept_risks{{ $loop->index }}">
                                                                            {{ __('Saya memahami risiko dan menerima tanggung jawab penuh atas tindakan ini') }}
                                                                        </label>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                            <div class="modal-footer">
                                                                <button type="button" class="btn btn-secondary" data-dismiss="modal">{{ __('Batal') }}</button>
                                                                <button type="submit" class="btn btn-warning">{{ __('Restore') }}</button>
                                                            </div>
                                                        </form>
                                                    </div>
                                                </div>
                                            </div>

                                            <!-- Delete Modal -->
                                            <div class="modal fade" id="deleteModal{{ $loop->index }}" tabindex="-1" role="dialog" aria-labelledby="deleteModalLabel" aria-hidden="true">
                                                <div class="modal-dialog" role="document">
                                                    <div class="modal-content">
                                                        <div class="modal-header bg-danger">
                                                            <h5 class="modal-title" id="deleteModalLabel">{{ __('Hapus Backup') }}</h5>
                                                            <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                                                                <span aria-hidden="true">&times;</span>
                                                            </button>
                                                        </div>
                                                        <form action="{{ route('settings.database-tools.backup.delete') }}" method="POST">
                                                            @csrf
                                                            <div class="modal-body">
                                                                <input type="hidden" name="path" value="{{ $backup['path'] }}">
                                                                <p>{{ __('Apakah Anda yakin ingin menghapus backup ini?') }}</p>
                                                                <p><strong>{{ $backup['name'] }}</strong></p>
                                                            </div>
                                                            <div class="modal-footer">
                                                                <button type="button" class="btn btn-secondary" data-dismiss="modal">{{ __('Batal') }}</button>
                                                                <button type="submit" class="btn btn-danger">{{ __('Hapus') }}</button>
                                                            </div>
                                                        </form>
                                                    </div>
                                                </div>
                                            </div>
                                        @endforeach
                                    @else
                                        <tr>
                                            <td colspan="4" class="text-center">{{ __('Tidak ada backup tersedia') }}</td>
                                        </tr>
                                    @endif
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Database Tools Section -->
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">{{ __('Tools Database') }}</h3>
                </div>
                <div class="card-body">
                    <!-- Reset Database -->
                    <div class="mb-4">
                        <h5>{{ __('Reset Database') }}</h5>
                        <p class="text-muted">{{ __('Reset database ke kondisi awal dengan data default.') }}</p>
                        <button type="button" class="btn btn-danger" data-toggle="modal" data-target="#resetDatabaseModal">
                            <i class="fas fa-trash-alt mr-1"></i> {{ __('Reset Database') }}
                        </button>
                    </div>

                    <!-- Clean Data -->
                    <div class="mt-4">
                        <h5>{{ __('Bersihkan Data') }}</h5>
                        <p class="text-muted">{{ __('Hapus data pencaker dan perusahaan (kecuali admin).') }}</p>
                        <div class="row">
                            <div class="col-md-6">
                                <button type="button" class="btn btn-warning btn-block" data-toggle="modal" data-target="#deleteCandidatesModal">
                                    <i class="fas fa-user-times mr-1"></i> {{ __('Hapus Semua Pencaker') }}
                                </button>
                            </div>
                            <div class="col-md-6">
                                <button type="button" class="btn btn-warning btn-block" data-toggle="modal" data-target="#deleteCompaniesModal">
                                    <i class="fas fa-building mr-1"></i> {{ __('Hapus Semua Perusahaan') }}
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- Optimize Database -->
                    <div class="mt-4">
                        <h5>{{ __('Optimasi Database') }}</h5>
                        <p class="text-muted">{{ __('Optimasi tabel database untuk meningkatkan performa.') }}</p>
                        <form action="{{ route('settings.database-tools.database.optimize') }}" method="POST">
                            @csrf
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-bolt mr-1"></i> {{ __('Optimasi Database') }}
                            </button>
                        </form>
                    </div>
                </div>
            </div>

            <!-- Backup Schedule Settings -->
            <div class="card mt-4">
                <div class="card-header">
                    <h3 class="card-title">{{ __('Pengaturan Jadwal Backup') }}</h3>
                </div>
                <div class="card-body">
                    <div class="alert alert-info">
                        <h5><i class="icon fas fa-info"></i> {{ __('Persyaratan Server') }}</h5>
                        <p class="mb-2">{{ __('Backup otomatis memerlukan cron job di server untuk berjalan. Tanpa cron job, backup tidak akan berjalan otomatis.') }}</p>
                        <p class="mb-2"><strong>{{ __('Setup Cron Job:') }}</strong></p>
                        <code>* * * * * cd /var/www/html/public_html && php artisan schedule:run >> /dev/null 2>&1</code>
                        <p class="mt-2 mb-0"><small>{{ __('Hubungi administrator server untuk setup cron job ini.') }}</small></p>
                    </div>
                    <form action="{{ route('settings.database-tools.backup.schedule') }}" method="POST">
                        @csrf
                        <div class="form-group">
                            <div class="custom-control custom-switch">
                                <input type="checkbox" class="custom-control-input" id="enable_schedule" name="enable_schedule" {{ $backupSchedule['enabled'] ? 'checked' : '' }}>
                                <label class="custom-control-label" for="enable_schedule">{{ __('Aktifkan Backup Otomatis') }}</label>
                            </div>
                        </div>
                        <div class="form-group">
                            <label for="frequency">{{ __('Frekuensi Backup') }}</label>
                            <select name="frequency" id="frequency" class="form-control">
                                <option value="daily" {{ $backupSchedule['frequency'] == 'daily' ? 'selected' : '' }}>{{ __('Harian') }}</option>
                                <option value="weekly" {{ $backupSchedule['frequency'] == 'weekly' ? 'selected' : '' }}>{{ __('Mingguan') }}</option>
                                <option value="monthly" {{ $backupSchedule['frequency'] == 'monthly' ? 'selected' : '' }}>{{ __('Bulanan') }}</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="time">{{ __('Waktu Backup') }}</label>
                            <input type="time" name="time" id="time" class="form-control" value="{{ $backupSchedule['time'] }}">
                        </div>
                        <div class="form-group">
                            <label for="keep_days">{{ __('Simpan Backup Selama (Hari)') }}</label>
                            <input type="number" name="keep_days" id="keep_days" class="form-control" value="{{ $backupSchedule['keep_days'] }}" min="1">
                        </div>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save mr-1"></i> {{ __('Simpan Pengaturan') }}
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Reset Database Modal -->
<div class="modal fade" id="resetDatabaseModal" tabindex="-1" role="dialog" aria-labelledby="resetDatabaseModalLabel" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header bg-danger">
                <h5 class="modal-title" id="resetDatabaseModalLabel">{{ __('Reset Database') }}</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <form action="{{ route('settings.database-tools.database.reset') }}" method="POST">
                @csrf
                <div class="modal-body">
                    <div class="alert alert-danger">
                        <i class="fas fa-exclamation-triangle mr-1"></i>
                        <strong>{{ __('Bahaya!') }}</strong> {{ __('Reset database akan menghapus SEMUA data dan mengembalikannya ke kondisi awal. Tindakan ini tidak dapat dibatalkan.') }}
                    </div>
                    <div class="form-group">
                        <label for="reset_confirmation">{{ __('Ketik "HAPUS" untuk konfirmasi:') }}</label>
                        <input type="text" name="confirmation" id="reset_confirmation" class="form-control" required>
                    </div>
                    <div class="form-group">
                        <div class="custom-control custom-checkbox">
                            <input type="checkbox" class="custom-control-input" id="reset_accept_risks" name="accept_risks" required>
                            <label class="custom-control-label" for="reset_accept_risks">
                                {{ __('Saya memahami risiko dan menerima tanggung jawab penuh atas tindakan ini') }}
                            </label>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">{{ __('Batal') }}</button>
                    <button type="submit" class="btn btn-danger" id="reset_database_button" disabled>
                        <i class="fas fa-trash-alt mr-1"></i> {{ __('Reset Database') }}
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Delete Candidates Modal -->
<div class="modal fade" id="deleteCandidatesModal" tabindex="-1" role="dialog" aria-labelledby="deleteCandidatesModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header bg-warning">
                <h5 class="modal-title" id="deleteCandidatesModalLabel">{{ __('Hapus Semua Pencaker') }}</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <form action="{{ route('settings.database-tools.delete-candidates') }}" method="POST">
                @csrf
                <div class="modal-body">
                    <div class="alert alert-warning">
                        <i class="fas fa-exclamation-triangle mr-1"></i>
                        <strong>{{ __('Peringatan!') }}</strong> {{ __('Tindakan ini akan menghapus SEMUA data pencaker termasuk profil, CV, lamaran, dan data terkait lainnya. Admin tidak akan terhapus. Tindakan ini tidak dapat dibatalkan.') }}
                    </div>

                    <!-- Langkah 1: Konfirmasi teks -->
                    <div class="form-group">
                        <label for="candidates_confirmation_1">{{ __('Langkah 1: Ketik "HAPUS PENCAKER" untuk konfirmasi:') }}</label>
                        <input type="text" id="candidates_confirmation_1" class="form-control" required>
                    </div>

                    <!-- Langkah 2: Checkbox persetujuan -->
                    <div class="form-group">
                        <div class="custom-control custom-checkbox">
                            <input type="checkbox" class="custom-control-input" id="candidates_accept_risks" required>
                            <label class="custom-control-label" for="candidates_accept_risks">
                                {{ __('Langkah 2: Saya memahami risiko dan menerima tanggung jawab penuh atas tindakan ini') }}
                            </label>
                        </div>
                    </div>

                    <!-- Langkah 3: Konfirmasi password admin -->
                    <div class="form-group">
                        <label for="candidates_admin_password">{{ __('Langkah 3: Masukkan password admin untuk konfirmasi akhir:') }}</label>
                        <input type="password" name="admin_password" id="candidates_admin_password" class="form-control" required>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">{{ __('Batal') }}</button>
                    <button type="submit" class="btn btn-warning" id="delete_candidates_button" disabled>
                        <i class="fas fa-user-times mr-1"></i> {{ __('Hapus Semua Pencaker') }}
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Delete Companies Modal -->
<div class="modal fade" id="deleteCompaniesModal" tabindex="-1" role="dialog" aria-labelledby="deleteCompaniesModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header bg-warning">
                <h5 class="modal-title" id="deleteCompaniesModalLabel">{{ __('Hapus Semua Perusahaan') }}</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <form action="{{ route('settings.database-tools.delete-companies') }}" method="POST">
                @csrf
                <div class="modal-body">
                    <div class="alert alert-warning">
                        <i class="fas fa-exclamation-triangle mr-1"></i>
                        <strong>{{ __('Peringatan!') }}</strong> {{ __('Tindakan ini akan menghapus SEMUA data perusahaan termasuk profil, lowongan kerja, dan data terkait lainnya. Admin tidak akan terhapus. Tindakan ini tidak dapat dibatalkan.') }}
                    </div>

                    <!-- Langkah 1: Konfirmasi teks -->
                    <div class="form-group">
                        <label for="companies_confirmation_1">{{ __('Langkah 1: Ketik "HAPUS PERUSAHAAN" untuk konfirmasi:') }}</label>
                        <input type="text" id="companies_confirmation_1" class="form-control" required>
                    </div>

                    <!-- Langkah 2: Checkbox persetujuan -->
                    <div class="form-group">
                        <div class="custom-control custom-checkbox">
                            <input type="checkbox" class="custom-control-input" id="companies_accept_risks" required>
                            <label class="custom-control-label" for="companies_accept_risks">
                                {{ __('Langkah 2: Saya memahami risiko dan menerima tanggung jawab penuh atas tindakan ini') }}
                            </label>
                        </div>
                    </div>

                    <!-- Langkah 3: Konfirmasi password admin -->
                    <div class="form-group">
                        <label for="companies_admin_password">{{ __('Langkah 3: Masukkan password admin untuk konfirmasi akhir:') }}</label>
                        <input type="password" name="admin_password" id="companies_admin_password" class="form-control" required>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">{{ __('Batal') }}</button>
                    <button type="submit" class="btn btn-warning" id="delete_companies_button" disabled>
                        <i class="fas fa-building mr-1"></i> {{ __('Hapus Semua Perusahaan') }}
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Restore Upload Modal -->
<div class="modal fade" id="restoreUploadModal" tabindex="-1" role="dialog" aria-labelledby="restoreUploadModalLabel" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header bg-warning">
                <h5 class="modal-title" id="restoreUploadModalLabel">{{ __('Restore dari File') }}</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <div class="alert alert-warning">
                    <i class="fas fa-exclamation-triangle mr-1"></i>
                    <strong>{{ __('Peringatan!') }}</strong> {{ __('Restore database akan menimpa database yang ada saat ini. Tindakan ini tidak dapat dibatalkan.') }}
                </div>
                <div class="form-group">
                    <label for="restore_upload_confirmation">{{ __('Ketik "RESTORE" untuk konfirmasi:') }}</label>
                    <input type="text" id="restore_upload_confirmation" class="form-control" required>
                </div>
                <div class="form-group">
                    <div class="custom-control custom-checkbox">
                        <input type="checkbox" class="custom-control-input" id="restore_upload_accept_risks" required>
                        <label class="custom-control-label" for="restore_upload_accept_risks">
                            {{ __('Saya memahami risiko dan menerima tanggung jawab penuh atas tindakan ini') }}
                        </label>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">{{ __('Batal') }}</button>
                <button type="button" class="btn btn-warning" id="restore_upload_button" disabled onclick="submitRestoreUpload()">
                    <i class="fas fa-undo mr-1"></i> {{ __('Restore') }}
                </button>
            </div>
        </div>
    </div>
</div>
@endsection

@section('script')
<script>
    $(document).ready(function() {
        // Initialize tooltips
        $('[data-toggle="tooltip"]').tooltip();

        // Initialize custom file input
        $('.custom-file-input').on('change', function() {
            let fileName = $(this).val().split('\\').pop();
            $(this).next('.custom-file-label').addClass("selected").html(fileName);
        });

        // Timer untuk tombol reset database
        let resetTimer = 10;
        const resetButton = $('#reset_database_button');
        const resetConfirmation = $('#reset_confirmation');
        const resetAcceptRisks = $('#reset_accept_risks');

        resetButton.html(`<i class="fas fa-trash-alt mr-1"></i> {{ __('Reset Database') }} (${resetTimer}d)`);

        const interval = setInterval(function() {
            resetTimer--;
            resetButton.html(`<i class="fas fa-trash-alt mr-1"></i> {{ __('Reset Database') }} (${resetTimer}d)`);

            if (resetTimer <= 0) {
                clearInterval(interval);
                resetButton.html(`<i class="fas fa-trash-alt mr-1"></i> {{ __('Reset Database') }}`);

                // Aktifkan tombol hanya jika konfirmasi benar dan checkbox dicentang
                checkResetButtonState();
            }
        }, 1000);

        // Periksa status tombol reset saat input berubah
        resetConfirmation.on('input', checkResetButtonState);
        resetAcceptRisks.on('change', checkResetButtonState);

        function checkResetButtonState() {
            if (resetTimer <= 0 && resetConfirmation.val() === 'HAPUS' && resetAcceptRisks.is(':checked')) {
                resetButton.prop('disabled', false);
            } else {
                resetButton.prop('disabled', true);
            }
        }

        // Timer untuk tombol restore upload
        let restoreUploadTimer = 10;
        const restoreUploadButton = $('#restore_upload_button');
        const restoreUploadConfirmation = $('#restore_upload_confirmation');
        const restoreUploadAcceptRisks = $('#restore_upload_accept_risks');

        restoreUploadButton.html(`<i class="fas fa-undo mr-1"></i> {{ __('Restore') }} (${restoreUploadTimer}d)`);

        const restoreUploadInterval = setInterval(function() {
            restoreUploadTimer--;
            restoreUploadButton.html(`<i class="fas fa-undo mr-1"></i> {{ __('Restore') }} (${restoreUploadTimer}d)`);

            if (restoreUploadTimer <= 0) {
                clearInterval(restoreUploadInterval);
                restoreUploadButton.html(`<i class="fas fa-undo mr-1"></i> {{ __('Restore') }}`);

                // Aktifkan tombol hanya jika konfirmasi benar dan checkbox dicentang
                checkRestoreUploadButtonState();
            }
        }, 1000);

        // Periksa status tombol restore upload saat input berubah
        restoreUploadConfirmation.on('input', checkRestoreUploadButtonState);
        restoreUploadAcceptRisks.on('change', checkRestoreUploadButtonState);

        function checkRestoreUploadButtonState() {
            if (restoreUploadTimer <= 0 && restoreUploadConfirmation.val() === 'RESTORE' && restoreUploadAcceptRisks.is(':checked')) {
                restoreUploadButton.prop('disabled', false);
            } else {
                restoreUploadButton.prop('disabled', true);
            }
        }

        // Validasi untuk hapus pencaker
        const candidatesConfirmation = $('#candidates_confirmation_1');
        const candidatesAcceptRisks = $('#candidates_accept_risks');
        const candidatesAdminPassword = $('#candidates_admin_password');
        const deleteCandidatesButton = $('#delete_candidates_button');

        candidatesConfirmation.on('input', checkDeleteCandidatesButtonState);
        candidatesAcceptRisks.on('change', checkDeleteCandidatesButtonState);
        candidatesAdminPassword.on('input', checkDeleteCandidatesButtonState);

        function checkDeleteCandidatesButtonState() {
            if (candidatesConfirmation.val() === 'HAPUS PENCAKER' &&
                candidatesAcceptRisks.is(':checked') &&
                candidatesAdminPassword.val().length > 0) {
                deleteCandidatesButton.prop('disabled', false);
            } else {
                deleteCandidatesButton.prop('disabled', true);
            }
        }

        // Validasi untuk hapus perusahaan
        const companiesConfirmation = $('#companies_confirmation_1');
        const companiesAcceptRisks = $('#companies_accept_risks');
        const companiesAdminPassword = $('#companies_admin_password');
        const deleteCompaniesButton = $('#delete_companies_button');

        companiesConfirmation.on('input', checkDeleteCompaniesButtonState);
        companiesAcceptRisks.on('change', checkDeleteCompaniesButtonState);
        companiesAdminPassword.on('input', checkDeleteCompaniesButtonState);

        function checkDeleteCompaniesButtonState() {
            if (companiesConfirmation.val() === 'HAPUS PERUSAHAAN' &&
                companiesAcceptRisks.is(':checked') &&
                companiesAdminPassword.val().length > 0) {
                deleteCompaniesButton.prop('disabled', false);
            } else {
                deleteCompaniesButton.prop('disabled', true);
            }
        }

        // Reset form saat modal ditutup
        $('#deleteCandidatesModal').on('hidden.bs.modal', function() {
            candidatesConfirmation.val('');
            candidatesAcceptRisks.prop('checked', false);
            candidatesAdminPassword.val('');
            deleteCandidatesButton.prop('disabled', true);
        });

        $('#deleteCompaniesModal').on('hidden.bs.modal', function() {
            companiesConfirmation.val('');
            companiesAcceptRisks.prop('checked', false);
            companiesAdminPassword.val('');
            deleteCompaniesButton.prop('disabled', true);
        });
    });

    // Confirm restore from existing backup
    function confirmRestore(modalId) {
        $('#' + modalId).modal('show');
    }

    // Confirm delete backup
    function confirmDelete(modalId) {
        $('#' + modalId).modal('show');
    }

    // Confirm restore from uploaded file
    function confirmRestoreUpload() {
        const fileInput = document.getElementById('backup_file');
        if (fileInput.files.length === 0) {
            alert('{{ __("Silakan pilih file backup terlebih dahulu") }}');
            return;
        }

        $('#restoreUploadModal').modal('show');
    }

    // Submit restore from uploaded file
    function submitRestoreUpload() {
        document.querySelector('form[action="{{ route("settings.database-tools.backup.restore-upload") }}"]').submit();
    }
</script>
@endsection
