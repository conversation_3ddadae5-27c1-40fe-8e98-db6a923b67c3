@extends('backend.settings.setting-layout')
@section('title')
    {{ __('<PERSON>ail Logs') }}
@endsection
@section('breadcrumbs')
    <div class="row mb-2 mt-4">
        <div class="col-sm-6">
            <h1 class="m-0">{{ __('Email Logs') }}</h1>
        </div>
        <div class="col-sm-6">
            <ol class="breadcrumb float-sm-right">
                <li class="breadcrumb-item"><a href="{{ route('admin.dashboard') }}">{{ __('home') }}</a></li>
                <li class="breadcrumb-item">{{ __('settings') }}</li>
                <li class="breadcrumb-item active">{{ __('Email Logs') }}</li>
            </ol>
        </div>
    </div>
@endsection
@section('website-settings')
    <div class="card">
        <div class="card-header">
            <h3 class="card-title line-height-36">{{ __('Email Logs') }}</h3>
        </div>
        <div class="card-body table-responsive p-0">
            <table class="table table-hover text-nowrap table-bordered">
                <thead>
                    <tr>
                        <th width="5%">#</th>
                        <th>{{ __('From') }}</th>
                        <th>{{ __('To') }}</th>
                        <th>{{ __('Subject') }}</th>
                        <th>{{ __('Status') }}</th>
                        <th>{{ __('Sent At') }}</th>
                        <th width="10%">{{ __('Action') }}</th>
                    </tr>
                </thead>
                <tbody>
                    @forelse ($emailLogs as $key => $log)
                        <tr>
                            <td>{{ $emailLogs->firstItem() + $key }}</td>
                            <td>{{ $log->from }}</td>
                            <td>{{ $log->to }}</td>
                            <td>{{ $log->subject }}</td>
                            <td>
                                @if ($log->status == 'sent')
                                    <span class="badge bg-success">{{ __('Sent') }}</span>
                                @else
                                    <span class="badge bg-danger">{{ __('Failed') }}</span>
                                @endif
                            </td>
                            <td>{{ $log->sent_at ? $log->sent_at->format('d M Y H:i:s') : 'N/A' }}</td>
                            <td>
                                <button type="button" class="btn btn-sm btn-info" data-toggle="modal"
                                    data-target="#emailLogModal{{ $log->id }}">
                                    <i class="fas fa-eye"></i>
                                </button>
                            </td>
                        </tr>

                        <!-- Email Log Modal -->
                        <div class="modal fade" id="emailLogModal{{ $log->id }}" tabindex="-1" role="dialog"
                            aria-labelledby="emailLogModalLabel" aria-hidden="true">
                            <div class="modal-dialog modal-lg" role="document">
                                <div class="modal-content">
                                    <div class="modal-header">
                                        <h5 class="modal-title" id="emailLogModalLabel">{{ __('Email Details') }}</h5>
                                        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                                            <span aria-hidden="true">&times;</span>
                                        </button>
                                    </div>
                                    <div class="modal-body">
                                        <div class="row mb-3">
                                            <div class="col-md-3">
                                                <strong>{{ __('From') }}:</strong>
                                            </div>
                                            <div class="col-md-9">
                                                {{ $log->from }}
                                            </div>
                                        </div>
                                        <div class="row mb-3">
                                            <div class="col-md-3">
                                                <strong>{{ __('To') }}:</strong>
                                            </div>
                                            <div class="col-md-9">
                                                {{ $log->to }}
                                            </div>
                                        </div>
                                        <div class="row mb-3">
                                            <div class="col-md-3">
                                                <strong>{{ __('Subject') }}:</strong>
                                            </div>
                                            <div class="col-md-9">
                                                {{ $log->subject }}
                                            </div>
                                        </div>
                                        <div class="row mb-3">
                                            <div class="col-md-3">
                                                <strong>{{ __('Status') }}:</strong>
                                            </div>
                                            <div class="col-md-9">
                                                @if ($log->status == 'sent')
                                                    <span class="badge bg-success">{{ __('Sent') }}</span>
                                                @else
                                                    <span class="badge bg-danger">{{ __('Failed') }}</span>
                                                @endif
                                            </div>
                                        </div>
                                        @if ($log->status == 'failed' && $log->error_message)
                                            <div class="row mb-3">
                                                <div class="col-md-3">
                                                    <strong>{{ __('Error Message') }}:</strong>
                                                </div>
                                                <div class="col-md-9">
                                                    <div class="alert alert-danger">
                                                        {{ $log->error_message }}
                                                    </div>
                                                </div>
                                            </div>
                                        @endif
                                        <div class="row mb-3">
                                            <div class="col-md-3">
                                                <strong>{{ __('Sent At') }}:</strong>
                                            </div>
                                            <div class="col-md-9">
                                                {{ $log->sent_at ? $log->sent_at->format('d M Y H:i:s') : 'N/A' }}
                                            </div>
                                        </div>
                                        <div class="row">
                                            <div class="col-md-3">
                                                <strong>{{ __('Body') }}:</strong>
                                            </div>
                                            <div class="col-md-9">
                                                <div class="border p-3 bg-light">
                                                    {!! $log->body !!}
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="modal-footer">
                                        <button type="button" class="btn btn-secondary"
                                            data-dismiss="modal">{{ __('Close') }}</button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    @empty
                        <tr>
                            <td colspan="7" class="text-center">{{ __('No email logs found') }}</td>
                        </tr>
                    @endforelse
                </tbody>
            </table>
        </div>
        <div class="card-footer">
            <div class="d-flex justify-content-center">
                {{ $emailLogs->links() }}
            </div>
        </div>
    </div>
@endsection
