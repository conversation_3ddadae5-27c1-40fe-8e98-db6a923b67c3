@extends('backend.layouts.app')
@section('title')
    {{ __('Import Kelurahan') }}
@endsection

@section('content')
    <div class="container-fluid">
        <div class="row">
            <div class="col-md-12">
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">{{ __('Import Data Kelurahan') }}</h3>
                        <div class="card-tools">
                            <a href="{{ route('location.kelurahan.index') }}" class="btn btn-primary">
                                <i class="fas fa-arrow-left"></i> {{ __('Kembali') }}
                            </a>
                        </div>
                    </div>
                    <div class="card-body">
                        <div class="alert alert-info">
                            <h5><i class="icon fas fa-info"></i> {{ __('Informasi Import') }}</h5>
                            <p>{{ __('Fitur ini akan mengimpor data kelurahan dari API Wilayah Indonesia berdasarkan data kecamatan yang sudah ada di database.') }}</p>
                            <ul>
                                <li>{{ __('Data akan diambil dari: https://tupski.github.io/api-wilayah-indonesia/') }}</li>
                                <li>{{ __('Import akan mencocokkan nama kecamatan yang ada di database dengan data API') }}</li>
                                <li>{{ __('Kelurahan yang sudah ada akan dilewati kecuali jika dipaksa update') }}</li>
                                <li>{{ __('Proses import mungkin membutuhkan waktu beberapa menit') }}</li>
                            </ul>
                        </div>

                        <form action="{{ route('location.kelurahan.import.data') }}" method="POST">
                            @csrf

                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="import_type">{{ __('Tipe Import') }}</label>
                                        <select name="import_type" id="import_type" class="form-control @error('import_type') is-invalid @enderror" required>
                                            <option value="">{{ __('Pilih Tipe Import') }}</option>
                                            <option value="by_kecamatan" {{ old('import_type') == 'by_kecamatan' ? 'selected' : '' }}>
                                                {{ __('Berdasarkan Kecamatan yang Ada') }}
                                            </option>
                                        </select>
                                        @error('import_type')
                                            <span class="invalid-feedback">{{ $message }}</span>
                                        @enderror
                                        <small class="form-text text-muted">
                                            {{ __('Import berdasarkan kecamatan yang sudah ada di database') }}
                                        </small>
                                    </div>
                                </div>

                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="kecamatan_filter">{{ __('Filter Kecamatan (Opsional)') }}</label>
                                        <input type="text" name="kecamatan_filter" id="kecamatan_filter"
                                               class="form-control @error('kecamatan_filter') is-invalid @enderror"
                                               value="{{ old('kecamatan_filter') }}"
                                               placeholder="{{ __('Contoh: Serpong, Ciputat, dll') }}">
                                        @error('kecamatan_filter')
                                            <span class="invalid-feedback">{{ $message }}</span>
                                        @enderror
                                        <small class="form-text text-muted">
                                            {{ __('Kosongkan untuk import semua kecamatan, atau isi untuk filter kecamatan tertentu') }}
                                        </small>
                                    </div>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <div class="custom-control custom-checkbox">
                                            <input type="checkbox" class="custom-control-input" id="force_update" name="force_update" value="1" {{ old('force_update') ? 'checked' : '' }}>
                                            <label class="custom-control-label" for="force_update">
                                                {{ __('Paksa Update Data yang Sudah Ada') }}
                                            </label>
                                        </div>
                                        <small class="form-text text-muted">
                                            {{ __('Centang jika ingin memperbarui kelurahan yang sudah ada di database') }}
                                        </small>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <div class="custom-control custom-checkbox">
                                            <input type="checkbox" class="custom-control-input" id="fresh_import" name="fresh_import" value="1" {{ old('fresh_import') ? 'checked' : '' }}>
                                            <label class="custom-control-label" for="fresh_import">
                                                <span class="text-danger">{{ __('Import Fresh (Hapus Semua Data Lama)') }}</span>
                                            </label>
                                        </div>
                                        <small class="form-text text-muted text-danger">
                                            {{ __('⚠️ HATI-HATI: Akan menghapus SEMUA kelurahan yang ada sebelum import data baru') }}
                                        </small>
                                    </div>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-12">
                                    <div class="alert alert-warning">
                                        <h6><i class="icon fas fa-exclamation-triangle"></i> {{ __('Peringatan') }}</h6>
                                        <p>{{ __('Proses import akan membutuhkan waktu dan koneksi internet yang stabil. Pastikan:') }}</p>
                                        <ul class="mb-0">
                                            <li>{{ __('Koneksi internet stabil') }}</li>
                                            <li>{{ __('Jangan menutup halaman selama proses import') }}</li>
                                            <li>{{ __('Data kecamatan sudah ada di database') }}</li>
                                        </ul>
                                    </div>
                                </div>
                            </div>

                            <div class="form-group">
                                <button type="submit" class="btn btn-success btn-lg">
                                    <i class="fas fa-download"></i> {{ __('Mulai Import Kelurahan') }}
                                </button>
                                <a href="{{ route('location.kelurahan.index') }}" class="btn btn-secondary btn-lg">
                                    <i class="fas fa-times"></i> {{ __('Batal') }}
                                </a>
                            </div>
                        </form>

                        <hr>

                        <div class="row">
                            <div class="col-md-12">
                                <h5>{{ __('Statistik Kecamatan') }}</h5>
                                <div class="table-responsive">
                                    <table class="table table-sm table-bordered">
                                        <thead>
                                            <tr>
                                                <th>{{ __('Provinsi') }}</th>
                                                <th>{{ __('Kota') }}</th>
                                                <th>{{ __('Kecamatan') }}</th>
                                                <th>{{ __('Jumlah Kelurahan') }}</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            @forelse($kecamatans as $kecamatan)
                                                <tr>
                                                    <td>{{ $kecamatan->city->state->name ?? '-' }}</td>
                                                    <td>{{ $kecamatan->city->name ?? '-' }}</td>
                                                    <td>{{ $kecamatan->name }}</td>
                                                    <td>
                                                        <span class="badge badge-info">
                                                            {{ $kecamatan->kelurahans_count ?? 0 }}
                                                        </span>
                                                    </td>
                                                </tr>
                                            @empty
                                                <tr>
                                                    <td colspan="4" class="text-center">{{ __('Tidak ada data kecamatan') }}</td>
                                                </tr>
                                            @endforelse
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection

@section('script')
<script>
    $(document).ready(function() {
        // Show loading when form is submitted
        $('form').on('submit', function(e) {
            // Check if fresh import is selected
            if ($('#fresh_import').is(':checked')) {
                if (!confirm('⚠️ PERINGATAN: Anda akan menghapus SEMUA data kelurahan yang ada!\n\nApakah Anda yakin ingin melanjutkan? Tindakan ini tidak dapat dibatalkan.')) {
                    e.preventDefault();
                    return false;
                }
            }

            $(this).find('button[type="submit"]').prop('disabled', true).html('<i class="fas fa-spinner fa-spin"></i> {{ __("Sedang Import...") }}');
        });

        // Warning when fresh import is checked
        $('#fresh_import').on('change', function() {
            if ($(this).is(':checked')) {
                alert('⚠️ PERINGATAN: Mode Fresh Import akan menghapus SEMUA data kelurahan yang ada sebelum import data baru!\n\nPastikan Anda sudah backup data jika diperlukan.');
            }
        });
    });
</script>
@endsection
