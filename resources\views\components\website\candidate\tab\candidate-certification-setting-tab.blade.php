@props(['certifications'])
<div class="tw-flex rt-mb-32 lg:tw-mt-0 tw-items-center tw-justify-between">
    <h3 class="f-size-18 lh-1 m-0">{{ __('Sertifikasi') }}</h3>
    <button id="addCertification" type="button" class="btn btn-primary ">
        {{ __('Tambah Sertifikasi') }}
    </button>
</div>
<div class="db-job-card-table -tw-mx-2">
    <table class="tw-px-2">
        <thead>
            <tr>
                <th class="!tw-text-base !tw-font-medium">{{ __('Nama Sertifikat') }}</th>
                <th class="!tw-text-base !tw-font-medium">{{ __('Organisasi Penerbit') }}</th>
                <th class="!tw-text-base !tw-font-medium">{{ __('Tanggal Terbit') }}</th>
                <th class="!tw-text-base !tw-font-medium">{{ __('Tanggal Kadaluarsa') }}</th>
                <th class="!tw-text-base !tw-font-medium tw-text-right">{{ __('action') }}</th>
            </tr>
        </thead>
        <tbody>
            @forelse ($certifications as $certification)
                <tr>
                    <td>{{ $certification->name }}</td>
                    <td>{{ $certification->organization }}</td>
                    <td>{{ $certification->formatted_issue_date }}</td>
                    <td>{{ $certification->expiration_date ? $certification->formatted_expiration_date : 'Tidak Kadaluarsa' }}</td>
                    <td>
                        <div class="d-flex justify-content-end">
                            <button type="button" class="btn btn-icon" id="dropdownMenuButton6"
                                data-bs-toggle="dropdown" aria-expanded="false">
                                <svg width="24" height="24" viewBox="0 0 24 24" fill="none"
                                    xmlns="http://www.w3.org/2000/svg">
                                    <path
                                        d="M12 13.125C12.6213 13.125 13.125 12.6213 13.125 12C13.125 11.3787 12.6213 10.875 12 10.875C11.3787 10.875 10.875 11.3787 10.875 12C10.875 12.6213 11.3787 13.125 12 13.125Z"
                                        fill="#767F8C" stroke="#767F8C" />
                                    <path
                                        d="M12 6.65039C12.6213 6.65039 13.125 6.14671 13.125 5.52539C13.125 4.90407 12.6213 4.40039 12 4.40039C11.3787 4.40039 10.875 4.90407 10.875 5.52539C10.875 6.14671 11.3787 6.65039 12 6.65039Z"
                                        fill="#767F8C" stroke="#767F8C" />
                                    <path
                                        d="M12 19.6094C12.6213 19.6094 13.125 19.1057 13.125 18.4844C13.125 17.8631 12.6213 17.3594 12 17.3594C11.3787 17.3594 10.875 17.8631 10.875 18.4844C10.875 19.1057 11.3787 19.6094 12 19.6094Z"
                                        fill="#767F8C" stroke="#767F8C" />
                                </svg>
                            </button>
                            <ul class="dropdown-menu dropdown-menu-end company-dashboard-dropdown"
                                aria-labelledby="dropdownMenuButton6">
                                <li>
                                    <a href="javascript:void(0)" class="dropdown-item"
                                        onclick="certificationDetail({{ json_encode($certification) }})">
                                        <x-svg.edit-icon />
                                        {{ __('Edit') }}
                                    </a>
                                </li>
                                <li>
                                    <form method="POST"
                                        action="{{ route('candidate.certifications.destroy', $certification->id) }}">
                                        @csrf
                                        @method('Delete')
                                        <button type="submit" class="dropdown-item"
                                            onclick="return confirm('{{ __('Anda yakin ingin menghapus sertifikasi ini?') }}');">
                                            <x-svg.trash-icon />
                                            {{ __('delete') }}
                                        </button>
                                    </form>
                                </li>
                            </ul>
                        </div>
                    </td>
                </tr>
            @empty
                <tr>
                    <td colspan="5" class="text-center">
                        <x-svg.not-found-icon />
                        <p class="mt-4">{{ __('no_data_found') }}</p>
                    </td>
                </tr>
            @endforelse
        </tbody>
    </table>
</div>

@push('frontend_links')
    <style>
        #addCertificationModal .modal-dialog,
        #editCertificationModal .modal-dialog {
            z-index: 999999 !important;
            max-width: 950px !important;
            padding: 20px;
        }
    </style>
@endpush

@push('frontend_scripts')
    <script>
        $('#addCertification').on('click', function() {
            $('#addCertificationModal').modal('show');
        });

        function closeAddCertificationModal() {
            $('#addCertificationModal').find('form')[0].reset();
            $('#addCertificationModal').modal('hide')
        }

        function closeEditCertificationModal() {
            $('#editCertificationModal').find('form')[0].reset();
            $('#editCertificationModal').modal('hide')
        }

        function certificationDetail(certification) {
            $('#certification-modal-id').val(certification.id);
            $('#certification-modal-name').val(certification.name);
            $('#certification-modal-organization').val(certification.organization);
            $('#certification-modal-issue-date').val(certification.issue_date);
            $('#certification-modal-expiration-date').val(certification.expiration_date);
            $('#certification-modal-credential-id').val(certification.credential_id);
            $('#certification-modal-credential-url').val(certification.credential_url);

            $('#editCertificationModal').modal('show');
        }
    </script>
@endpush
