@props(['user', 'organizationTypes' => [], 'industryTypes' => [], 'teamSizes' => []])

<form action="{{ route('company.profile.complete', auth()->user()->id) }}" method="post">
    @method('PUT')
    @csrf
    <input type="hidden" name="field" value="contact">
    <input type="hidden" name="lat" id="lat_input_progress" value="{{ $user->company->lat }}">
    <input type="hidden" name="long" id="long_input_progress" value="{{ $user->company->long }}">
    <fieldset>
        <div class="form-card mb-4">
            <div class="dashboard-account-setting-item pb-0">
                @if (config('templatecookie.map_show'))
                    <h6>{{ __('company_location') }}
                        <span class="text-danger">*</span>
                        <small class="h6">
                            ({{ __('click_to_add_a_pointer') }})
                        </small>
                    </h6>
                    <div class="row">
                        <x-website.map.map-warning />
                        @php
                            $map = $setting->default_map;
                        @endphp
                        {{-- <div class="{{ $map == 'leaflet' ? '' : 'd-none' }}">
                            <input type="text" autocomplete="off" id="leaflet_search"
                                placeholder="{{ __('enter_city_name') }}" class="form-control"
                                value="{{ $user->company->exact_location ? $user->company->exact_location : $user->company->full_address }}" />
                            <br>
                            <div id="leaflet-map"></div>
                        </div>
                        <div id="google-map-div" class="{{ $map == 'google-map' ? '' : 'd-none' }}">
                            <input id="searchInput" class="mapClass" type="text" placeholder="Enter a location">
                            <div class="map mymap" id="google-map"></div>
                        </div>
                        @error('location')
                            <span class="ml-3 text-md text-danger">{{ $message }}</span>
                        @enderror --}}
                    </div>
                    @php
                        $session_location = session()->get('location');
                        $session_country = $session_location && array_key_exists('country', $session_location) ? $session_location['country'] : '-';
                        $session_exact_location = $session_location && array_key_exists('exact_location', $session_location) ? $session_location['exact_location'] : '-';

                        $company_country = $user->company->country;
                        $company_exact_location = $user->company->exact_location;
                    @endphp
                    <div class="card-footer row mt-4 border-0">
                        <span>
                            <img src="{{ asset('frontend/assets/images/loader.gif') }}" alt="loading" width="50px"
                                height="50px" class="loader_position d-none">
                        </span>
                        <div class="location_section">
                            {{ __('country') }}: <span
                                class="location_country">{{ $company_country ?: $session_country }}</span> <br>
                            {{ __('full_address') }}: <span
                                class="location_full_address">{{ $company_exact_location ?: $session_exact_location }}</span>
                        </div>
                    </div>
                @else
                    <x-forms.label name="Domisili Perusahaan" :required="true" class="tw-text-sm tw-mb-2" />
                    <div class="card-body pt-0 row">
                        <div class="col-12">
                            @livewire('country-state-city', [
                                'selectedStateId' => $user->provinsi,
                                'selectedCityId' => $user->kabupaten_kota,
                                'selectedKecamatanId' => $user->kecamatan,
                                'selectedKelurahanId' => $user->kelurahan
                            ])
                            @error('location')
                                <span class="ml-3 text-md text-danger">{{ $message }}</span>
                            @enderror
                        </div>

                        {{-- Alamat Perusahaan dari Registrasi --}}
                        <div class="col-12 mt-3">
                            <label class="body-font-4 d-block text-gray-900 rt-mb-8">
                                Alamat Perusahaan (dari registrasi)
                            </label>
                            <textarea class="form-control" rows="3" readonly disabled>{{ $user->alamat_ktp }}</textarea>
                            <small class="text-muted">Alamat ini diambil dari data registrasi dan tidak dapat diubah di sini</small>
                        </div>
                    </div>
                @endif
            </div>
            <div class="dashboard-account-setting-item">
                <h6>{{ __('phone_email') }}</h6>
                <div class="row">
                    <div class="col-lg-6 mb-3">
                        <label class="pointer body-font-4 d-block text-gray-900 rt-mb-8">
                            {{ __('Nomor Telepon Perusahaan') }}
                            <x-forms.required />
                        </label>
                        <input class="phonecode @error('phone') is-invalid border-danger @enderror" name="phone"
                            type="text" value="{{ old('phone', $user->contactInfo->phone) }}"
                            placeholder="{{ __('phone') }}" id="phone_input" />
                        @error('phone')
                            <span class="invalid-feedback" role="alert">
                                <strong>{{ __($message) }}</strong>
                            </span>
                        @enderror
                    </div>
                    <div class="col-lg-6 mb-3">
                        <label class="pointer body-font-4 d-block text-gray-900 rt-mb-8">
                            {{ __('Email Perusahaan') }}
                            <x-forms.required />
                        </label>
                        <div class="fromGroup has-icon2">
                            <div class="form-control-icon">
                                <input class="form-control @error('email') is-invalid @enderror" name="email"
                                    type="text" placeholder="{{ __('email_address') }}"
                                    value="{{ old('email', $user->contactInfo->email) }}">
                                <div class="icon-badge-2">
                                    <x-svg.envelope-icon width="24" height="24" />
                                </div>
                            </div>
                        </div>
                        @error('email')
                            <span class="invalid-feedback  d-block" role="alert">
                                <strong>{{ __($message) }}</strong>
                            </span>
                        @enderror
                    </div>
                    <small class="text-danger font-italic">Informasi ini akan ditampilkan ke profil publik perusahaan Anda</small>
                </div>

            </div>
        </div>
        <a href="{{ url('company/account-progress?social') }}">
            <button type="button" class="btn previous bg-gray-50 rt-mr-8">
                {{ __('previous') }}
            </button>
        </a>
        <button type="button" class="btn next btn-primary hide-menu-btn" id="final-submit-btn">
            <span class="button-content-wrapper ">
                <span class="button-icon align-icon-right">
                    <i class="ph-arrow-right"></i>
                </span>
                <span class="button-text">
                    {{ __('save_next') }}
                </span>
            </span>
        </button>
    </fieldset>
</form>

<!-- Modal Konfirmasi Data Perusahaan -->
<div class="modal fade" id="companyDataConfirmModal" tabindex="-1" aria-labelledby="companyDataConfirmModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="companyDataConfirmModalLabel">
                    <i class="fas fa-check-circle text-success"></i> Konfirmasi Data Perusahaan
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="alert alert-info">
                    <i class="fas fa-info-circle"></i>
                    Silakan periksa kembali data perusahaan Anda sebelum menyelesaikan proses pendaftaran.
                </div>

                <div class="row">
                    <div class="col-md-6">
                        <h6 class="fw-bold mb-3">Informasi Perusahaan</h6>
                        <div class="mb-2">
                            <strong>Nama Perusahaan:</strong>
                            <span id="confirm-company-name">{{ $user->name }}</span>
                        </div>
                        <div class="mb-2">
                            <strong>Badan Hukum:</strong>
                            <span id="confirm-organization-type">
                                @foreach($organizationTypes ?? [] as $type)
                                    @if($type->id == $user->company->organization_type_id)
                                        {{ $type->name }}
                                    @endif
                                @endforeach
                            </span>
                        </div>
                        <div class="mb-2">
                            <strong>Jenis Industri:</strong>
                            <span id="confirm-industry-type">
                                @foreach($industryTypes ?? [] as $type)
                                    @if($type->id == $user->company->industry_type_id)
                                        {{ $type->name }}
                                    @endif
                                @endforeach
                            </span>
                        </div>
                        <div class="mb-2">
                            <strong>Jumlah Karyawan:</strong>
                            <span id="confirm-team-size">
                                @foreach($teamSizes ?? [] as $size)
                                    @if($size->id == $user->company->team_size_id)
                                        {{ $size->name }}
                                    @endif
                                @endforeach
                            </span>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <h6 class="fw-bold mb-3">Kontak & Lokasi</h6>
                        <div class="mb-2">
                            <strong>Email:</strong>
                            <span id="confirm-email">{{ $user->contactInfo->email ?? $user->email }}</span>
                        </div>
                        <div class="mb-2">
                            <strong>Telepon:</strong>
                            <span id="confirm-phone">{{ $user->contactInfo->phone ?? '-' }}</span>
                        </div>
                        <div class="mb-2">
                            <strong>Website:</strong>
                            <span id="confirm-website">{{ $user->company->website ?? '-' }}</span>
                        </div>
                        <div class="mb-2">
                            <strong>Lokasi:</strong>
                            <span id="confirm-location">
                                {{ $user->company->district ?? 'Belum diset' }}, {{ $user->company->region ?? '' }}
                            </span>
                        </div>
                    </div>
                </div>

                <div class="alert alert-warning mt-3">
                    <i class="fas fa-exclamation-triangle"></i>
                    <strong>Perhatian:</strong> Beberapa data seperti Badan Hukum tidak dapat diubah setelah proses ini selesai.
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                    <i class="fas fa-edit"></i> Periksa Lagi
                </button>
                <button type="button" class="btn btn-success" id="confirm-final-submit">
                    <i class="fas fa-check"></i> Ya, Data Sudah Benar
                </button>
            </div>
        </div>
    </div>
</div>

@push('frontend_scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    const phoneInput = document.getElementById('phone_input');
    const finalSubmitBtn = document.getElementById('final-submit-btn');
    const confirmModal = new bootstrap.Modal(document.getElementById('companyDataConfirmModal'));
    const confirmFinalSubmit = document.getElementById('confirm-final-submit');
    const form = document.querySelector('form[action*="profile.complete"]');

    // Handle final submit button click
    if (finalSubmitBtn) {
        finalSubmitBtn.addEventListener('click', function(e) {
            e.preventDefault();

            // Update modal dengan data terbaru dari form
            updateModalData();

            // Tampilkan modal konfirmasi
            confirmModal.show();
        });
    }

    // Handle konfirmasi final submit
    if (confirmFinalSubmit) {
        confirmFinalSubmit.addEventListener('click', function() {
            confirmModal.hide();
            if (form) {
                form.submit();
            }
        });
    }

    // Function untuk update data di modal
    function updateModalData() {
        // Update email
        const emailInput = document.querySelector('input[name="email"]');
        if (emailInput) {
            document.getElementById('confirm-email').textContent = emailInput.value || '-';
        }

        // Update phone
        const phoneInput = document.querySelector('input[name="phone"]');
        if (phoneInput) {
            document.getElementById('confirm-phone').textContent = phoneInput.value || '-';
        }
    }

    if (phoneInput) {
        phoneInput.addEventListener('blur', function() {
            let value = this.value.trim();

            // Remove any non-digit characters except +
            value = value.replace(/[^\d+]/g, '');

            // Format phone number
            if (value.startsWith('08')) {
                // Replace 08 with 628
                value = '628' + value.substring(2);
            } else if (value.startsWith('02')) {
                // Replace 02 with 622
                value = '622' + value.substring(2);
            } else if (value.startsWith('0')) {
                // Replace other 0 prefixes with 62
                value = '62' + value.substring(1);
            } else if (!value.startsWith('62') && !value.startsWith('+62')) {
                // If doesn't start with 62 or +62, assume it's local number starting with 8
                if (value.startsWith('8')) {
                    value = '62' + value;
                }
            }

            // Ensure it starts with +62
            if (value.startsWith('62') && !value.startsWith('+62')) {
                value = '+' + value;
            }

            this.value = value;
        });
    }

    // Override setLocationSession function untuk menyimpan koordinat ke hidden input
    window.setLocationSession = function(form) {
        // Update hidden inputs dengan koordinat yang dipilih
        const lat = form.get('lat');
        const lng = form.get('lng');

        if (lat && lng) {
            const latInput = document.getElementById('lat_input_progress');
            const longInput = document.getElementById('long_input_progress');

            if (latInput && longInput) {
                latInput.value = lat;
                longInput.value = lng;
            }
        }

        // Panggil fungsi asli untuk menyimpan ke session
        axios.post('{{ route('website.set.session') }}', form)
            .then(function(response) {
                console.log('Location saved to session');
            })
            .catch(function(error) {
                console.error('Error saving location:', error);
            });
    };
});
</script>
@endpush
