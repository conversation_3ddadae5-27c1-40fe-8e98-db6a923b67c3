@props(['user'])

<form action="{{ route('company.profile.complete', auth()->user()->id) }}" method="post" enctype="multipart/form-data">
    @method('PUT')
    @csrf
    <input type="hidden" name="field" value="personal">
    @if ($errors->any())
    <div class="alert alert-danger">
        <ul>
            @foreach ($errors->all() as $error)
                <li>{{ $error }}</li>
            @endforeach
        </ul>
    </div>
@endif
    <fieldset>
        <div class="form-card">
            <div class="personal-profile-picture-wrap">
                <div class="company-logo-banner-info">
                    <h6>{{ __('logo_banner_image') }}</h6>
                    <div class="row">
                        <x-website.company.photo-section :user="$user" />
                        <x-website.company.banner-section :user="$user" />
                        <small class="text-danger font-italic">Logo wajib diisi untuk ditampilkan di halaman profil perusahaan. Format: .jpeg / .jpg / .png</small>
                    </div>
                </div>
            </div>

            <div class="dashboard-account-setting-item">
                <h6>{{ __('company_information') }}</h6>
                <div class="row">
                    <div class="col-md-4 mb-3">
                        <label class="pointer body-font-4 d-block text-gray-900 rt-mb-8">
                            {{ __('Badan Hukum') }}
                        </label>
                        <div class="fromGroup">
                            <div class="form-control-icon">
                                @php
                                    $organizationType = $user->company && $user->company->organization_type_id
                                        ? \App\Models\OrganizationTypeTranslation::where('organization_type_id', $user->company->organization_type_id)
                                            ->where('locale', 'id')
                                            ->first()
                                        : null;
                                @endphp
                                <input class="form-control" type="text"
                                    value="{{ $organizationType ? $organizationType->prefix : '-' }}"
                                    disabled readonly>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-8 mb-3">
                        <label class="pointer body-font-4 d-block text-gray-900 rt-mb-8">
                            {{ __('Nama Perusahaan') }}
                        </label>
                        <div class="fromGroup">
                            <div class="form-control-icon">
                                <input class="form-control" type="text"
                                    value="{{ $user->name ?? old('name') }}"
                                    disabled readonly>
                            </div>
                        </div>
                        <small class="text-muted">Data ini diambil dari registrasi dan tidak dapat diubah di sini</small>
                    </div>
                    <div class="col-12">
                        <label class="body-font-4 d-block text-gray-900 rt-mb-8">
                            {{ __('Tentang Perusahaan') }}
                            <x-forms.required />
                            <span class="text-info" data-toggle="tooltip" title="Minimal 50 karakter untuk deskripsi perusahaan yang baik"><i class="ph-question"></i></span>
                        </label>
                        <textarea rows="8" class="form-control @error('bio') is-invalid @enderror" name="bio"
                            placeholder="{{ __('Contoh: Kami adalah perusahaan yang bergerak di bidang ...') }}" id="bio_editor">{{ $user->company->bio ?? old('bio') }}</textarea>
                        <div class="d-flex justify-content-between mt-1">
                            <small class="text-muted">Minimal 50 karakter</small>
                            <small class="text-muted"><span id="bio_char_count">0</span> karakter</small>
                        </div>
                        @error('bio')
                            <span class="invalid-feedback" role="alert">
                                <strong>{{ __($message) }}</strong>
                            </span>
                        @enderror
                    </div>
                </div>
            </div>
        </div>
        <button type="submit" class="btn next btn-primary">
            {{ __('save_next') }}
        </button>
    </fieldset>
</form>

@push('frontend_scripts')
<script src="{{ asset('frontend') }}/assets/js/ckeditor.min.js"></script>
<script>
    // Konfigurasi CKEditor tanpa upload media
    ClassicEditor
        .create(document.querySelector('#bio_editor'), {
            toolbar: ['heading', '|', 'bold', 'italic', 'link', 'bulletedList', 'numberedList', 'blockQuote'],
            removePlugins: ['CKFinderUploadAdapter', 'CKFinder', 'EasyImage', 'Image', 'ImageCaption', 'ImageStyle', 'ImageToolbar', 'ImageUpload', 'MediaEmbed'],
        })
        .then(editor => {
            // Penghitung karakter
            const charCountElement = document.getElementById('bio_char_count');

            // Fungsi untuk menghitung karakter (tanpa HTML tags)
            function updateCharCount() {
                const text = editor.getData().replace(/<[^>]*>/g, '');
                const charCount = text.length;
                charCountElement.textContent = charCount;

                // Validasi minimal 50 karakter
                if (charCount < 50) {
                    charCountElement.classList.add('text-danger');
                    charCountElement.classList.remove('text-success');
                } else {
                    charCountElement.classList.add('text-success');
                    charCountElement.classList.remove('text-danger');
                }
            }

            // Update penghitung saat editor diinisialisasi
            updateCharCount();

            // Update penghitung saat konten berubah
            editor.model.document.on('change:data', updateCharCount);
        })
        .catch(error => {
            console.error(error);
        });
</script>
@endpush
