@props(['countries', 'user', 'organizationTypes', 'industryTypes', 'teamSizes'])

<form action="{{ route('company.profile.complete', auth()->user()->id) }}" method="post">
    @method('PUT')
    @csrf
    <input type="hidden" name="field" value="profile">
    <fieldset>
        <div class="form-card mb-4">
            <div class="dashboard-account-setting-item pb-0">
                <div class="row">
                    <div class="col-lg-12 mb-3">
                        <label class="body-font-4 d-block text-gray-900 rt-mb-8">
                            {{ __('organization_type') }}
                            <x-forms.required />
                        </label>
                        <select name="organization_type_id"
                            class="select2-taggable @error('organization_type_id') is-invalid @enderror"
                            id="organization_type_id">
                            @foreach ($organizationTypes as $type)
                                <option
                                    {{ $type->id == old('organization_type_id', $user->company->organization_type_id) ? 'selected' : '' }}
                                    value="{{ $type->id }}">
                                    {{ $type->name }}
                                </option>
                            @endforeach
                        </select>
                        <small class="text-warning"><i class="fas fa-exclamation-triangle"></i> Badan hukum tidak dapat diubah setelah diinput di pengaturan</small>
                        @error('organization_type_id')
                            <span class="invalid-feedback" role="alert">
                                <strong>{{ $message }}</strong>
                            </span>
                        @enderror
                    </div>
                    <div class="col-lg-6 mb-3">
                        <label class="body-font-4 d-block text-gray-900 rt-mb-8">{{ __('industry_type') }}
                            <x-forms.required />
                        </label>
                        <select type="text"
                            class="select2-taggable-industry @error('industry_type_id') is-invalid @enderror text-uppercase"
                            name="industry_type_id" id="industry_type">
                            @foreach ($industryTypes as $type)
                                <option
                                    {{ $type->id == old('industry_type_id', $user->company->industry_type_id) ? 'selected' : '' }}
                                    value="{{ $type->id }}">
                                    {{ $type->name }}
                                </option>
                            @endforeach
                        </select>
                        <small class="text-muted">Jika jenis industri tidak ada di list, Anda bisa mengetik dan tekan Enter</small>
                        @error('industry_type_id')
                            <span class="invalid-feedback" role="alert"><strong>{{ __($message) }}</strong></span>
                        @enderror
                    </div>
                    <div class="col-lg-6 mb-3">
                        <label class="pointer body-font-4 d-block text-gray-900 rt-mb-8">
                            {{ __('team_size') }}
                            <x-forms.required />
                        </label>
                        <select type="text" name="team_size_id"
                            class="rt-selectactive @error('team_size_id') is-invalid @enderror" id="team_size">
                            <option value="">{{ __('select_one') }}</option>
                            @foreach ($teamSizes as $size)
                                <option
                                    {{ $size->id == old('team_size_id', $user->company->team_size_id) ? 'selected' : '' }}
                                    value="{{ $size->id }}">
                                    {{ $size->name }}
                                </option>
                            @endforeach
                        </select>
                        @error('team_size_id')
                            <span class="invalid-feedback" role="alert"><strong>{{ __($message) }}</strong></span>
                        @enderror
                    </div>
                    <div class="col-lg-6 mb-3">
                        <label class="pointer body-font-4 d-block text-gray-900 rt-mb-8">
                            {{ __('website') }} (Opsional)
                        </label>
                        <div class="fromGroup has-icon2">
                            <div class="form-control-icon">
                                <input name="website" id="website_input" class="form-control @error('website') is-invalid @enderror"
                                    type="text" placeholder="Contoh: websitesaya.com"
                                    value="{{ old('website', $user->company->website) }}">
                                <div class="icon-badge-2">
                                    <x-svg.link-icon />
                                </div>
                            </div>
                        </div>
                        <small class="text-muted">Cukup masukkan nama domain, https:// akan ditambahkan otomatis</small>
                        @error('website')
                            <span class="invalid-feedback d-block" role="alert">
                                <strong>{{ __($message) }}</strong>
                            </span>
                        @enderror
                    </div>
                    <div class="col-lg-6 mb-3">
                        <label class="pointer body-font-4 d-block text-gray-900 rt-mb-8">
                            {{ __('Tanggal Pendirian') }} (Opsional)
                        </label>
                        <div class="fromGroup">
                            <div class="form-control-icon date datepicker">
                                <input autocomplete="off" name="establishment_date" placeholder="d-m-Y" type="text"
                                    class="form-control @error('establishment_date') is-invalid @enderror"
                                    id="date"
                                    value="{{ old('establishment_date', $user->company->establishment_date ? date('d-m-Y', strtotime($user->company->establishment_date)) : '') }}" />
                                <span class="input-group-addon has-badge">
                                    <x-svg.calendar-icon />
                                </span>
                                @error('establishment_date')
                                    <span class="invalid-feedback" role="alert">
                                        <strong>{{ __($message) }}</strong>
                                    </span>
                                @enderror
                            </div>
                        </div>
                    </div>
                    <div class="col-12">
                        <label class="body-font-4 d-block text-gray-900 rt-mb-8">
                            Visi dan Misi Perusahaan
                            <x-forms.required />
                            <span class="text-info" data-toggle="tooltip" title="Minimal 50 karakter untuk visi dan misi perusahaan yang baik"><i class="ph-question"></i></span>
                        </label>
                        <textarea id="vision_editor" name="vision" class="ckeditor @error('vision') is-invalid @enderror"
                            placeholder="Tuliskan visi dan misi perusahaan Anda">{{ old('vision', $user->company->vision) }}</textarea>
                        <div class="d-flex justify-content-between mt-1">
                            <small class="text-muted">Minimal 50 karakter</small>
                            <small class="text-muted"><span id="vision_char_count">0</span> karakter</small>
                        </div>
                        @error('vision')
                            <span class="invalid-feedback" role="alert">
                                <strong>{{ __($message) }}</strong>
                            </span>
                        @enderror
                    </div>
                </div>
            </div>
        </div>
        <a href="{{ url('company/account-progress') }}">
            <button type="button" class="btn previous bg-gray-50 rt-mr-8">
                {{ __('previous') }}
            </button>
        </a>
        <button type="submit" class="btn next btn-primary">
            <span class="button-content-wrapper ">
                <span class="button-icon align-icon-right">
                    <i class="ph-arrow-right"></i>
                </span>
                <span class="button-text">
                    {{ __('save_next') }}
                </span>
            </span>
        </button>
    </fieldset>
</form>

@push('frontend_scripts')
<script src="{{ asset('frontend/assets/js/bootstrap-datepicker.min.js') }}"></script>
@if (app()->getLocale() == 'ar')
<script src="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-datepicker/1.9.0/locales/bootstrap-datepicker.ar.min.js"></script>
@endif
<script>
    function UploadMode(param) {
        if (param === 'photo') {
            $('#photo-uploadMode').removeClass('d-none');
            $('#photo-oldMode').addClass('d-none');
        } else {
            $('#banner-uploadMode').removeClass('d-none');
            $('#banner-oldMode').addClass('d-none');
        }
    }
</script>
<script src="{{ asset('frontend') }}/assets/js/ckeditor.min.js"></script>
<script>
    // Konfigurasi CKEditor tanpa upload media
    ClassicEditor
        .create(document.querySelector('#vision_editor'), {
            toolbar: ['heading', '|', 'bold', 'italic', 'link', 'bulletedList', 'numberedList', 'blockQuote'],
            removePlugins: ['CKFinderUploadAdapter', 'CKFinder', 'EasyImage', 'Image', 'ImageCaption', 'ImageStyle', 'ImageToolbar', 'ImageUpload', 'MediaEmbed'],
        })
        .then(editor => {
            // Penghitung karakter
            const charCountElement = document.getElementById('vision_char_count');

            // Fungsi untuk menghitung karakter (tanpa HTML tags)
            function updateCharCount() {
                const text = editor.getData().replace(/<[^>]*>/g, '');
                const charCount = text.length;
                charCountElement.textContent = charCount;

                // Validasi minimal 50 karakter
                if (charCount < 50) {
                    charCountElement.classList.add('text-danger');
                    charCountElement.classList.remove('text-success');
                } else {
                    charCountElement.classList.add('text-success');
                    charCountElement.classList.remove('text-danger');
                }
            }

            // Update penghitung saat editor diinisialisasi
            updateCharCount();

            // Update penghitung saat konten berubah
            editor.model.document.on('change:data', updateCharCount);
        })
        .catch(error => {
            console.error(error);
        });
    //init datepicker
    $("#date").attr("autocomplete", "off");
    //init datepicker
    $('#date').datepicker({
        format: 'dd-mm-yyyy',
        isRTL: "{{ app()->getLocale() == 'ar' ? true : false }}",
        language: "{{ app()->getLocale() }}",
    });

    // Inisialisasi Select2 untuk jenis industri dengan opsi tags
    $(document).ready(function() {
        $('.select2-taggable-industry').select2({
            tags: true,
            createTag: function (params) {
                return {
                    id: params.term,
                    text: params.term,
                    newTag: true
                }
            },
            placeholder: 'Pilih atau ketik jenis industri baru',
            allowClear: true,
            width: '100%'
        });

        // Tangani pemilihan jenis industri baru
        $('.select2-taggable-industry').on('select2:select', function (e) {
            var data = e.params.data;
            if (data.newTag) {
                // Jika ini adalah tag baru, kirim permintaan AJAX untuk membuat jenis industri baru
                $.ajax({
                    url: '{{ route("company.industry.store") }}',
                    type: 'POST',
                    data: {
                        name: data.text,
                        _token: '{{ csrf_token() }}'
                    },
                    success: function(response) {
                        if (response.success) {
                            // Ganti ID sementara dengan ID yang sebenarnya dari database
                            var newOption = new Option(data.text, response.id, true, true);
                            $('.select2-taggable-industry').find('option[value="' + data.id + '"]').remove();
                            $('.select2-taggable-industry').append(newOption).trigger('change');

                            // Tampilkan pesan sukses
                            alert('Jenis industri baru berhasil ditambahkan!');
                        }
                    },
                    error: function(xhr) {
                        alert('Gagal menambahkan jenis industri baru. Silakan coba lagi.');
                        console.error(xhr.responseText);
                    }
                });
            }
        });
    });

    // Auto-add https:// to website field
    const websiteInput = document.getElementById('website_input');
    if (websiteInput) {
        websiteInput.addEventListener('blur', function() {
            let value = this.value.trim();
            if (value && !value.startsWith('http://') && !value.startsWith('https://')) {
                this.value = 'https://' + value;
            }
        });
    }
</script>
@endpush
