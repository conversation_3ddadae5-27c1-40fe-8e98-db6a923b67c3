<div class="tw-flex tw-flex-col tw-items-center tw-justify-center tw-py-10">
    <div id="lottie-container" class="tw-w-32 tw-h-32 tw-mb-4"></div>
    <p class="tw-text-center tw-text-gray-600 tw-text-lg">Memuat loker...</p>
</div>

<script src="https://cdnjs.cloudflare.com/ajax/libs/lottie-web/5.9.6/lottie.min.js"></script>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        var animation = lottie.loadAnimation({
            container: document.getElementById('lottie-container'),
            renderer: 'svg',
            loop: true,
            autoplay: true,
            path: '{{ asset('frontend/images/loading.json') }}'
        });
    });
</script>
