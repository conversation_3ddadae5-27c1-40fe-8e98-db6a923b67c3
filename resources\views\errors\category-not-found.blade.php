@extends('frontend.layouts.app')

@section('title', 'Kategori Tidak Ditemukan')

@section('main')
    <section class="rt-error">
        <div class="container">
            <div class="row align-items-center">
                <div class="rt-spacer-100 rt-spacer-md-50"></div>
                <div class="rt-spacer-50 rt-spacer-md-0"></div>
                <div class="col-xl-6 col-lg-6 order-1 order-lg-0">
                    <div class="rt-error-left">
                        <h4 class="rt-mb-24">{{ __('Kategori Tidak Ditemukan') }}</h4>
                        <div class="body-font-2 ft-wt-5 text-gray-500 rt-mb-24 max-width-408">
                            {{ __('Maaf, kategori loker') }} <strong>"{{ $slug }}"</strong> {{ __('tidak ditemukan. Silakan coba kategori lain atau kembali ke halaman kategori.') }}
                        </div>

                        @if(isset($availableCategories) && $availableCategories->count() > 0)
                            <div class="rt-mb-24">
                                <h5 class="rt-mb-15">{{ __('Kategori Tersedia:') }}</h5>
                                <div class="row g-3">
                                    @foreach($availableCategories as $category)
                                        <div class="col-lg-6">
                                            <a href="{{ route('website.job.category.slug', ['category' => $category->slug]) }}" class="tw-block tw-p-3 tw-border tw-border-gray-200 tw-rounded-lg tw-bg-white hover:tw-bg-primary-50 hover:tw-border-primary-500 tw-transition-all">
                                                <div class="tw-flex tw-items-center tw-gap-2">
                                                    <div class="tw-w-8 tw-h-8 tw-flex tw-items-center tw-justify-center tw-bg-primary-50 tw-text-primary-500 tw-rounded-full">
                                                        <i class="ph-briefcase-simple"></i>
                                                    </div>
                                                    <div>
                                                        <span class="tw-font-medium">{{ $category->name }}</span>
                                                        <span class="tw-text-sm tw-text-gray-500">({{ $category->jobs_count }})</span>
                                                    </div>
                                                </div>
                                            </a>
                                        </div>
                                    @endforeach
                                </div>
                            </div>
                        @endif

                        <div class="error-button d-flex">
                            <div class="me-3">
                                <a href="{{ route('website.job.category') }}" class="btn btn-primary">
                                    <span class="button-content-wrapper">
                                        <span class="button-icon align-icon-right">
                                            <svg width="24" height="24" viewBox="0 0 24 24" fill="none"
                                                xmlns="http://www.w3.org/2000/svg">
                                                <path d="M5 12H19" stroke="white" stroke-width="1.5"
                                                    stroke-linecap="round" stroke-linejoin="round"></path>
                                                <path d="M12 5L19 12L12 19" stroke="white" stroke-width="1.5"
                                                    stroke-linecap="round" stroke-linejoin="round"></path>
                                            </svg>
                                        </span>
                                        <span class="button-text">
                                            {{ __('Lihat Semua Kategori') }}
                                        </span>
                                    </span>
                                </a>
                            </div>
                            <div>
                                <a href="{{ route('website.job') }}" class="btn btn-outline-primary">
                                    {{ __('Lihat Semua Loker') }}
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-xl-6 col-lg-6 order-0 order-lg-1">
                    <div class="rt-error-right">
                        <img src="{{ asset($cms_setting?->page404_image) }}" class="img-fluid" alt="error-banner">
                    </div>
                </div>
            </div>
        </div>
        <div class="rt-spacer-100 rt-spacer-md-50"></div>
        <div class="rt-spacer-50 rt-spacer-md-0"></div>
    </section>
@endsection
