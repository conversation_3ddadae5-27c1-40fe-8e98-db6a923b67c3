<!DOCTYPE html>
<html>
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
    <title>Daftar Pencaker</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            font-size: 12px;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
        }
        th {
            background-color: #007BFF;
            color: white;
            font-weight: bold;
            text-align: center;
            padding: 8px;
            border: 1px solid #000;
        }
        td {
            padding: 6px;
            border: 1px solid #000;
            vertical-align: top;
        }
        tr:nth-child(even) {
            background-color: #f2f2f2;
        }
        h1 {
            text-align: center;
            font-size: 18px;
            margin-bottom: 20px;
        }
        .date {
            text-align: right;
            margin-bottom: 20px;
            font-style: italic;
        }
    </style>
</head>
<body>
    <h1>Daftar Pencaker</h1>
    <div class="date">Tanggal: {{ date('d/m/Y') }}</div>

    <table>
        <thead>
            <tr>
                <th>No</th>
                <th>NIK</th>
                <th><PERSON>a</th>
                <th>Email</th>
                <th><PERSON><PERSON></th>
                <th>No HP</th>
                <th>Pendidikan</th>
                <th>Profesi</th>
                <th>Alamat</th>
                <th>Status</th>
                <th>Tanggal Daftar</th>
            </tr>
        </thead>
        <tbody>
            @foreach($candidates as $index => $candidate)
                <tr>
                    <td style="text-align: center;">{{ $index + 1 }}</td>
                    <td>{{ $candidate->user->nik ?? '-' }}</td>
                    <td>{{ $candidate->user->name ?? '-' }}</td>
                    <td>{{ $candidate->user->email ?? '-' }}</td>
                    <td>{{ $candidate->gender == 'male' ? 'Laki-laki' : 'Perempuan' }}</td>
                    <td>{{ $candidate->user->no_hp ?? '-' }}</td>
                    <td>{{ $candidate->education ? $candidate->education->name : '-' }}</td>
                    <td>{{ $candidate->profession ? $candidate->profession->name : '-' }}</td>
                    <td>{{ $candidate->user->alamat_ktp ?? '-' }}</td>
                    <td style="text-align: center;">{{ $candidate->user->status == 1 ? 'Aktif' : 'Tidak Aktif' }}</td>
                    <td style="text-align: center;">{{ $candidate->created_at ? $candidate->created_at->format('d/m/Y') : '-' }}</td>
                </tr>
            @endforeach
        </tbody>
    </table>

    <div style="text-align: right; margin-top: 30px;">
        <p>Total Pencaker: {{ count($candidates) }}</p>
    </div>
</body>
</html>
