@extends('frontend.layouts.app')

@section('title', __('Pesan'))

@section('main')
    <div class="dashboard-wrapper">
        <div class="container">
            <div class="row">
                {{-- Sidebar --}}
                <x-website.candidate.sidebar />

                <div class="col-lg-9">
                    <div class="dashboard-right tw-ps-0 lg:tw-ps-5">
                        <div class="dashboard-right-header">
                            <span class="sidebar-open-nav">
                                <i class="ph-list"></i>
                            </span>
                        </div>
                        <h2 class="tw-text-2xl tw-font-medium tw-text-[#18191C] tw-mb-8">
                            {{ __('Pesan') }}
                        </h2>

                        <div class="support-ticket-container">
                            <div class="row g-0">
                                <!-- Message Detail Section -->
                                <div class="col-12" id="message-detail-section" style="{{ $selectedThread ? 'display: block;' : 'display: none;' }}">
                                    <div class="card">
                                        <div class="card-header d-flex justify-content-between align-items-center">
                                            <h5 id="message-detail-title">{{ __('Detail Pesan') }}</h5>
                                            <button type="button" class="btn btn-outline-secondary btn-sm" id="back-to-list">
                                                <i class="fas fa-arrow-left me-1"></i> {{ __('Kembali ke Daftar Pesan') }}
                                            </button>
                                        </div>
                                        <div class="card-body p-0">
                                            <div id="message-container">
                                                @if($selectedThread)
                                                    <div class="message-header p-3 border-bottom">
                                                        @php
                                                            // Determine other party based on user role
                                                            if (auth()->user()->role == 'candidate') {
                                                                $otherParty = $selectedThread->company->user ?? null;
                                                                $otherPartyName = $otherParty ? $otherParty->name : 'Admin';
                                                                $legalEntity = $selectedThread->company->legal_entity ?? '';
                                                                $otherPartyName = $otherParty ? ($legalEntity ? $legalEntity . ' ' . $otherParty->name : $otherParty->name) : 'Admin';
                                                                $otherPartyImage = $selectedThread->company && $selectedThread->company->logo ? str_replace('/company/uploads/', '/uploads/', $selectedThread->company->logo) : asset('frontend/assets/images/default-company.png');
                                                            } else {
                                                                $otherParty = $selectedThread->candidate->user ?? null;
                                                                $otherPartyName = $otherParty ? $otherParty->name : 'Admin';
                                                                $otherPartyImage = $selectedThread->candidate && $selectedThread->candidate->photo ? str_replace('/storage/', '/uploads/', $selectedThread->candidate->photo) : asset('frontend/assets/images/default-user.png');
                                                            }
                                                        @endphp
                                                        <div class="d-flex align-items-center">
                                                            <div class="avatar me-3">
                                                                @if($selectedThread->is_admin_thread)
                                                                    <div class="rounded-circle bg-primary d-flex align-items-center justify-content-center" style="width: 50px; height: 50px;">
                                                                        <i class="fas fa-user-shield text-white"></i>
                                                                    </div>
                                                                @else
                                                                    @if($otherPartyImage)
                                                                        <img src="{{ $otherPartyImage }}" alt="{{ $otherPartyName }}" class="rounded-circle" width="50" height="50">
                                                                    @else
                                                                        <div class="rounded-circle bg-info d-flex align-items-center justify-content-center" style="width: 50px; height: 50px;">
                                                                            <span class="text-white fw-bold">{{ substr($otherPartyName, 0, 1) }}</span>
                                                                        </div>
                                                                    @endif
                                                                @endif
                                                            </div>
                                                            <div>
                                                                <h5 class="mb-1">{{ $otherPartyName }}</h5>
                                                                <p class="mb-0 text-muted">{{ $selectedThread->subject }}</p>
                                                                @if($selectedThread->job)
                                                                    <small class="text-muted">
                                                                        {{ __('Mengenai lowongan') }}: <a href="{{ route('website.job.details', $selectedThread->job->slug) }}" target="_blank">{{ $selectedThread->job->title }}</a>
                                                                    </small>
                                                                @endif
                                                            </div>
                                                        </div>
                                                    </div>

                                                    <div class="message-body p-3" style="height: 400px; overflow-y: auto;" id="message-content">
                                                        @if($hasMoreMessages)
                                                            <div class="text-center mb-3">
                                                                <button class="btn btn-sm btn-outline-secondary" id="load-more-btn" data-thread="{{ $selectedThread->id }}" data-skip="5">
                                                                    <i class="fas fa-chevron-up me-1"></i> {{ __('Muat pesan sebelumnya') }}
                                                                </button>
                                                            </div>
                                                        @endif

                                                        <div id="message-list">
                                                            @foreach($groupedMessages as $date => $messages)
                                                                <div class="message-date-header text-center my-3 position-relative">
                                                                    <span class="badge bg-light text-dark px-3 py-2 position-relative">
                                                                        {{ \Carbon\Carbon::parse($date)->format('d F Y') }}
                                                                    </span>
                                                                </div>
                                                                @foreach($messages as $index => $message)
                                                                    <div class="message-item mb-3 {{ auth()->id() == $message->sender_id ? 'sender' : 'receiver' }}" id="message-{{ $message->id }}">
                                                                        <div class="d-flex {{ auth()->id() == $message->sender_id ? 'justify-content-end' : 'justify-content-start' }}">
                                                                            @if(auth()->id() != $message->sender_id)
                                                                                <div class="avatar me-2">
                                                                                    @if($message->sender_type == 'admin')
                                                                                        <div class="rounded-circle bg-primary d-flex align-items-center justify-content-center" style="width: 40px; height: 40px;">
                                                                                            <i class="fas fa-user-shield text-white"></i>
                                                                                        </div>
                                                                                    @else
                                                                                        @if($message->sender_image)
                                                                                            <img src="{{ str_replace(['/company/uploads/', '/storage/'], '/uploads/', $message->sender_image) }}" alt="{{ $message->sender_name }}" class="rounded-circle" width="40" height="40" style="object-fit: cover;">
                                                                                        @else
                                                                                            <div class="rounded-circle bg-info d-flex align-items-center justify-content-center" style="width: 40px; height: 40px;">
                                                                                                <span class="text-white fw-bold">{{ substr($message->sender_name, 0, 1) }}</span>
                                                                                            </div>
                                                                                        @endif
                                                                                    @endif
                                                                                </div>
                                                                            @endif

                                                                            <div class="message-content {{ auth()->id() == $message->sender_id ? 'bg-light-success' : 'bg-light' }}" style="max-width: 75%; border-radius: 10px; padding: 10px 15px;">
                                                                                <div class="message-header d-flex justify-content-between align-items-center mb-2">
                                                                                    <span class="fw-bold">
                                                                                        {{ $message->sender_name }}
                                                                                    </span>
                                                                                    <small class="text-muted">{{ $message->created_time }}</small>
                                                                                </div>
                                                                                <div class="message-text">
                                                                                    {!! nl2br(e($message->body)) !!}
                                                                                </div>
                                                                                @if($message->attachment && !empty($message->attachment))
                                                                                    <div class="message-attachments mt-2">
                                                                                        @php
                                                                                            // Handle different attachment formats
                                                                                            $attachments = [];
                                                                                            if (is_array($message->attachment)) {
                                                                                                if (isset($message->attachment['url']) || isset($message->attachment['path'])) {
                                                                                                    // Format 1: Single attachment with direct url/path
                                                                                                    $attachments[] = $message->attachment;
                                                                                                } elseif (isset($message->attachment[0]) && is_array($message->attachment[0])) {
                                                                                                    // Format 2: Array of attachments
                                                                                                    $attachments = $message->attachment;
                                                                                                } else {
                                                                                                    // Try to process as a regular array
                                                                                                    $attachments = [$message->attachment];
                                                                                                }
                                                                                            } else {
                                                                                                // String format (unlikely but handle it)
                                                                                                $attachments[] = [
                                                                                                    'url' => $message->attachment,
                                                                                                    'name' => 'Lampiran',
                                                                                                    'type' => ''
                                                                                                ];
                                                                                            }

                                                                                            // Debug attachment info if needed
                                                                                            // echo "<pre>Attachment: " . json_encode($message->attachment, JSON_PRETTY_PRINT) . "</pre>";
                                                                                        @endphp

                                                                                        @foreach($attachments as $attachment)
                                                                                            <div class="attachment mb-2">
                                                                                                @php
                                                                                                    // Determine file URL based on format
                                                                                                    if (isset($attachment['url'])) {
                                                                                                        $fileUrl = $attachment['url'];
                                                                                                        $fileName = $attachment['name'] ?? 'Lampiran';
                                                                                                        $fileType = $attachment['type'] ?? '';
                                                                                                    } elseif (isset($attachment['path'])) {
                                                                                                        $fileUrl = asset('public/storage/' . $attachment['path']);
                                                                                                        $fileName = $attachment['name'] ?? 'Lampiran';
                                                                                                        $fileType = $attachment['type'] ?? '';
                                                                                                    } else {
                                                                                                        $fileUrl = '';
                                                                                                        $fileName = 'Lampiran';
                                                                                                        $fileType = '';
                                                                                                    }

                                                                                                    // Determine file type from extension if not provided
                                                                                                    if (!$fileType || $fileType === '') {
                                                                                                        $extension = strtolower(pathinfo($fileUrl, PATHINFO_EXTENSION));
                                                                                                        if (in_array($extension, ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'svg', 'webp'])) {
                                                                                                            $fileType = 'image';
                                                                                                        } elseif (in_array($extension, ['pdf', 'doc', 'docx'])) {
                                                                                                            $fileType = 'document';
                                                                                                        }
                                                                                                    }

                                                                                                    // If type is a MIME type, extract the main type
                                                                                                    if (strpos($fileType, '/') !== false) {
                                                                                                        list($mainType, $subType) = explode('/', $fileType, 2);
                                                                                                        if ($mainType === 'image') {
                                                                                                            $fileType = 'image';
                                                                                                        } elseif ($mainType === 'application' && ($subType === 'pdf' || strpos($subType, 'pdf') !== false)) {
                                                                                                            $fileType = 'document';
                                                                                                        }
                                                                                                    }

                                                                                                    // Debug info if needed
                                                                                                    // echo "File URL: $fileUrl, Type: $fileType, Name: $fileName<br>";
                                                                                                @endphp

                                                                                                @if($fileType == 'document')
                                                                                                    <div class="pdf-attachment p-3 border rounded bg-light">
                                                                                                        <div class="d-flex align-items-center mb-2">
                                                                                                            <i class="fas fa-file-pdf text-danger fs-4 me-2"></i>
                                                                                                            <span class="text-truncate" style="max-width: 200px;" title="{{ $fileName }}">
                                                                                                                {{ $fileName }}
                                                                                                            </span>
                                                                                                        </div>
                                                                                                        <div class="btn-group btn-group-sm w-100">
                                                                                                            <a href="{{ $fileUrl }}" target="_blank" class="btn btn-outline-primary">
                                                                                                                <i class="fas fa-download me-1"></i> {{ __('Unduh') }}
                                                                                                            </a>
                                                                                                            <button type="button" class="btn btn-outline-secondary pdf-preview-btn" data-pdf-url="{{ $fileUrl }}" data-pdf-name="{{ $fileName }}">
                                                                                                                <i class="fas fa-eye me-1"></i> {{ __('Pratinjau') }}
                                                                                                            </button>
                                                                                                        </div>
                                                                                                    </div>
                                                                                                @elseif($fileType == 'image')
                                                                                                    <div class="image-attachment">
                                                                                                        <a href="{{ $fileUrl }}" class="image-lightbox" data-caption="{{ $message->body }}">
                                                                                                            <img src="{{ $fileUrl }}" alt="{{ $fileName }}" class="img-fluid rounded" style="max-height: 200px;">
                                                                                                        </a>
                                                                                                    </div>
                                                                                                @else
                                                                                                    <a href="{{ $fileUrl }}" target="_blank" class="btn btn-sm btn-outline-primary">
                                                                                                        <i class="fas fa-paperclip me-1"></i> {{ $fileName }}
                                                                                                    </a>
                                                                                                @endif
                                                                                            </div>
                                                                                        @endforeach
                                                                                    </div>
                                                                                @endif
                                                                            </div>

                                                                            @if(auth()->id() == $message->sender_id)
                                                                                <div class="avatar ms-2">
                                                                                    @if($message->sender_type == 'admin')
                                                                                        <div class="rounded-circle bg-primary d-flex align-items-center justify-content-center" style="width: 40px; height: 40px;">
                                                                                            <i class="fas fa-user-shield text-white"></i>
                                                                                        </div>
                                                                                    @else
                                                                                        @if($message->sender_image)
                                                                                            <img src="{{ str_replace(['/company/uploads/', '/storage/'], '/uploads/', $message->sender_image) }}" alt="{{ $message->sender_name }}" class="rounded-circle" width="40" height="40" style="object-fit: cover;">
                                                                                        @else
                                                                                            <div class="rounded-circle bg-info d-flex align-items-center justify-content-center" style="width: 40px; height: 40px;">
                                                                                                <span class="text-white fw-bold">{{ substr($message->sender_name, 0, 1) }}</span>
                                                                                            </div>
                                                                                        @endif
                                                                                    @endif
                                                                                </div>
                                                                            @endif
                                                                        </div>
                                                                    </div>
                                                                @endforeach
                                                            @endforeach
                                                        </div>
                                                    </div>

                                                    <div class="message-reply p-3 border-top">
                                                        @php
                                                            // Periksa apakah pesan terakhir dapat dibalas
                                                            $lastMessage = $selectedThread->messages()->latest()->first();
                                                            $canReply = !$lastMessage || $lastMessage->can_reply;
                                                        @endphp

                                                        @if($canReply)
                                                            <form id="reply-form" data-thread="{{ $selectedThread->id }}" enctype="multipart/form-data">
                                                                <div class="form-group">
                                                                    <textarea class="form-control" id="reply-message" rows="3" placeholder="{{ __('Ketik pesan Anda di sini...') }}"></textarea>
                                                                </div>
                                                                <div class="d-flex justify-content-between align-items-center mt-2">
                                                                    <div class="attachment-buttons">
                                                                        <label for="file-attachment" class="btn btn-outline-secondary btn-sm me-2" data-bs-toggle="tooltip" title="{{ __('Lampirkan Dokumen (PDF)') }}">
                                                                            <i class="fas fa-file-pdf"></i>
                                                                            <span class="d-none d-md-inline ms-1">{{ __('Dokumen') }}</span>
                                                                        </label>
                                                                        <input type="file" id="file-attachment" name="file-attachment" accept=".pdf" class="d-none">

                                                                        <label for="image-attachment" class="btn btn-outline-secondary btn-sm" data-bs-toggle="tooltip" title="{{ __('Lampirkan Gambar') }}">
                                                                            <i class="fas fa-image"></i>
                                                                            <span class="d-none d-md-inline ms-1">{{ __('Gambar') }}</span>
                                                                        </label>
                                                                        <input type="file" id="image-attachment" name="image-attachment" accept="image/*" class="d-none">
                                                                    </div>
                                                                    <div id="attachment-preview" class="d-none me-2">
                                                                        <span class="badge bg-light text-dark p-2">
                                                                            <i class="fas fa-paperclip me-1"></i>
                                                                            <span id="attachment-name"></span>
                                                                            <button type="button" class="btn-close ms-2" id="remove-attachment" aria-label="Close"></button>
                                                                        </span>
                                                                    </div>
                                                                    <button type="submit" class="btn btn-primary">
                                                                        <i class="fas fa-paper-plane me-1"></i>
                                                                        <span class="d-none d-md-inline">{{ __('Kirim') }}</span>
                                                                    </button>
                                                                </div>
                                                            </form>
                                                        @else
                                                            <div class="alert alert-info mb-0">
                                                                <i class="fas fa-info-circle me-2"></i> {{ __('Pesan ini tidak dapat dibalas.') }}
                                                            </div>
                                                        @endif
                                                    </div>
                                                @endif
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Message List -->
                                <div class="col-12" id="message-list-section" style="{{ $selectedThread ? 'display: none;' : 'display: block;' }}">
                                    <div class="card">
                                        <div class="card-header d-flex justify-content-between align-items-center">
                                            <h5>{{ __('Daftar Pesan') }}</h5>
                                            <div class="filter-buttons">
                                                <a href="{{ route('candidate.messages') }}" class="btn btn-sm {{ !$unreadOnly ? 'btn-primary' : 'btn-outline-secondary' }} me-2">
                                                    <i class="fas fa-envelope-open me-1"></i> {{ __('Semua') }}
                                                </a>
                                                <a href="{{ route('candidate.messages', ['unread' => 1]) }}" class="btn btn-sm {{ $unreadOnly ? 'btn-primary' : 'btn-outline-secondary' }}">
                                                    <i class="fas fa-envelope me-1"></i> {{ __('Belum Dibaca') }}
                                                </a>
                                            </div>
                                        </div>
                                        <div class="card-body border-bottom pb-3">
                                            <form action="{{ route('candidate.messages') }}" method="GET" class="search-form">
                                                @if($unreadOnly)
                                                    <input type="hidden" name="unread" value="1">
                                                @endif
                                                @if(isset($selectedThread) && $selectedThread)
                                                    <input type="hidden" name="pesan_id" value="{{ $selectedThread->id }}">
                                                @endif
                                                <div class="input-group">
                                                    <input type="text" name="cari" class="form-control" placeholder="{{ __('Cari pesan...') }}" value="{{ $search ?? '' }}">
                                                    <button class="btn btn-primary" type="submit">
                                                        <i class="fas fa-search"></i>
                                                    </button>
                                                    @if($search)
                                                        <a href="{{ route('candidate.messages', $unreadOnly ? ['unread' => 1] : []) }}" class="btn btn-outline-secondary">
                                                            <i class="fas fa-times"></i>
                                                        </a>
                                                    @endif
                                                </div>
                                            </form>
                                        </div>
                                        <div class="card-body">
                                            <div id="message-list-container">
                                                @include('frontend.pages.candidate.partials.thread-list', ['threads' => $threads, 'selectedThread' => $selectedThread ?? null])
                                            </div>

                                            <!-- Load More Button -->
                                            <div class="d-flex justify-content-center mt-4" id="load-more-container">
                                                @if($totalPages > 1)
                                                    <button type="button" class="btn btn-outline-primary" id="load-more-threads-btn" data-current-page="1" data-total-pages="{{ $totalPages }}">
                                                        <i class="fas fa-sync-alt me-2"></i> {{ __('Muat Lebih Banyak') }}
                                                    </button>
                                                @endif
                                            </div>

                                            <!-- Loading Indicator -->
                                            <div class="d-flex justify-content-center mt-4 d-none" id="threads-loading-indicator">
                                                <div class="spinner-border text-primary" role="status">
                                                    <span class="visually-hidden">Loading...</span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="dashboard-footer text-center body-font-4 text-gray-500">
            <x-website.footer-copyright />
        </div>
    </div>
@endsection

<!-- PDF Preview Modal -->
<div class="modal fade" id="pdfPreviewModal" tabindex="-1" aria-labelledby="pdfPreviewModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="pdfPreviewModalLabel">{{ __('Pratinjau PDF') }}</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="text-center mb-3" id="pdfFileName"></div>
                <div class="ratio ratio-16x9">
                    <iframe id="pdfPreviewFrame" src="" frameborder="0" allowfullscreen></iframe>
                </div>
            </div>
            <div class="modal-footer">
                <a href="#" class="btn btn-primary" id="pdfDownloadLink" target="_blank" download>
                    <i class="fas fa-download me-1"></i> {{ __('Unduh') }}
                </a>
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                    <i class="fas fa-times me-1"></i> {{ __('Tutup') }}
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Image Lightbox Modal -->
<div class="modal fade" id="imageLightboxModal" tabindex="-1" aria-labelledby="imageLightboxModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="imageLightboxModalLabel">{{ __('Gambar') }}</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body text-center">
                <img id="lightboxImage" src="" class="img-fluid" alt="Lightbox Image">
                <p class="mt-3" id="lightboxCaption"></p>
            </div>
            <div class="modal-footer">
                <a href="#" class="btn btn-primary" id="imageDownloadLink" target="_blank" download>
                    <i class="fas fa-download me-1"></i> {{ __('Unduh') }}
                </a>
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                    <i class="fas fa-times me-1"></i> {{ __('Tutup') }}
                </button>
            </div>
        </div>
    </div>
</div>

@push('styles')
<style>
    .support-ticket-container {
        background-color: #fff;
        border-radius: 10px;
        box-shadow: 0 0 20px rgba(0, 0, 0, 0.05);
        overflow: hidden;
    }

    #message-detail-section .card,
    #message-list-section .card {
        border-radius: 10px;
        box-shadow: 0 0 15px rgba(0, 0, 0, 0.05);
        border: 2px solid #138C79;
    }

    #message-detail-section .card-header,
    #message-list-section .card-header {
        background-color: #f8f9fa;
        padding: 15px 20px;
        border-bottom: 2px solid #138C79;
    }

    .message-item.unread {
        background-color: rgba(19, 140, 121, 0.05);
    }

    .message-item:hover {
        background-color: rgba(19, 140, 121, 0.1);
    }

    .message-date-header {
        position: relative;
    }

    .message-date-header:before {
        content: '';
        position: absolute;
        left: 0;
        top: 50%;
        width: 100%;
        height: 1px;
        background-color: #e5e5e5;
        z-index: 1;
    }

    .message-date-header .badge {
        z-index: 2;
    }

    .bg-light-success {
        background-color: rgba(144, 238, 144, 0.3) !important;
    }

    .avatar-md {
        width: 40px;
        height: 40px;
        overflow: hidden;
    }

    .avatar-md img {
        width: 100%;
        height: 100%;
        object-fit: cover;
    }
</style>
@endpush

@push('scripts')
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Initialize tooltips
        const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
        tooltipTriggerList.map(function (tooltipTriggerEl) {
            return new bootstrap.Tooltip(tooltipTriggerEl, {
                trigger: 'hover focus'
            });
        });

        // Scroll to bottom of message content on load
        const messageContent = document.getElementById('message-content');
        if (messageContent) {
            messageContent.scrollTop = messageContent.scrollHeight;
        }

        // PDF Preview
        const pdfPreviewModal = document.getElementById('pdfPreviewModal');
        if (pdfPreviewModal) {
            const pdfPreviewModalInstance = new bootstrap.Modal(pdfPreviewModal);
            const pdfPreviewFrame = document.getElementById('pdfPreviewFrame');
            const pdfFileName = document.getElementById('pdfFileName');
            const pdfDownloadLink = document.getElementById('pdfDownloadLink');

            document.addEventListener('click', function(e) {
                if (e.target.classList.contains('pdf-preview-btn') || e.target.closest('.pdf-preview-btn')) {
                    const button = e.target.classList.contains('pdf-preview-btn') ? e.target : e.target.closest('.pdf-preview-btn');
                    const pdfUrl = button.getAttribute('data-pdf-url');
                    const pdfName = button.getAttribute('data-pdf-name');

                    console.log('PDF Preview clicked:', pdfUrl, pdfName);

                    if (pdfUrl && pdfPreviewFrame) {
                        // Fix URL if needed (remove any double slashes except after protocol)
                        const fixedUrl = pdfUrl.replace(/(https?:\/\/)|(\/)+/g, function(match, protocol) {
                            if (protocol) return protocol;
                            return '/';
                        });

                        console.log('Loading PDF from URL:', fixedUrl);
                        pdfPreviewFrame.src = fixedUrl;

                        if (pdfFileName) {
                            pdfFileName.textContent = pdfName;
                        }
                        if (pdfDownloadLink) {
                            pdfDownloadLink.href = fixedUrl;
                            pdfDownloadLink.setAttribute('download', pdfName || 'dokumen.pdf');
                        }
                        pdfPreviewModalInstance.show();
                    }
                }
            });

            // Clear iframe when modal is closed
            pdfPreviewModal.addEventListener('hidden.bs.modal', function() {
                if (pdfPreviewFrame) {
                    pdfPreviewFrame.src = '';
                }
            });
        }

        // Image Lightbox
        const imageLightboxModal = document.getElementById('imageLightboxModal');
        if (imageLightboxModal) {
            const imageLightboxModalInstance = new bootstrap.Modal(imageLightboxModal);
            const lightboxImage = document.getElementById('lightboxImage');
            const lightboxCaption = document.getElementById('lightboxCaption');
            const imageDownloadLink = document.getElementById('imageDownloadLink');

            document.addEventListener('click', function(e) {
                if (e.target.closest('.image-lightbox')) {
                    e.preventDefault();
                    const link = e.target.closest('.image-lightbox');
                    const imageUrl = link.getAttribute('href');
                    const caption = link.getAttribute('data-caption');

                    console.log('Image Lightbox clicked:', imageUrl, caption);

                    if (imageUrl && lightboxImage) {
                        // Fix URL if needed (remove any double slashes except after protocol)
                        const fixedUrl = imageUrl.replace(/(https?:\/\/)|(\/)+/g, function(match, protocol) {
                            if (protocol) return protocol;
                            return '/';
                        });

                        console.log('Loading image from URL:', fixedUrl);
                        lightboxImage.src = fixedUrl;

                        if (lightboxCaption) {
                            lightboxCaption.textContent = caption || '';
                        }
                        if (imageDownloadLink) {
                            imageDownloadLink.href = fixedUrl;
                            imageDownloadLink.setAttribute('download', caption || 'image.jpg');
                        }
                        imageLightboxModalInstance.show();
                    }
                }
            });
        }

        // Back to list button
        const backToListBtn = document.getElementById('back-to-list');
        if (backToListBtn) {
            backToListBtn.addEventListener('click', function() {
                // Remove thread ID from URL
                const url = new URL(window.location);
                url.searchParams.delete('pesan_id');
                window.location.href = url.toString();
            });
        }

        // Handle file attachment
        const fileAttachment = document.getElementById('file-attachment');
        const imageAttachment = document.getElementById('image-attachment');
        const attachmentPreview = document.getElementById('attachment-preview');
        const attachmentName = document.getElementById('attachment-name');
        const removeAttachment = document.getElementById('remove-attachment');

        let currentAttachment = null;
        let currentAttachmentType = null;

        if (fileAttachment) {
            fileAttachment.addEventListener('change', function(e) {
                if (this.files.length > 0) {
                    const file = this.files[0];
                    if (file.size > 5 * 1024 * 1024) { // 5MB limit
                        alert('{{ __("Ukuran file terlalu besar. Maksimal 5MB.") }}');
                        this.value = '';
                        return;
                    }

                    // Clear any existing image attachment
                    if (imageAttachment) {
                        imageAttachment.value = '';
                    }

                    currentAttachment = file;
                    currentAttachmentType = 'document';
                    attachmentName.textContent = file.name;
                    attachmentPreview.classList.remove('d-none');
                }
            });
        }

        if (imageAttachment) {
            imageAttachment.addEventListener('change', function(e) {
                if (this.files.length > 0) {
                    const file = this.files[0];
                    if (file.size > 5 * 1024 * 1024) { // 5MB limit
                        alert('{{ __("Ukuran file terlalu besar. Maksimal 5MB.") }}');
                        this.value = '';
                        return;
                    }

                    // Clear any existing document attachment
                    if (fileAttachment) {
                        fileAttachment.value = '';
                    }

                    currentAttachment = file;
                    currentAttachmentType = 'image';
                    attachmentName.textContent = file.name;
                    attachmentPreview.classList.remove('d-none');
                }
            });
        }

        if (removeAttachment) {
            removeAttachment.addEventListener('click', function() {
                if (currentAttachmentType === 'document' && fileAttachment) {
                    fileAttachment.value = '';
                } else if (currentAttachmentType === 'image' && imageAttachment) {
                    imageAttachment.value = '';
                }

                currentAttachment = null;
                currentAttachmentType = null;
                attachmentPreview.classList.add('d-none');
            });
        }

        // Load more messages
        const loadMoreBtn = document.getElementById('load-more-btn');
        if (loadMoreBtn) {
            loadMoreBtn.addEventListener('click', function() {
                const button = this;
                const threadId = button.getAttribute('data-thread');
                const skip = parseInt(button.getAttribute('data-skip'));

                button.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i> Loading...';
                button.disabled = true;

                fetch('{{ route("messages.load-more") }}', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': '{{ csrf_token() }}'
                    },
                    body: JSON.stringify({
                        thread_id: threadId,
                        skip: skip
                    })
                })
                .then(response => response.json())
                .then(data => {
                    // Prepend messages to the list
                    const messageList = document.getElementById('message-list');
                    const tempDiv = document.createElement('div');
                    tempDiv.innerHTML = data.messages;

                    // Insert before the first child
                    while (tempDiv.firstChild) {
                        messageList.insertBefore(tempDiv.firstChild, messageList.firstChild);
                    }

                    // Update skip value
                    button.setAttribute('data-skip', skip + 5);

                    // Hide button if no more messages
                    if (!data.hasMoreMessages) {
                        button.parentElement.style.display = 'none';
                    }

                    // Reset button text
                    button.innerHTML = '<i class="fas fa-chevron-up me-1"></i> {{ __("Muat pesan sebelumnya") }}';
                    button.disabled = false;
                })
                .catch(error => {
                    console.error('Error:', error);
                    button.innerHTML = '<i class="fas fa-chevron-up me-1"></i> {{ __("Muat pesan sebelumnya") }}';
                    button.disabled = false;
                    alert('Error loading messages. Please try again.');
                });
            });
        }

        // AJAX Pagination for threads
        const loadMoreThreadsBtn = document.getElementById('load-more-threads-btn');
        if (loadMoreThreadsBtn) {
            loadMoreThreadsBtn.addEventListener('click', function() {
                const button = this;
                const currentPage = parseInt(button.getAttribute('data-current-page'));
                const totalPages = parseInt(button.getAttribute('data-total-pages'));
                const nextPage = currentPage + 1;

                if (nextPage > totalPages) {
                    return;
                }

                // Show loading indicator
                button.style.display = 'none';
                const loadingIndicator = document.getElementById('threads-loading-indicator');
                if (loadingIndicator) {
                    loadingIndicator.classList.remove('d-none');
                }

                // Make AJAX request
                fetch('{{ route("messages.threads.ajax") }}', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': '{{ csrf_token() }}'
                    },
                    body: JSON.stringify({
                        page: nextPage,
                        unread: '{{ $unreadOnly ? "1" : "0" }}'
                    })
                })
                .then(response => response.json())
                .then(data => {
                    // Append new threads
                    const messageListContainer = document.getElementById('message-list-container');
                    const tempDiv = document.createElement('div');
                    tempDiv.innerHTML = data.html;

                    // Append all child nodes
                    while (tempDiv.firstChild) {
                        messageListContainer.appendChild(tempDiv.firstChild);
                    }

                    // Update button data
                    button.setAttribute('data-current-page', nextPage);

                    // Hide loading indicator
                    if (loadingIndicator) {
                        loadingIndicator.classList.add('d-none');
                    }

                    // Show button if there are more pages
                    if (nextPage < data.totalPages) {
                        button.style.display = 'block';
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('Error loading messages. Please try again.');

                    // Hide loading indicator and show button
                    if (loadingIndicator) {
                        loadingIndicator.classList.add('d-none');
                    }
                    button.style.display = 'block';
                });
            });
        }

        // Reply to message
        const replyForm = document.getElementById('reply-form');
        if (replyForm) {
            replyForm.addEventListener('submit', function(e) {
                e.preventDefault();

                const form = this;
                const threadId = form.getAttribute('data-thread');
                const messageInput = document.getElementById('reply-message');
                const message = messageInput.value.trim();

                if (!message && !currentAttachment) {
                    alert('{{ __("Silakan ketik pesan atau lampirkan file.") }}');
                    return;
                }

                const submitButton = form.querySelector('button[type="submit"]');
                submitButton.disabled = true;
                submitButton.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i> <span class="d-none d-md-inline">{{ __("Mengirim") }}</span>';

                const formData = new FormData();
                formData.append('thread_id', threadId);
                formData.append('message', message);

                if (currentAttachment) {
                    formData.append('attachment', currentAttachment);
                    formData.append('attachment_type', currentAttachmentType);
                }

                fetch('{{ route("messages.reply") }}', {
                    method: 'POST',
                    headers: {
                        'X-CSRF-TOKEN': '{{ csrf_token() }}'
                    },
                    body: formData
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        // Append new message
                        const messageList = document.getElementById('message-list');
                        const tempDiv = document.createElement('div');
                        tempDiv.innerHTML = data.message;

                        // Append the new message
                        while (tempDiv.firstChild) {
                            messageList.appendChild(tempDiv.firstChild);
                        }

                        // Clear input
                        messageInput.value = '';

                        // Clear attachment
                        if (currentAttachmentType === 'document' && fileAttachment) {
                            fileAttachment.value = '';
                        } else if (currentAttachmentType === 'image' && imageAttachment) {
                            imageAttachment.value = '';
                        }

                        currentAttachment = null;
                        currentAttachmentType = null;
                        attachmentPreview.classList.add('d-none');

                        // Scroll to bottom
                        messageContent.scrollTop = messageContent.scrollHeight;
                    } else {
                        alert('Error: ' + data.error);
                    }

                    // Reset button
                    submitButton.disabled = false;
                    submitButton.innerHTML = '<i class="fas fa-paper-plane me-1"></i> <span class="d-none d-md-inline">{{ __("Kirim") }}</span>';
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('Error sending message. Please try again.');

                    // Reset button
                    submitButton.disabled = false;
                    submitButton.innerHTML = '<i class="fas fa-paper-plane me-1"></i> <span class="d-none d-md-inline">{{ __("Kirim") }}</span>';
                });
            });

            // Enter to submit, Shift+Enter for new line
            const replyMessage = document.getElementById('reply-message');
            if (replyMessage) {
                replyMessage.addEventListener('keydown', function(e) {
                    if (e.key === 'Enter' && !e.shiftKey) {
                        e.preventDefault();
                        replyForm.dispatchEvent(new Event('submit'));
                    }
                });
            }
        }

        // Long press for mobile tooltip
        const touchButtons = document.querySelectorAll('.btn-sm[data-bs-toggle="tooltip"]');
        touchButtons.forEach(button => {
            let longPressTimer;

            button.addEventListener('touchstart', function(e) {
                longPressTimer = setTimeout(() => {
                    const tooltip = bootstrap.Tooltip.getInstance(button);
                    if (tooltip) {
                        tooltip.show();
                        setTimeout(() => tooltip.hide(), 2000);
                    }
                }, 500);
            });

            button.addEventListener('touchend', function(e) {
                clearTimeout(longPressTimer);
            });

            button.addEventListener('touchcancel', function(e) {
                clearTimeout(longPressTimer);
            });
        });

        // PDF Preview
        const pdfPreviewModal = document.getElementById('pdfPreviewModal');
        if (pdfPreviewModal) {
            const pdfPreviewModalInstance = new bootstrap.Modal(pdfPreviewModal);
            const pdfPreviewFrame = document.getElementById('pdfPreviewFrame');
            const pdfFileName = document.getElementById('pdfFileName');
            const pdfDownloadLink = document.getElementById('pdfDownloadLink');

            document.addEventListener('click', function(e) {
                if (e.target.classList.contains('pdf-preview-btn') || e.target.closest('.pdf-preview-btn')) {
                    const button = e.target.classList.contains('pdf-preview-btn') ? e.target : e.target.closest('.pdf-preview-btn');
                    const pdfUrl = button.getAttribute('data-pdf-url');
                    const pdfName = button.getAttribute('data-pdf-name');

                    console.log('PDF Preview clicked:', pdfUrl, pdfName);

                    if (pdfUrl && pdfPreviewFrame) {
                        // Fix URL if needed (remove any double slashes except after protocol)
                        const fixedUrl = pdfUrl.replace(/(https?:\/\/)|(\/)+/g, function(match, protocol) {
                            if (protocol) return protocol;
                            return '/';
                        });

                        console.log('Loading PDF from URL:', fixedUrl);
                        pdfPreviewFrame.src = fixedUrl;

                        if (pdfFileName) {
                            pdfFileName.textContent = pdfName;
                        }
                        if (pdfDownloadLink) {
                            pdfDownloadLink.href = fixedUrl;
                            pdfDownloadLink.setAttribute('download', pdfName || 'dokumen.pdf');
                        }
                        pdfPreviewModalInstance.show();
                    }
                }
            });

            // Clear iframe when modal is closed
            pdfPreviewModal.addEventListener('hidden.bs.modal', function() {
                if (pdfPreviewFrame) {
                    pdfPreviewFrame.src = '';
                }
            });
        }

        // Image Lightbox
        const imageLightboxModal = document.getElementById('imageLightboxModal');
        if (imageLightboxModal) {
            const imageLightboxModalInstance = new bootstrap.Modal(imageLightboxModal);
            const lightboxImage = document.getElementById('lightboxImage');
            const lightboxCaption = document.getElementById('lightboxCaption');
            const imageDownloadLink = document.getElementById('imageDownloadLink');

            document.addEventListener('click', function(e) {
                if (e.target.closest('.image-lightbox')) {
                    e.preventDefault();
                    const link = e.target.closest('.image-lightbox');
                    const imageUrl = link.getAttribute('href');
                    const caption = link.getAttribute('data-caption');

                    console.log('Image Lightbox clicked:', imageUrl, caption);

                    if (imageUrl && lightboxImage) {
                        // Fix URL if needed (remove any double slashes except after protocol)
                        const fixedUrl = imageUrl.replace(/(https?:\/\/)|(\/)+/g, function(match, protocol) {
                            if (protocol) return protocol;
                            return '/';
                        });

                        console.log('Loading image from URL:', fixedUrl);
                        lightboxImage.src = fixedUrl;

                        if (lightboxCaption) {
                            lightboxCaption.textContent = caption || '';
                        }
                        if (imageDownloadLink) {
                            imageDownloadLink.href = fixedUrl;
                            imageDownloadLink.setAttribute('download', caption || 'image.jpg');
                        }
                        imageLightboxModalInstance.show();
                    }
                }
            });
        }
    });
</script>
@endpush
