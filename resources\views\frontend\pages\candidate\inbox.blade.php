@extends('frontend.layouts.app')

@section('title', __('<PERSON>esan'))

@section('main')
    <div class="dashboard-wrapper">
        <div class="container">
            <div class="row">
                {{-- Sidebar --}}
                <x-website.candidate.sidebar />

                <div class="col-lg-9">
                    <div class="dashboard-right tw-ps-0 lg:tw-ps-5">
                        <div class="dashboard-right-header">
                            <span class="sidebar-open-nav">
                                <i class="ph-list"></i>
                            </span>
                        </div>
                        <h2 class="tw-text-2xl tw-font-medium tw-text-[#18191C] tw-mb-8">
                            {{ __('Pesan') }}
                        </h2>

                        <div class="support-ticket-container">
                            <div class="row g-0">
                                <!-- Message Detail Section -->
                                <div class="col-12" id="message-detail-section" style="display: none;">
                                    <div class="card">
                                        <div class="card-header d-flex justify-content-between align-items-center">
                                            <h5 id="message-detail-title">{{ __('Detail Pesan') }}</h5>
                                            <button type="button" class="btn btn-outline-secondary btn-sm" id="back-to-list">
                                                <i class="fas fa-arrow-left me-1"></i> {{ __('Kembali ke Daftar Pesan') }}
                                            </button>
                                        </div>
                                        <div class="card-body p-0">
                                            <div id="message-container">
                                                <div class="text-center p-3 position-absolute w-100" id="message-loading" style="top: 0; left: 0; z-index: 1000; background-color: rgba(255, 255, 255, 0.9);">
                                                    <div class="d-flex justify-content-center">
                                                        <div class="ph-briefcase loading-icon"></div>
                                                    </div>
                                                    <p class="mt-2">{{ __('Memuat pesan...') }}</p>
                                                </div>
                                                @livewire('message.message-detail', ['initialThreadId' => $threadId ?? null])
                                                @livewire('message.message-reply')
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Message List -->
                                <div class="col-12" id="message-list-section">
                                    <div class="card">
                                        <div class="card-header d-flex justify-content-between align-items-center">
                                            <h5>{{ __('Daftar Pesan') }}</h5>
                                            <div class="filter-buttons">
                                                <button class="btn btn-sm btn-outline-secondary me-2" id="refresh-messages">
                                                    <i class="ph-arrows-clockwise"></i> {{ __('Refresh') }}
                                                </button>
                                                <button class="btn btn-sm btn-outline-secondary" id="toggle-unread">
                                                    <i class="ph-envelope"></i> {{ __('Belum Dibaca') }}
                                                </button>
                                            </div>
                                        </div>
                                        <div class="card-body">
                                            <div id="message-list-container">
                                                @livewire('message.message-list', ['initialThreadId' => $threadId ?? null])
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="dashboard-footer text-center body-font-4 text-gray-500">
            <x-website.footer-copyright />
        </div>
    </div>
@endsection

@push('styles')
<style>
    .support-ticket-container {
        background-color: #fff;
        border-radius: 10px;
        box-shadow: 0 0 20px rgba(0, 0, 0, 0.05);
        overflow: hidden;
    }

    #message-detail-section {
        transition: all 0.3s ease;
    }

    #message-detail-section .card {
        border-radius: 10px;
        box-shadow: 0 0 15px rgba(0, 0, 0, 0.05);
        border: 2px solid #138C79;
    }

    #message-detail-section .card-header {
        background-color: #f8f9fa;
        padding: 15px 20px;
        border-bottom: 2px solid #138C79;
    }

    #message-detail-section .card-header h5 {
        margin-bottom: 0;
        color: #333;
        font-weight: 600;
    }

    #message-list-section .card {
        border-radius: 10px;
        box-shadow: 0 0 15px rgba(0, 0, 0, 0.05);
        border: 2px solid #138C79;
    }

    #message-list-section .card-header {
        background-color: #f8f9fa;
        padding: 15px 20px;
        border-bottom: 2px solid #138C79;
    }

    #message-list-section .card-header h5 {
        margin-bottom: 0;
        color: #333;
        font-weight: 600;
    }

    .message-table {
        margin-bottom: 0;
    }

    .message-table tr.unread {
        font-weight: bold;
        background-color: rgba(19, 140, 121, 0.05);
    }

    .message-table tr:hover {
        background-color: rgba(19, 140, 121, 0.1);
    }

    .message-item {
        border-bottom: 1px solid #eee;
        padding: 15px;
        transition: all 0.2s ease;
    }

    .message-item:hover {
        background-color: rgba(19, 140, 121, 0.05);
    }

    .message-item.unread {
        background-color: rgba(19, 140, 121, 0.05);
    }

    .message-item .avatar {
        width: 50px;
        height: 50px;
        border-radius: 50%;
        overflow: hidden;
    }

    .message-item .message-content {
        flex: 1;
    }

    .message-item .message-title {
        font-weight: 600;
        margin-bottom: 5px;
    }

    .message-item .message-preview {
        color: #6c757d;
        font-size: 0.9rem;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        max-width: 300px;
    }

    .message-item .message-time {
        font-size: 0.8rem;
        color: #6c757d;
    }

    .message-item .message-status {
        font-size: 0.8rem;
    }

    .message-item .badge {
        font-size: 0.75rem;
    }

    .pagination-container {
        padding: 15px;
        border-top: 1px solid #eee;
    }

    .loading-icon {
        font-size: 2rem;
        animation: bounce 1s infinite;
    }

    @keyframes bounce {
        0%, 100% {
            transform: translateY(0);
        }
        50% {
            transform: translateY(-10px);
        }
    }

    @media (max-width: 991.98px) {
        .support-ticket-container .col-lg-4 {
            border-right: none !important;
            border-bottom: 1px solid #dee2e6;
        }

        .support-ticket-container .d-flex.flex-column {
            height: auto !important;
        }
    }
</style>
@endpush

@push('scripts')
<script>
    // Check if there's a thread ID in the URL and show the detail section
    document.addEventListener('DOMContentLoaded', function() {
        const urlParams = new URLSearchParams(window.location.search);
        const threadId = urlParams.get('pesan_id');
        const page = urlParams.get('page') || 1;
        const unreadOnly = urlParams.get('unread') === '1';
        const jobFilter = urlParams.get('job');

        // Initialize state
        window.messageState = {
            currentPage: parseInt(page),
            unreadOnly: unreadOnly,
            jobFilter: jobFilter,
            isLoading: false
        };

        // Setup event listeners
        document.getElementById('back-to-list').addEventListener('click', function() {
            hideMessageDetail();
        });

        document.getElementById('refresh-messages').addEventListener('click', function() {
            refreshMessages();
        });

        document.getElementById('toggle-unread').addEventListener('click', function() {
            toggleUnreadFilter();
        });

        // Update UI based on initial state
        if (unreadOnly) {
            document.getElementById('toggle-unread').classList.remove('btn-outline-secondary');
            document.getElementById('toggle-unread').classList.add('btn-primary');
            document.getElementById('toggle-unread').innerHTML = '<i class="ph-envelope-open"></i> ' + "{{ __('Semua Pesan') }}";
        }

        if (threadId) {
            setTimeout(function() {
                showMessageDetail();
            }, 500);
        }

        // Setup pagination click handlers
        setupPaginationHandlers();
    });

    // Function to setup pagination handlers
    function setupPaginationHandlers() {
        document.querySelectorAll('.pagination .page-link').forEach(link => {
            link.addEventListener('click', function(e) {
                e.preventDefault();
                const page = this.getAttribute('data-page');
                if (page) {
                    loadPage(page);
                }
            });
        });
    }

    // Function to load a specific page via AJAX
    function loadPage(page) {
        if (window.messageState.isLoading) return;

        window.messageState.isLoading = true;
        window.messageState.currentPage = parseInt(page);

        // Show loading indicator
        const listContainer = document.getElementById('message-list-container');
        listContainer.innerHTML = `
            <div class="text-center p-4">
                <div class="d-flex justify-content-center">
                    <div class="ph-briefcase loading-icon"></div>
                </div>
                <p class="mt-2">{{ __('Memuat pesan...') }}</p>
            </div>
        `;

        // Update URL
        updateUrlParameters();

        // Call Livewire method to load page
        Livewire.emit('gotoPage', page);

        // Reset loading state after a delay
        setTimeout(() => {
            window.messageState.isLoading = false;
            setupPaginationHandlers();
        }, 1000);

        // Scroll to top of list
        document.getElementById('message-list-section').scrollIntoView({ behavior: 'smooth' });
    }

    // Function to refresh messages
    function refreshMessages() {
        if (window.messageState.isLoading) return;

        window.messageState.isLoading = true;

        // Show loading indicator
        const listContainer = document.getElementById('message-list-container');
        listContainer.innerHTML = `
            <div class="text-center p-4">
                <div class="d-flex justify-content-center">
                    <div class="ph-briefcase loading-icon"></div>
                </div>
                <p class="mt-2">{{ __('Memuat pesan...') }}</p>
            </div>
        `;

        // Call Livewire method to refresh
        Livewire.emit('refreshMessageList');

        // Reset loading state after a delay
        setTimeout(() => {
            window.messageState.isLoading = false;
            setupPaginationHandlers();
        }, 1000);
    }

    // Function to toggle unread filter
    function toggleUnreadFilter() {
        if (window.messageState.isLoading) return;

        window.messageState.isLoading = true;
        window.messageState.unreadOnly = !window.messageState.unreadOnly;
        window.messageState.currentPage = 1;

        // Update button appearance
        const button = document.getElementById('toggle-unread');
        if (window.messageState.unreadOnly) {
            button.classList.remove('btn-outline-secondary');
            button.classList.add('btn-primary');
            button.innerHTML = '<i class="ph-envelope-open"></i> ' + "{{ __('Semua Pesan') }}";
        } else {
            button.classList.remove('btn-primary');
            button.classList.add('btn-outline-secondary');
            button.innerHTML = '<i class="ph-envelope"></i> ' + "{{ __('Belum Dibaca') }}";
        }

        // Show loading indicator
        const listContainer = document.getElementById('message-list-container');
        listContainer.innerHTML = `
            <div class="text-center p-4">
                <div class="d-flex justify-content-center">
                    <div class="ph-briefcase loading-icon"></div>
                </div>
                <p class="mt-2">{{ __('Memuat pesan...') }}</p>
            </div>
        `;

        // Update URL
        updateUrlParameters();

        // Call Livewire method to toggle filter
        Livewire.emit('toggleUnreadFilter');

        // Reset loading state after a delay
        setTimeout(() => {
            window.messageState.isLoading = false;
            setupPaginationHandlers();
        }, 1000);
    }

    // Function to update URL parameters
    function updateUrlParameters() {
        const url = new URL(window.location);
        url.searchParams.set('page', window.messageState.currentPage);

        if (window.messageState.unreadOnly) {
            url.searchParams.set('unread', '1');
        } else {
            url.searchParams.delete('unread');
        }

        if (window.messageState.jobFilter) {
            url.searchParams.set('job', window.messageState.jobFilter);
        } else {
            url.searchParams.delete('job');
        }

        window.history.pushState({}, '', url);
    }

    // Function to show message detail
    function showMessageDetail() {
        document.getElementById('message-detail-section').style.display = 'block';
        document.getElementById('message-list-section').style.display = 'none';
        document.getElementById('message-loading').style.display = 'block';

        // Hide loading after a short delay
        setTimeout(function() {
            document.getElementById('message-loading').style.display = 'none';
        }, 1000);
    }

    // Function to hide message detail
    function hideMessageDetail() {
        document.getElementById('message-detail-section').style.display = 'none';
        document.getElementById('message-list-section').style.display = 'block';

        // Remove thread ID from URL
        const url = new URL(window.location);
        url.searchParams.delete('pesan_id');
        window.history.pushState({}, '', url);

        // Reload the page to reset the state completely
        window.location.reload();
    }

    // Livewire event listeners
    window.addEventListener('updateUrl', event => {
        const threadId = event.detail.threadId;
        const url = new URL(window.location);
        url.searchParams.set('pesan_id', threadId);
        window.history.pushState({}, '', url);
    });

    window.addEventListener('openMessageModal', event => {
        // Show message detail section
        showMessageDetail();

        // Scroll to top
        window.scrollTo({ top: 0, behavior: 'smooth' });
    });

    window.addEventListener('paginationUpdated', event => {
        // Setup pagination handlers again after Livewire updates the DOM
        setupPaginationHandlers();
    });

    window.addEventListener('jobFilterUpdated', event => {
        window.messageState.jobFilter = event.detail.jobId;
        updateUrlParameters();
    });

    // Show toast messages
    window.addEventListener('showToast', event => {
        const { type, message } = event.detail;

        // Create toast element
        const toastId = 'toast-' + Date.now();
        const toastHtml = `
            <div id="${toastId}" class="toast align-items-center text-white bg-${type} border-0" role="alert" aria-live="assertive" aria-atomic="true">
                <div class="d-flex">
                    <div class="toast-body">
                        ${message}
                    </div>
                    <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast" aria-label="Close"></button>
                </div>
            </div>
        `;

        // Check if toast container exists, if not create it
        let toastContainer = document.querySelector('.toast-container');
        if (!toastContainer) {
            toastContainer = document.createElement('div');
            toastContainer.className = 'toast-container position-fixed bottom-0 end-0 p-3';
            document.body.appendChild(toastContainer);
        }

        // Add toast to container
        toastContainer.innerHTML += toastHtml;

        // Initialize and show toast
        const toastElement = document.getElementById(toastId);
        const toast = new bootstrap.Toast(toastElement, { delay: 5000 });
        toast.show();

        // Remove toast after it's hidden
        toastElement.addEventListener('hidden.bs.toast', function () {
            toastElement.remove();
        });
    });
</script>
@endpush
