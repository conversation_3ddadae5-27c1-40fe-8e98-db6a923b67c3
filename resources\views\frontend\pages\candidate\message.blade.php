@extends('frontend.layouts.app')

@section('title', __('messenger'))

@section('main')
    <div class="dashboard-wrapper">
        <div class="container">
            <div class="chat-container">
                <div class="row">
                    <div class="col-lg-12">
                        <div class="chat-box-card">
                            <div class="row g-0">
                                <!-- User List Sidebar -->
                                <div class="col-lg-4 border-end">
                                    @livewire('messenger.chat-list')
                                </div>

                                <!-- Chat Area -->
                                <div class="col-lg-8">
                                    <div class="d-flex flex-column" style="height: 964px;">
                                        @livewire('messenger.chat-box')
                                        @livewire('messenger.chat-input')
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="dashboard-footer text-center body-font-4 text-gray-500">
            <x-website.footer-copyright />
        </div>
    </div>
@endsection

@section('css')
    <style>
        .chat-box-card {
            border: 1px solid #E4E5E8;
            height: 964px;
            overflow: hidden;
        }

        .select-job+.select2-container .select2-selection--single .select2-selection__rendered {
            padding-left: 48px;
        }

        .custom-checkbox label {
            position: relative;
            padding-left: 28px;
        }
        .custom-checkbox label::before {
            content: '';
            display: inline-flex;
            justify-content: center;
            align-items: center;
            background: white;
            width: 20px;
            height: 20px;
            border-radius: 3px;
            border: 1px solid #9DC1EB;
            background: var(--gray-00, #FFF);
            position: absolute;
            left: 0px;
            top: 50%;
            transform: translateY(-50%)
        }
        .custom-checkbox:has(input:checked) label::before {
            content: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='12' height='12' viewBox='0 0 12 12' fill='none'%3E%3Cpath d='M10 3.00006L4.5 8.50006L2 6.00006' stroke='white' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'/%3E%3C/svg%3E");
            background: #0A65CC;
        }

        .chat-user-item {
            cursor: pointer;
            transition: all 0.3s ease;
            background-color: #f8f9fa;
        }

        .chat-user-item:hover, .chat-user-item.active {
            background-color: #e9ecef;
        }

        .chat-messages {
            display: flex;
            flex-direction: column;
        }

        .message {
            max-width: 70%;
            margin-bottom: 15px;
            padding: 10px 15px;
            border-radius: 10px;
            position: relative;
        }

        .message.sent {
            align-self: flex-end;
            background-color: #0A65CC;
            color: white;
            border-bottom-right-radius: 0;
        }

        .message.received {
            align-self: flex-start;
            background-color: #E7F0FA;
            color: #333;
            border-bottom-left-radius: 0;
        }

        .message-time {
            font-size: 0.7rem;
            margin-top: 5px;
            opacity: 0.7;
        }

        .message.sent .message-time {
            text-align: right;
        }

        .message.received .message-time {
            text-align: left;
        }

        .emoji-item {
            display: inline-block;
            font-size: 1.2rem;
            width: 30px;
            height: 30px;
            text-align: center;
            transition: all 0.2s ease;
        }

        .emoji-item:hover {
            background-color: #e9ecef;
            border-radius: 5px;
        }

        .attachment-preview {
            max-width: 200px;
            max-height: 200px;
            margin-top: 10px;
            border-radius: 5px;
        }

        .day-separator {
            display: flex;
            align-items: center;
            margin: 20px 0;
            color: #6c757d;
        }

        .day-separator::before, .day-separator::after {
            content: "";
            flex: 1;
            border-bottom: 1px solid #dee2e6;
        }

        .day-separator::before {
            margin-right: 10px;
        }

        .day-separator::after {
            margin-left: 10px;
        }

        #message-input {
            resize: none;
            overflow: hidden;
            min-height: 38px;
            max-height: 100px;
        }

        .message-attachment {
            display: flex;
            align-items: center;
            background-color: rgba(0, 0, 0, 0.05);
            padding: 5px 10px;
            border-radius: 5px;
            margin-top: 5px;
        }

        .message-attachment i {
            margin-right: 5px;
        }

        .message-attachment a {
            color: inherit;
            text-decoration: none;
            word-break: break-all;
        }

        .message-image {
            max-width: 200px;
            max-height: 200px;
            border-radius: 5px;
            cursor: pointer;
        }
        
        .typing-indicator {
            color: #0A65CC;
            font-style: italic;
        }
    </style>
@endsection

@section('frontend_scripts')
<script>
    document.addEventListener('livewire:load', function () {
        // Initialize Select2
        $('.select-job').select2({
            placeholder: "{{ __('all_jobs') }}",
        });
        
        // Handle Select2 changes
        $('.select-job').on('change', function (e) {
            const value = $(this).val();
            Livewire.emit('jobFilterChanged', value);
        });
    });
</script>
@endsection
