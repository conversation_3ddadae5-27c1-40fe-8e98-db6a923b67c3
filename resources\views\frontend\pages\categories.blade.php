@extends('frontend.layouts.app')

@section('description')
    {{ __('Kategori Lowongan Kerja') }}
@endsection
@section('og:image')
    {{ asset('frontend/assets/images/banner/banner-1.png') }}
@endsection
@section('title')
    {{ __('Kategori Lowongan Kerja') }}
@endsection

@section('main')
    <div class="breadcrumbs breadcrumbs-height">
        <div class="container">
            <div class="row align-items-center breadcrumbs-height">
                <div class="col-12 position-relative">
                    <div class="breadcrumb-menu">
                        <h6 class="f-size-18 m-0">{{ __('Kategori Lowongan Kerja') }}</h6>
                        <ul>
                            <li><a href="{{ route('website.home') }}">{{ __('home') }}</a></li>
                            <li>/</li>
                            <li>{{ __('Kategori Lowongan Kerja') }}</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="rt-spacer-100 rt-spacer-md-50"></div>
    <div class="container">
        <div class="row">
            <div class="col-12 text-center">
                <h4 class="tw-mb-4">{{ __('Pilih Kategori Lowongan Kerja') }}</h4>
                <p class="tw-text-gray-600 tw-mb-4">{{ __('Temukan lowongan kerja berdasarkan kategori yang sesuai dengan keahlian Anda') }}</p>

                <!-- Statistics -->
                <div class="tw-flex tw-justify-center tw-gap-8 tw-mb-6">
                    <div class="tw-text-center">
                        <div class="tw-text-2xl tw-font-bold tw-text-primary-500">{{ number_format($total_categories) }}</div>
                        <div class="tw-text-sm tw-text-gray-600">{{ __('Kategori') }}</div>
                    </div>
                    <div class="tw-text-center">
                        <div class="tw-text-2xl tw-font-bold tw-text-primary-500">{{ number_format($total_jobs) }}</div>
                        <div class="tw-text-sm tw-text-gray-600">{{ __('Lowongan Aktif') }}</div>
                    </div>
                </div>

                <!-- Search Box -->
                <div class="tw-max-w-md tw-mx-auto tw-mb-8">
                    <div class="tw-relative">
                        <input type="text" id="category-search" class="form-control tw-pl-10 tw-py-3 tw-rounded-lg" placeholder="{{ __('Cari kategori...') }}">
                        <div class="tw-absolute tw-left-3 tw-top-1/2 tw-transform tw--translate-y-1/2 tw-text-gray-500">
                            <i class="ph-magnifying-glass"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="row g-4" id="category-container">
            @foreach ($categories as $category)
                <div class="col-xl-3 col-lg-4 col-md-6">
                    <a href="{{ route('website.job.category.slug', ['category' => $category->slug]) }}" class="tw-block tw-h-full">
                        <div class="tw-bg-white tw-rounded-lg tw-shadow-sm tw-border tw-border-gray-200 tw-p-6 tw-h-full tw-transition-all tw-duration-300 hover:tw-shadow-md hover:tw-border-primary-500">
                            <div class="tw-flex tw-items-center tw-gap-4 tw-mb-4">
                                <div class="tw-w-12 tw-h-12 tw-flex tw-items-center tw-justify-center tw-bg-primary-50 tw-text-primary-500 tw-rounded-full">
                                    <i class="{{ $category->icon ?? 'ph-briefcase-simple' }} tw-text-xl"></i>
                                </div>
                                <h5 class="tw-text-lg tw-font-medium tw-text-gray-900 tw-mb-0">{{ $category->name }}</h5>
                            </div>
                            <p class="tw-text-gray-600 tw-mb-0">
                                {{ $category->jobs_count }} {{ __('open_positions') }}
                            </p>
                        </div>
                    </a>
                </div>
            @endforeach
        </div>
    </div>
    <div class="rt-spacer-100 rt-spacer-md-50"></div>

    <!-- No Results Message -->
    <div id="no-results" class="tw-hidden tw-text-center tw-py-10">
        <div class="tw-bg-gray-50 tw-rounded-lg tw-p-8 tw-max-w-md tw-mx-auto">
            <div class="tw-text-gray-400 tw-mb-4">
                <i class="ph-magnifying-glass-minus tw-text-5xl"></i>
            </div>
            <h5 class="tw-mb-2">{{ __('Kategori Tidak Ditemukan') }}</h5>
            <p class="tw-text-gray-600">{{ __('Maaf, tidak ada kategori yang cocok dengan pencarian Anda. Silakan coba dengan kata kunci lain.') }}</p>
        </div>
    </div>
@endsection

@section('css')
<style>
    .category-item {
        transition: all 0.3s ease;
    }

    .category-item.hidden {
        display: none;
    }
</style>
@endsection

@section('frontend_scripts')
<script>
    document.addEventListener('DOMContentLoaded', function() {
        const searchInput = document.getElementById('category-search');
        const categoryItems = document.querySelectorAll('#category-container > div');
        const noResults = document.getElementById('no-results');

        searchInput.addEventListener('input', function() {
            const searchTerm = this.value.toLowerCase().trim();
            let matchCount = 0;

            categoryItems.forEach(item => {
                const categoryName = item.querySelector('h5').textContent.toLowerCase();

                if (categoryName.includes(searchTerm)) {
                    item.classList.remove('tw-hidden');
                    matchCount++;
                } else {
                    item.classList.add('tw-hidden');
                }
            });

            // Show/hide no results message
            if (matchCount === 0) {
                noResults.classList.remove('tw-hidden');
            } else {
                noResults.classList.add('tw-hidden');
            }
        });
    });
</script>
@endsection
