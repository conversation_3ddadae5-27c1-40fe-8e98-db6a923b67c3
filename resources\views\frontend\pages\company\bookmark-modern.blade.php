@extends('frontend.layouts.app')

@section('title', __('bookmarks'))

@section('main')
    <div class="dashboard-wrapper">
        <div class="container">
            <div class="row">
                {{-- Sidebar --}}
                <x-website.company.sidebar />

                <div class="col-lg-9">
                    <div class="dashboard-right tw-ps-0 lg:tw-ps-3">
                        <div class="bookmark-header tw-bg-white tw-rounded-lg tw-shadow-sm tw-p-4 tw-mb-4">
                            <div class="row d-flex tw-flex-wrap tw-items-center">
                                <div class="col-sm-12 col-md-6">
                                    <h3 class="tw-text-xl tw-font-semibold tw-mb-0">
                                        {{ __('bookmarks') }}
                                        <span class="tw-text-gray-500 tw-text-base" id="bookmark-count">({{ $bookmarks->total() }})</span>
                                    </h3>
                                </div>
                                <div class="col-sm-12 col-md-6 d-flex tw-flex-wrap tw-gap-3 tw-justify-end">
                                    <div class="tw-flex tw-items-center tw-gap-2">
                                        <label for="category-select" class="tw-text-sm tw-font-medium tw-mb-0">{{ __('filter') }}:</label>
                                        <select id="category-select" name="category" class="form-select tw-rounded-md tw-border-gray-300 tw-shadow-sm">
                                            <option value="all">{{ __('all') }}</option>
                                            @foreach ($categories as $cat)
                                                <option {{ old('category', request()->query('category')) == $cat->id ? 'selected' : '' }}
                                                    value="{{ $cat->id }}">{{ $cat->name }}</option>
                                            @endforeach
                                        </select>
                                    </div>
                                    <button type="button" id="add-category-btn" class="btn btn-primary tw-rounded-md tw-shadow-sm tw-flex tw-items-center tw-gap-2">
                                        <i class="ph-plus"></i>
                                        {{ __('Tambah Kategori') }}
                                    </button>
                                    <div class="sidebar-open-nav !tw-mt-0 d-md-none">
                                        <i class="ph-list"></i>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Loading Indicator -->
                        <div id="loading-indicator" class="tw-hidden tw-flex tw-justify-center tw-items-center tw-py-8">
                            <div class="tw-flex tw-flex-col tw-items-center">
                                <div class="tw-animate-spin tw-rounded-full tw-h-12 tw-w-12 tw-border-t-2 tw-border-b-2 tw-border-primary-500"></div>
                                <p class="tw-mt-3 tw-text-gray-600">Memuat data...</p>
                            </div>
                        </div>

                        <!-- Bookmark Content Container -->
                        <div id="bookmark-content" class="bookmark-content">
                            @include('frontend.pages.company.partials._bookmark_candidates', ['bookmarks' => $bookmarks])
                        </div>

                        <!-- Pagination Container -->
                        <div id="pagination-container" class="tw-mt-4">
                            {{ $bookmarks->links('vendor.pagination.frontend-ajax-generic') }}
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Modal Tambah Kategori -->
        <div class="modal fade" id="addCategoryModal" tabindex="-1" aria-labelledby="addCategoryModalLabel" aria-hidden="true">
            <div class="modal-dialog modal-dialog-centered">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title" id="addCategoryModalLabel">{{ __('Tambah Kategori Bookmark') }}</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                    <div class="modal-body">
                        <form id="addCategoryForm">
                            <div class="mb-3">
                                <label for="category_name" class="form-label">{{ __('Nama Kategori') }}</label>
                                <input type="text" class="form-control" id="category_name" name="name" required>
                                <div class="invalid-feedback" id="category-name-error"></div>
                            </div>
                        </form>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">{{ __('Batal') }}</button>
                        <button type="button" id="saveCategoryBtn" class="btn btn-primary">{{ __('Simpan') }}</button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Modal Kandidat -->
        <x-website.modal.candidate-profile-modal />
    </div>
@endsection

@section('frontend_links')
    <style>
        /* Modern styling for bookmark page */
        .bookmark-header {
            border-left: 4px solid #138C79;
        }

        .candidate-card {
            transition: all 0.3s ease;
            height: 100%;
            display: flex;
            flex-direction: column;
        }

        .candidate-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05) !important;
        }

        .candidate-avatar img {
            object-fit: cover;
            border: 2px solid #f8f9fa;
            width: 64px;
            height: 64px;
        }

        /* Pagination styling */
        .pagination {
            gap: 0.5rem;
            justify-content: center;
            flex-wrap: wrap;
        }

        .page-item .page-link {
            border-radius: 0.5rem;
            border: 1px solid #e2e8f0;
            color: #4a5568;
            padding: 0.5rem 0.75rem;
            min-width: 40px;
            text-align: center;
        }

        .page-item.active .page-link {
            background-color: #138C79;
            border-color: #138C79;
            color: white;
        }

        .page-item .page-link:hover {
            background-color: #f7fafc;
        }

        /* Loading indicator styling */
        #loading-indicator {
            min-height: 200px;
        }

        /* Modal styling */
        #addCategoryModal .modal-content {
            border-radius: 0.5rem;
            border: none;
        }

        #addCategoryModal .modal-header {
            border-bottom: 2px solid #138C79;
            padding: 1rem 1.5rem;
        }

        #addCategoryModal .modal-footer {
            border-top: none;
            padding: 1rem 1.5rem 1.5rem;
        }

        /* Responsive adjustments */
        @media (max-width: 768px) {
            .bookmark-header {
                padding: 1rem;
            }

            .bookmark-header .row {
                row-gap: 1rem;
            }

            .bookmark-header .col-sm-12.col-md-6:last-child {
                justify-content: space-between;
            }

            .candidate-card {
                margin-bottom: 1rem;
            }

            .candidate-actions {
                flex-wrap: wrap;
                row-gap: 0.5rem;
            }

            .candidate-actions .btn {
                font-size: 0.875rem;
                padding: 0.375rem 0.75rem;
            }

            .candidate-avatar img {
                width: 56px;
                height: 56px;
            }

            .candidate-info h4 {
                font-size: 1rem;
            }

            /* Adjust filter and add category button on mobile */
            .tw-flex.tw-items-center.tw-gap-2 {
                width: 100%;
            }

            #category-select {
                flex-grow: 1;
            }

            #add-category-btn {
                padding: 0.5rem 0.75rem;
                font-size: 0.875rem;
            }
        }

        /* Small mobile devices */
        @media (max-width: 576px) {
            .bookmark-header h3 {
                font-size: 1.25rem;
            }

            .candidate-card .tw-p-4 {
                padding: 0.75rem !important;
            }

            .candidate-card .tw-gap-4 {
                gap: 0.75rem !important;
            }

            .candidate-avatar img {
                width: 48px;
                height: 48px;
            }
        }
    </style>
@endsection

@section('script')
    <script>
        $(document).ready(function() {
            // Inisialisasi variabel untuk menyimpan halaman saat ini
            let currentPage = 1;
            let currentCategory = 'all';

            // Event listener untuk filter kategori
            $('#category-select').on('change', function() {
                currentCategory = $(this).val();
                loadBookmarks(1, currentCategory);
            });

            // Event listener untuk pagination
            $(document).on('click', '.pagination .page-link', function(e) {
                e.preventDefault();

                // Ambil halaman dari href
                const href = $(this).attr('href');
                if (href) {
                    const url = new URL(href);
                    currentPage = url.searchParams.get('page') || 1;

                    // Load data dengan halaman baru
                    loadBookmarks(currentPage, currentCategory);

                    // Scroll ke atas halaman
                    $('html, body').animate({
                        scrollTop: $('#bookmark-content').offset().top - 100
                    }, 500);
                }
            });

            // Event listener untuk tombol tambah kategori
            $('#add-category-btn').on('click', function() {
                $('#addCategoryModal').modal('show');
            });

            // Prevent form submission on Enter key press
            $('#addCategoryForm').on('submit', function(e) {
                e.preventDefault();
                $('#saveCategoryBtn').click();
            });

            // Handle Enter key press in category name input
            $('#category_name').on('keypress', function(e) {
                if (e.which === 13) { // Enter key
                    e.preventDefault();
                    $('#saveCategoryBtn').click();
                }
            });

            // Event listener untuk tombol simpan kategori
            $('#saveCategoryBtn').on('click', function() {
                const categoryName = $('#category_name').val();

                if (!categoryName) {
                    $('#category-name-error').text('Nama kategori tidak boleh kosong').show();
                    return;
                }

                // Tampilkan loading state
                $(this).html('<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> Menyimpan...');
                $(this).prop('disabled', true);

                // Kirim data kategori baru
                $.ajax({
                    url: "{{ route('company.bookmark.category.store') }}",
                    type: "POST",
                    data: {
                        name: categoryName,
                        _token: "{{ csrf_token() }}"
                    },
                    success: function(response) {
                        // Reset form
                        $('#category_name').val('');

                        // Sembunyikan modal
                        $('#addCategoryModal').modal('hide');

                        // Tampilkan pesan sukses
                        toastr.success('Kategori berhasil ditambahkan');

                        // Refresh dropdown kategori
                        refreshCategoryDropdown();
                    },
                    error: function(xhr) {
                        // Tampilkan pesan error
                        if (xhr.responseJSON && xhr.responseJSON.errors) {
                            $('#category-name-error').text(xhr.responseJSON.errors.name[0]).show();
                        } else {
                            toastr.error('Terjadi kesalahan saat menyimpan kategori');
                        }
                    },
                    complete: function() {
                        // Reset tombol
                        $('#saveCategoryBtn').html('{{ __("Simpan") }}');
                        $('#saveCategoryBtn').prop('disabled', false);
                    }
                });
            });

            // Fungsi untuk memuat data bookmark dengan AJAX
            function loadBookmarks(page = 1, category = 'all') {
                // Tampilkan loading indicator
                $('#loading-indicator').removeClass('tw-hidden');
                $('#bookmark-content').addClass('tw-opacity-50');

                // Buat URL dengan parameter
                let url = "{{ route('company.bookmark.data') }}?page=" + page;
                if (category !== 'all') {
                    url += "&category=" + category;
                }

                // Ambil data dengan AJAX
                $.ajax({
                    url: url,
                    type: "GET",
                    success: function(response) {
                        if (response.success) {
                            // Update konten bookmark
                            $('#bookmark-content').html(response.html);

                            // Update pagination
                            $('#pagination-container').html(response.pagination);

                            // Update jumlah bookmark
                            $('#bookmark-count').text('(' + response.total + ')');
                        } else {
                            toastr.error('Terjadi kesalahan saat memuat data');
                        }
                    },
                    error: function() {
                        toastr.error('Terjadi kesalahan saat memuat data');
                    },
                    complete: function() {
                        // Sembunyikan loading indicator
                        $('#loading-indicator').addClass('tw-hidden');
                        $('#bookmark-content').removeClass('tw-opacity-50');
                    }
                });
            }

            // Fungsi untuk refresh dropdown kategori
            function refreshCategoryDropdown() {
                $.ajax({
                    url: "{{ route('company.bookmark.category.index') }}",
                    type: "GET",
                    data: {
                        ajax: true
                    },
                    success: function(response) {
                        // Bersihkan dropdown
                        const dropdown = $('#category-select');
                        dropdown.empty();

                        // Tambahkan opsi "all"
                        dropdown.append('<option value="all">{{ __("all") }}</option>');

                        // Tambahkan kategori-kategori
                        $.each(response, function(index, category) {
                            dropdown.append('<option value="' + category.id + '">' + category.name + '</option>');
                        });
                    }
                });
            }
        });

        // candidate profile modal data by ajax
        function showCandidateProfileModal(username) {
            // Tampilkan modal dengan loading state
            $('#candidate-profile-modal').modal('show');
            $('#candidate_name').text('Loading...');
            $('#candidate_profession').text('Loading...');
            $('#candidate-bio').text('Loading...');

            $.ajax({
                url: "{{ route('website.candidate.application.profile.details') }}",
                type: "GET",
                data: {
                    username: username,
                    count_view: 1
                },
                success: function(response) {
                    if (!response.success) {
                        // Sembunyikan modal jika terjadi error
                        $('#candidate-profile-modal').modal('hide');

                        if (response.redirect_url) {
                            return Swal.fire({
                                title: 'Oops...',
                                text: response.message,
                                icon: 'error',
                                showCancelButton: true,
                                confirmButtonColor: '#3085d6',
                                cancelButtonColor: '#d33',
                                confirmButtonText: "{{ __('upgrade_plan') }}"
                            }).then((result) => {
                                if (result.value) {
                                    window.location.href = response.redirect_url;
                                }
                            })
                        } else {
                            return Swal.fire('Error', response.message, 'error');
                        }
                    }

                    let data = response.data;
                    let social = data.social_info
                    let candidate = response.data.candidate;

                    $('#candidate_id').val(data.candidate.id)
                    if (data.candidate.bookmarked) {
                        $('#removeBookmakCandidate').removeClass('d-none')
                        $('#bookmakCandidate').addClass('d-none')
                    } else {
                        $('#bookmakCandidate').removeClass('d-none')
                        $('#removeBookmakCandidate').addClass('d-none')
                    }

                    data.name ? $('.candidate-profile-info h2').html(data.name) : '';
                    data.bio ? $('.tab-pane p').html(data.bio) : '';
                    data.candidate.photo ? $('#candidate_image').attr("src", data.candidate.photo) : '';
                    data.candidate.profession ? $('.candidate-profile-info h4').html(capitalizeFirstLetter(data
                        .candidate.profession.name)) : '';
                    data.candidate.bio ? $('.biography p').html(data.candidate.bio) : '';

                    if (data.candidate.status == 'available') {
                       $('.candidate-profile-info h6').removeClass('d-none');
                    }

                    //social media Link
                    candidate.social_info[0] ? $('#facebook').attr('href', candidate.social_info[0]['url']) :
                        '';
                    candidate.social_info[1] ? $('#twitter').attr('href', candidate.social_info[1]['url']) : '';
                    candidate.social_info[2] ? $('#youtube').attr('href', candidate.social_info[2]['url']) : '';
                    candidate.social_info[3] ? $('#linkedin').attr('href', candidate.social_info[3]['url']) :
                        '';
                    candidate.social_info[4] ? $('#pinterest').attr('href', candidate.social_info[4]['url']) :
                        '';
                    candidate.social_info[5] ? $('#github').attr('href', candidate.social_info[5]['url']) : '';

                    // Social info
                    if (social.facebook || social.twitter || social.linkedin || social.youtube || social
                        .instagram) {
                        $('#candidate_social_profile_modal').show()
                        social.facebook ? $('.social-media ul li:nth-child(1)').attr("href", social.facebook) :
                            '';
                        social.twitter ? $('.social-media ul li:nth-child(2)').attr("href", social.twitter) :
                            '';
                        social.linkedin ? $('.social-media ul li:nth-child(3)').attr("href", social.linkedin) :
                            '';
                        social.instagram ? $('.social-media ul li:nth-child(4)').attr("href", social
                                .instagram) :
                            '';
                        social.youtube ? $('.social-media ul li:nth-child(5)').attr("href", social.youtube) :
                            '';

                    } else {
                        $('#candidate_social_profile_modal').hide()
                    }

                    // skills & languages
                    if (candidate.skills.length != 0) {
                        $("#candidate_skills span").remove();
                        console.log(123)

                        $.each(candidate.skills, function(key, value) {
                            $('#candidate_skills').append(
                                `<span class="tw-bg-[#E7F0FA] tw-rounded-[4px] tw-text-sm tw-text-[#0A65CC] tw-px-3 tw-py-1.5">
                                    ${value.name}
                                </span>`
                            )
                        });
                    }

                    if (candidate.languages.length != 0) {
                        $("#candidate_languages div").remove();

                        $.each(candidate.languages, function(key, value) {
                            $('#candidate_languages').append(
                                `<div class="tw-inline-block tw-p-3 tw-bg-[#F1F2F4] tw-rounded-[4px]">
                                    <h4 class="tw-text-sm tw-text-[#474C54] tw-font-medium tw-mb-[2px]">${value.name}</h4>
                                </div>`

                            )
                        });
                    }

                    if (candidate.educations.length != 0) {
                        $("#candidate_profile_educations tr").remove();

                        $.each(candidate.educations, function(key, value) {
                            $('#candidate_profile_educations').append(
                                `<tr>
                                    <td>${value.level}</td>
                                    <td>${value.degree}</td>
                                    <td>${value.year}</td>
                                </tr>`
                            )
                        });
                    }else{
                        $('#candidate_profile_educations').append(
                            `<tr>
                                <td>No data found</td>
                            </tr>`
                        )
                    }

                    if (candidate.experiences.length != 0) {
                        $("#candidate_profile_experiences tr").remove();

                        $.each(candidate.experiences, function(key, value) {
                            let formatted_end = value.currently_working ? 'Currently Working' : value.formatted_end

                            $('#candidate_profile_experiences').append(
                                `<tr>
                                    <td>${value.company}</td>
                                    <td>${value.department} / ${value.designation}</td>
                                    <td>${value.formatted_start} - ${formatted_end}</td>
                                </tr>
                                `
                            )
                        });
                    }else{
                        $('#candidate_profile_experiences').append(
                            `<tr>
                                <td>No data found</td>
                            </tr>`
                        )
                    }

                    // other info - Bio (Tentang Saya)
                    if (data.candidate.bio) {
                        $('#candidate-bio').html(data.candidate.bio);
                    } else {
                        $('#candidate-bio').html('Tidak ada informasi tentang kandidat ini.');
                    }

                    // experience info
                    // Clear the experience list container before populating the data
                    $('#experience-list').empty();

                    // Check if experiences array exists and has items
                    if (data.candidate.experiences && data.candidate.experiences.length > 0) {
                        // Loop through the experiences array in reverse order
                        for (let i = data.candidate.experiences.length - 1; i >= 0; i--) {
                            const experience = data.candidate.experiences[i];

                            // Create HTML for each experience
                            const experienceHTML = `
                            <li class="tw-text-sm tw-flex tw-flex-col tw-gap-3">
                                <div class="tw-flex tw-flex-col tw-gap-1">
                                    <p class="tw-m-0 tw-text-sm tw-text-[#0066CC] tw-font-medium -tw-mt-1.5">${experience.formatted_start || ''}</p>
                                    <h4 class="tw-m-0 tw-text-sm tw-text-[#14181A] tw-font-medium">${experience.designation || ''}</h4>
                                    <p class="tw-m-0 tw-text-sm tw-text-[#707A7D]">${experience.company || ''}${experience.department ? '/' + experience.department : ''}</p>
                                </div>
                                <p class="tw-m-0 tw-text-sm tw-text-[#3C4649]">${experience.responsibilities || ''}</p>
                            </li>
                            `;

                            // Append the experience HTML to the list
                            $('#experience-list').append(experienceHTML);
                        }
                    } else {
                        // If no experiences, show a message
                        $('#experience-list').append('<li class="tw-text-sm">Tidak ada data pengalaman</li>');
                    }

                    // education info
                    $('#education-list').empty();

                    // Check if educations array exists and has items
                    if (data.candidate.educations && data.candidate.educations.length > 0) {
                        // Loop through the education array in reverse order
                        for (let i = data.candidate.educations.length - 1; i >= 0; i--) {
                            const education = data.candidate.educations[i];

                            // Create HTML for each education
                            const educationHtml = `
                                <li class="tw-text-sm tw-flex tw-flex-col tw-gap-3">
                                    <div class="tw-flex tw-flex-col tw-gap-1">
                                        <p class="tw-m-0 tw-text-sm tw-text-[#0066CC] tw-font-medium -tw-mt-1.5" id="candidate-edu-year">${education.year || ''}</p>
                                        <h4 class="tw-m-0 tw-text-sm tw-text-[#14181A] tw-font-medium">${education.degree || ''}</h4>
                                        <p class="tw-m-0 tw-text-sm tw-text-[#707A7D]">${education.degree || ''} / ${education.level || ''}</p>
                                    </div>
                                    <p class="tw-m-0 tw-text-sm tw-text-[#3C4649]">${education.notes || ''}</p>
                                </li>
                            `;

                            // Append the education HTML to the list
                            $('#education-list').append(educationHtml);
                        }
                    } else {
                        // If no educations, show a message
                        $('#education-list').append('<li class="tw-text-sm">Tidak ada data pendidikan</li>');
                    }

                    data.candidate.birth_date ? $('#candidate_birth_date').html(data.candidate.birth_date) : '';
                    // Selalu tampilkan Indonesia sebagai nationality
                    $('#candidate_nationality').html('Indonesia');

                    // Terjemahkan marital status ke Bahasa Indonesia
                    if (data.candidate.marital_status) {
                        const maritalStatusTranslated = data.candidate.marital_status.toLowerCase() === 'married' ? 'Menikah' :
                                                      (data.candidate.marital_status.toLowerCase() === 'single' ? 'Belum Menikah' :
                                                      capitalizeFirstLetter(data.candidate.marital_status));
                        $('#candidate_marital_status').html(maritalStatusTranslated);
                    }

                    // Terjemahkan gender ke Bahasa Indonesia
                    if (data.candidate.gender) {
                        const genderTranslated = data.candidate.gender.toLowerCase() === 'male' ? 'Laki-laki' :
                                               (data.candidate.gender.toLowerCase() === 'female' ? 'Perempuan' :
                                               capitalizeFirstLetter(data.candidate.gender));
                        $('#candidate_gender').html(genderTranslated);
                    }
                    data.candidate.experience ? $('#candidate_experience').html(data.candidate.experience
                        .name) : ''
                    data.candidate.education ? $('#candidate_education').html(capitalizeFirstLetter(data
                        .candidate.education.name)) : ''

                    if (data.candidate.website) {
                        $('#candidate_website').attr('href', data.candidate.website);
                        $('#candidate_website').html(data.candidate.website);
                    }
                    $('#candidate_location').html(data.candidate.exact_location ? data.candidate
                        .exact_location : data.candidate.full_address)

                    data.contact_info && data.contact_info.phone ? $('#candidate_phone').html(data.contact_info
                        .phone) : ''
                    data.contact_info && data.contact_info.secondary_phone ? $('#candidate_seconday_phone')
                        .html(data.contact_info
                            .secondary_phone) : ''
                    data.contact_info && data.contact_info.email ? $('#contact_info_email').html(data
                        .contact_info.email) : ''

                    if (data.candidate.whatsapp_number && data.candidate.whatsapp_number.length) {
                        $("#contact_whatsapp").attr("href", 'https://wa.me/' + data.candidate.whatsapp_number)
                        $('#contact_whatsapp_part').removeClass('d-none')
                    } else {
                        $('#contact_whatsapp_part').addClass('d-none')
                    }

                    $('#candidate-profile-modal').modal('show');
                    if (response.profile_view_limit && response.profile_view_limit.length) {
                        if (!response.data.candidate.already_view) {
                            toastr.success(response.profile_view_limit, 'Success');
                        }
                    }

                    $('#cv_view' + candidate.id).removeClass("d-none");
                },
                error: function(error) {
                    // Sembunyikan modal jika terjadi error
                    $('#candidate-profile-modal').modal('hide');

                    // Tampilkan pesan error yang lebih informatif
                    let errorMessage = 'Terjadi kesalahan saat memuat data kandidat';
                    if (error.responseJSON && error.responseJSON.message) {
                        errorMessage = error.responseJSON.message;
                    }

                    Swal.fire('Error', errorMessage, 'error');
                }
            });
        }
        function capitalizeFirstLetter(string) {
            return string[0].toUpperCase() + string.slice(1);
        }
    </script>
@endsection
