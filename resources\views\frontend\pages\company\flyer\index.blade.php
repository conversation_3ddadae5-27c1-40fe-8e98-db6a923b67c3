@extends('frontend.layouts.app')

@section('title')
    {{ __('Upload Flyer Lowongan Kerja') }}
@endsection

@section('main')
    <div class="dashboard-wrapper">
        <div class="container">
            <div class="row">
                {{-- Sidebar --}}
                <x-website.company.sidebar />
                <div class="col-lg-9">
                    <div class="dashboard-right">
                        <div class="dashboard-right-header">
                            <div class="d-flex align-items-center justify-content-between">
                                <span class="sidebar-open-nav d-lg-none">
                                    <i class="ph-list"></i>
                                </span>
                                <h2 class="tw-text-2xl tw-font-medium tw-text-[#18191C]">
                                    {{ __('Upload Flyer Lowongan Kerja') }}
                                </h2>
                            </div>
                        </div>
                        <div class="d-flex justify-content-end mb-3">
                            <a href="{{ route('company.flyer.create') }}" class="btn btn-primary">
                                <i class="ph-plus-circle"></i> {{ __('Tambah Flyer') }}
                            </a>
                        </div>
                        <div class="dashboard-right-body mt-5">
                            <div class="table-responsive">
                                <table class="table table-bordered">
                                    <thead>
                                        <tr>
                                            <th>{{ __('No') }}</th>
                                            <th>{{ __('Gambar') }}</th>
                                            <th>{{ __('Tanggal Submit') }}</th>
                                            <th>{{ __('Status') }}</th>
                                            <th>{{ __('Aksi') }}</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        @forelse ($flyers as $flyer)
                                            <tr>
                                                <td>{{ $loop->iteration }}</td>
                                                <td>
                                                    <a href="{{ asset('uploads/flyers/' . $flyer->image) }}" data-fancybox="gallery">
                                                        <img src="{{ asset('uploads/flyers/' . $flyer->image) }}" alt="Flyer" class="img-thumbnail" style="width: 100px; height: 100px; object-fit: cover;">
                                                    </a>
                                                </td>
                                                <td>{{ $flyer->created_at->format('d M Y') }}</td>
                                                <td>
                                                    @if ($flyer->status == 'pending')
                                                        <span class="badge bg-warning">{{ __('Menunggu') }}</span>
                                                    @elseif ($flyer->status == 'active')
                                                        <span class="badge bg-success">{{ __('Aktif') }}</span>
                                                    @elseif ($flyer->status == 'rejected')
                                                        <span class="badge bg-danger">{{ __('Ditolak') }}</span>
                                                        @if ($flyer->rejection_reason)
                                                            <button type="button" class="btn btn-sm btn-link text-danger" data-bs-toggle="modal" data-bs-target="#rejectionModal{{ $flyer->id }}">
                                                                <i class="ph-info-circle"></i>
                                                            </button>
                                                            <!-- Modal -->
                                                            <div class="modal fade" id="rejectionModal{{ $flyer->id }}" tabindex="-1" aria-labelledby="rejectionModalLabel{{ $flyer->id }}" aria-hidden="true">
                                                                <div class="modal-dialog">
                                                                    <div class="modal-content">
                                                                        <div class="modal-header">
                                                                            <h5 class="modal-title" id="rejectionModalLabel{{ $flyer->id }}">{{ __('Alasan Penolakan') }}</h5>
                                                                            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                                                                        </div>
                                                                        <div class="modal-body">
                                                                            {{ $flyer->rejection_reason }}
                                                                        </div>
                                                                        <div class="modal-footer">
                                                                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">{{ __('Tutup') }}</button>
                                                                        </div>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        @endif
                                                    @elseif ($flyer->status == 'revision')
                                                        <span class="badge bg-info">{{ __('Perlu Revisi') }}</span>
                                                        @if ($flyer->rejection_reason)
                                                            <button type="button" class="btn btn-sm btn-link text-info" data-bs-toggle="modal" data-bs-target="#rejectionModal{{ $flyer->id }}">
                                                                <i class="ph-info-circle"></i>
                                                            </button>
                                                            <!-- Modal -->
                                                            <div class="modal fade" id="rejectionModal{{ $flyer->id }}" tabindex="-1" aria-labelledby="rejectionModalLabel{{ $flyer->id }}" aria-hidden="true">
                                                                <div class="modal-dialog">
                                                                    <div class="modal-content">
                                                                        <div class="modal-header">
                                                                            <h5 class="modal-title" id="rejectionModalLabel{{ $flyer->id }}">{{ __('Alasan Revisi') }}</h5>
                                                                            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                                                                        </div>
                                                                        <div class="modal-body">
                                                                            {{ $flyer->rejection_reason }}
                                                                        </div>
                                                                        <div class="modal-footer">
                                                                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">{{ __('Tutup') }}</button>
                                                                        </div>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        @endif
                                                    @endif
                                                </td>
                                                <td>
                                                    <div class="d-flex gap-1">
                                                        <a href="{{ route('company.flyer.edit', $flyer->id) }}" class="btn btn-sm btn-primary">
                                                            <i class="ph-pencil-simple"></i>
                                                        </a>
                                                        <form action="{{ route('company.flyer.destroy', $flyer->id) }}" method="POST" class="d-inline">
                                                            @csrf
                                                            @method('DELETE')
                                                            <button type="submit" class="btn btn-sm btn-danger" onclick="return confirm('{{ __('Apakah Anda yakin ingin menghapus flyer ini?') }}')">
                                                                <i class="ph-trash"></i>
                                                            </button>
                                                        </form>
                                                    </div>
                                                </td>
                                            </tr>
                                        @empty
                                            <tr>
                                                <td colspan="5" class="text-center">{{ __('Tidak ada data') }}</td>
                                            </tr>
                                        @endforelse
                                    </tbody>
                                </table>
                            </div>
                            <div class="mt-3">
                                {{ $flyers->links() }}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection

@section('frontend_scripts')
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/@fancyapps/ui@5.0/dist/fancybox/fancybox.css" />
<script src="https://cdn.jsdelivr.net/npm/@fancyapps/ui@5.0/dist/fancybox/fancybox.umd.js"></script>
<script>
    Fancybox.bind("[data-fancybox]", {
        // Your custom options
    });

    // Mobile sidebar toggle
    $(document).ready(function() {
        $('.sidebar-open-nav').on('click', function() {
            $('.d-sidebar').addClass('active');
            $('body').addClass('sidebar-overlay');
        });

        $('.close-sidebar').on('click', function() {
            $('.d-sidebar').removeClass('active');
            $('body').removeClass('sidebar-overlay');
        });

        $(document).on('click', function(e) {
            if ($(e.target).closest('.d-sidebar').length === 0 && $(e.target).closest('.sidebar-open-nav').length === 0) {
                $('.d-sidebar').removeClass('active');
                $('body').removeClass('sidebar-overlay');
            }
        });
    });
</script>
<style>
    @media (max-width: 991px) {
        .d-sidebar {
            position: fixed;
            top: 0;
            left: -300px;
            width: 300px;
            height: 100vh;
            background-color: #fff;
            z-index: 9999;
            transition: all 0.3s ease;
            overflow-y: auto;
            padding: 20px;
            box-shadow: 0 0 15px rgba(0, 0, 0, 0.1);
        }

        .d-sidebar.active {
            left: 0;
        }

        body.sidebar-overlay:before {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
            z-index: 999;
        }

        .close-sidebar {
            display: block;
            cursor: pointer;
        }

        .sidebar-open-nav {
            display: block;
            cursor: pointer;
            font-size: 24px;
        }
    }
</style>
@endsection
