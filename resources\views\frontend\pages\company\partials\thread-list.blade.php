@foreach($threads as $thread)
    @php
        $isSelected = isset($selectedThread) && $selectedThread && $selectedThread->id == $thread->id;
        $unreadCount = $thread->unreadMessages->count();
        $otherParty = null;
        $otherPartyName = '';
        $otherPartyAvatar = '';
        $otherPartyRole = '';

        // Determine other party based on current user role
        if (auth()->user()->role == 'company') {
            if ($thread->candidate) {
                $otherParty = $thread->candidate;
                $otherPartyName = $otherParty->user->name;
                $otherPartyAvatar = $otherParty->photo;
                $otherPartyRole = 'candidate';
            } elseif ($thread->is_admin_thread) {
                $otherPartyName = 'Admin';
                $otherPartyAvatar = asset('backend/image/default.png');
                $otherPartyRole = 'admin';
            }
        } elseif (auth()->user()->role == 'candidate') {
            if ($thread->company) {
                $otherParty = $thread->company;
                $legalEntity = $otherParty->legal_entity ?? '';
                $otherPartyName = $legalEntity ? $legalEntity . ' ' . $otherParty->user->name : $otherParty->user->name;
                $otherPartyAvatar = $otherParty->logo;
                $otherPartyRole = 'company';
            } elseif ($thread->is_admin_thread) {
                $otherPartyName = 'Admin';
                $otherPartyAvatar = asset('backend/image/default.png');
                $otherPartyRole = 'admin';
            }
        }

        // Get latest message
        $latestMessage = $thread->latestMessage;
        $messagePreview = $latestMessage ? (strlen($latestMessage->body) > 50 ? substr($latestMessage->body, 0, 50) . '...' : $latestMessage->body) : '';

        // Check if there's an attachment
        $hasAttachment = $latestMessage && !empty($latestMessage->attachment);

        // Format time
        $messageTime = $latestMessage ? $latestMessage->created_at->diffForHumans() : '';
    @endphp

    <a href="{{ route('company.inbox', ['pesan_id' => $thread->id]) }}"
       class="list-group-item list-group-item-action py-3 {{ $isSelected ? 'active' : '' }} {{ $unreadCount > 0 ? 'fw-bold' : '' }}">
        <div class="d-flex w-100 align-items-center">
            <div class="flex-shrink-0 me-3">
                @if($otherPartyRole == 'admin')
                    <div class="avatar avatar-md">
                        <div class="rounded-circle bg-primary d-flex align-items-center justify-content-center" style="width: 40px; height: 40px;">
                            <i class="fas fa-user-shield text-white"></i>
                        </div>
                    </div>
                @elseif($otherPartyRole == 'candidate')
                    <div class="avatar avatar-md">
                        @if($otherPartyAvatar)
                            <img src="{{ str_replace('/company/uploads/', '/uploads/', $otherPartyAvatar) }}" alt="{{ $otherPartyName }}" class="rounded-circle" width="40" height="40" style="object-fit: cover;">
                        @else
                            <div class="rounded-circle bg-secondary d-flex align-items-center justify-content-center" style="width: 40px; height: 40px;">
                                <span class="text-white fw-bold">{{ substr($otherPartyName, 0, 1) }}</span>
                            </div>
                        @endif
                    </div>
                @elseif($otherPartyRole == 'company')
                    <div class="avatar avatar-md">
                        @if($otherPartyAvatar)
                            <img src="{{ str_replace('/company/uploads/', '/uploads/', $otherPartyAvatar) }}" alt="{{ $otherPartyName }}" class="rounded-circle" width="40" height="40" style="object-fit: cover;">
                        @else
                            <div class="rounded-circle bg-info d-flex align-items-center justify-content-center" style="width: 40px; height: 40px;">
                                <span class="text-white fw-bold">{{ substr($otherPartyName, 0, 1) }}</span>
                            </div>
                        @endif
                    </div>
                @endif
            </div>
            <div class="flex-grow-1 min-width-0">
                <div class="d-flex justify-content-between align-items-center mb-1">
                    <h6 class="mb-0 text-truncate" style="max-width: 150px;" title="{{ $otherPartyName }}">
                        {{ $otherPartyName }}
                    </h6>
                    <small class="text-muted">{{ $messageTime }}</small>
                </div>
                <div class="d-flex justify-content-between align-items-center">
                    <p class="mb-0 text-truncate" style="max-width: 200px;">
                        @if($hasAttachment)
                            <i class="fas fa-paperclip me-1"></i>
                        @endif
                        {{ $messagePreview ?: 'No message' }}
                    </p>
                    @if($unreadCount > 0)
                        <span class="badge bg-primary rounded-pill">{{ $unreadCount }}</span>
                    @endif
                </div>
            </div>
        </div>
    </a>
@endforeach
