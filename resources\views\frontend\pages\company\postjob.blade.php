@extends('frontend.layouts.app')

@section('title')
    {{ __('post_job') }}
@endsection

@section('main')
    <div class="dashboard-wrapper">
        <div class="container">
            <div class="row">
                {{-- Sidebar --}}
                <x-website.company.sidebar />
                <div class="col-lg-9">
                    <div class="dashboard-right tw-ps-0 lg:tw-ps-5">
                        <div class="dashboard-right-header">
                            <span class="sidebar-open-nav">
                                <i class="ph-list"></i>
                            </span>
                        </div>
                        <h2 class="tw-text-2xl tw-font-medium tw-text-[#18191C] tw-mb-8">
                            {{ __('post_a_job') }}
                        </h2>
                        <p>Bertanda <font color="red">*</font> <b>WAJIB</b> diisi!</p>
                        <form action="{{ route('company.job.store') }}" method="POST" class="rt-from">
                            @csrf
                            <div class="post-job-item rt-mb-15 tw-w-full tw-overflow-hidden">
                                <div class="row">
                                    <div class="col-lg-8 rt-mb-20">
                                        <x-forms.label name="job_title" :required="true" class="tw-text-sm tw-mb-2" />
                                        <input value="{{ old('title') }}" name="title"
                                            class="form-control @error('title') is-invalid @enderror" type="text"
                                            placeholder="{{ __('job_title') }}" id="m">
                                        @error('title')
                                            <span class="error invalid-feedback">{{ $message }}</span>
                                        @enderror
                                    </div>
                                    <div class="col-lg-4 rt-mb-20 col-md-4">
                                        <x-forms.label name="job_category" :required="true" class="tw-text-sm tw-mb-2" />
                                        <select
                                            class=" select2-taggable select2-search form-control @error('category_id') is-invalid @enderror"
                                            name="category_id">
                                            @foreach ($jobCategories as $category)
                                                <option {{ old('category_id') == $category->id ? 'selected' : '' }}
                                                    value="{{ $category->id }}">{{ $category->name }}</option>
                                            @endforeach
                                        </select>
                                        @error('category_id')
                                            <span class="error invalid-feedback">{{ $message }}</span>
                                        @enderror
                                    </div>
                                    <div class="col-lg-8 rt-mb-20 col-md-8">
                                        <x-forms.label name="tags" :required="false" class="tw-text-sm tw-mb-2">
                                            ({{ __('saerch_or_write_tag_and_hit_enter') }})
                                        </x-forms.label>

                                        <select
                                            class=" rt-selectactive select2-taggable form-control @error('tags') is-invalid @enderror"
                                            name="tags[]" multiple>
                                            @foreach ($tags as $tag)
                                                <option
                                                    {{ old('tags') ? (in_array($tag->id, old('tags')) ? 'selected' : '') : '' }}
                                                    value="{{ $tag->id }}">{{ $tag->name }}</option>
                                            @endforeach
                                        </select>
                                        @error('tags')
                                            <span class="error invalid-feedback">{{ $message }}</span>
                                        @enderror

                                        <!-- Tag Populer -->
                                        @if(isset($popularTags) && $popularTags->count() > 0)
                                        <div class="mt-2">
                                            <small class="text-muted">{{ __('Tag Populer') }}:</small>
                                            <div class="popular-tags mt-1">
                                                @foreach($popularTags->take(8) as $popularTag)
                                                <span class="badge badge-light border mr-1 mb-1 popular-tag-item"
                                                      style="cursor: pointer; font-size: 11px; padding: 4px 8px;"
                                                      data-tag-id="{{ $popularTag->id }}"
                                                      data-tag-name="{{ $popularTag->name }}">
                                                    #{{ $popularTag->name }}
                                                </span>
                                                @endforeach
                                            </div>
                                        </div>
                                        @endif
                                    </div>
                                    <div class="col-lg-4 rt-mb-20 col-md-4">
                                        <x-forms.label name="job_role" :required="true" class="tw-text-sm tw-mb-2" />
                                        <select
                                            class=" select2-taggable select2-search form-control @error('role_id') is-invalid @enderror"
                                            name="role_id">
                                            @foreach ($roles as $role)
                                                <option {{ old('role_id') == $role->id ? 'selected' : '' }}
                                                    value="{{ $role->id }}">{{ $role->name }}</option>
                                            @endforeach
                                        </select>
                                        @error('role_id')
                                            <span class="error invalid-feedback">{{ $message }}</span>
                                        @enderror
                                    </div>
                                </div>
                            </div>
                            <div class="post-job-item rt-mb-15 tw-w-full tw-overflow-hidden">
                                <h4 class="f-size-18 ft-wt-5 rt-mb-20 lh-1">{{ __('salary') }}</h4>
                                    <small class="tw-text-sm tw-font-medium italic tw-text-red-500">Masukkan informasi gaji yang sama antara gaji minimal dan maksimal jika bukan rentang gaji.</small>
                                <div class="tw-flex tw-gap-5 mb-3">
                                    {{-- <span class="text-danger italic"></span> --}}
                                    <div
                                        class="ll-radio tw-flex tw-items-center tw-border tw-border-gray-200 tw-rounded tw-ps-1">
                                        <input checked onclick="salaryModeChange('range')" id="salary_rangee" type="radio"
                                            value="range" name="salary_mode" class="tw-scale-150">
                                        <label for="salary_rangee"
                                            class="tw-w-full tw-py-4 tw-ms-2 tw-text-sm tw-font-medium">{{ __('salary_range') }}</label>
                                    </div>
                                    <div
                                        class="ll-radio tw-flex tw-items-center tw-border tw-border-gray-200 tw-rounded tw-ps-1">
                                        <input onclick="salaryModeChange('custom')" id="custom_salary" type="radio"
                                            value="custom" name="salary_mode" class="tw-scale-150">
                                        <label for="custom_salary"
                                            class="tw-w-full tw-py-4 tw-ms-2 tw-text-sm tw-font-medium">{{ __('custom_salary') }}</label>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="rt-mb-20 col-md-8 d-none" id="custom_salary_part">
                                        <x-forms.label name="custom_salary" :required="true" class="tw-text-sm tw-mb-2" />
                                        <div class="position-relative">
                                            <input value="{{ old('custom_salary', '') }}" name="custom_salary"
                                                class="form-control @error('custom_salary') is-invalid @enderror"
                                                type="text" placeholder="{{ __('Contoh: UMR / Kompetitif') }}" id="m">
                                            @error('custom_salary')
                                                <span class="error invalid-feedback">{{ $message }}</span>
                                            @enderror
                                        </div>
                                    </div>
                                    <div class="rt-mb-20 col-md-4 salary_range_part">
                                        <x-forms.label name="min_salary" :required="false" class="tw-text-sm tw-mb-2" />
                                        <div class="position-relative">
                                            <input step="0.01"
                                                   value="{{ old('min_salary', '1000000') }}"
                                                   class="form-control @error('min_salary') is-invalid @enderror"
                                                   name="min_salary"
                                                   type="text"
                                                   placeholder="{{ __('min_salary') }}"
                                                   id="min_salary"
                                                   oninput="formatNumber(this)"
                                                   onblur="removeFormatting(this)">
                                            <div class="usd font-bold">{{ $currency_symbol }}</div>
                                            @error('min_salary')
                                                <span class="error invalid-feedback">{{ $message }}</span>
                                            @enderror
                                        </div>
                                    </div>

                                    <div class="rt-mb-20 col-md-4 salary_range_part">
                                        <x-forms.label name="max_salary" :required="false" class="tw-text-sm tw-mb-2" />
                                        <div class="position-relative">
                                            <input step="0.01"
                                                   value="{{ old('max_salary', '2000000') }}"
                                                   class="form-control @error('max_salary') is-invalid @enderror"
                                                   name="max_salary"
                                                   type="text"
                                                   placeholder="{{ __('max_salary') }}"
                                                   id="max_salary"
                                                   oninput="formatNumber(this)"
                                                   onblur="removeFormatting(this)">
                                            <div class="usd">{{ $currency_symbol }}</div>
                                            @error('max_salary')
                                                <span class="error invalid-feedback">{{ $message }}</span>
                                            @enderror
                                        </div>
                                    </div>
                                    <div class="col-lg-4 rt-mb-20 col-md-6">
                                        <x-forms.label name="{{ __('salary_type') }}" :required="true"
                                            class="tw-text-sm tw-mb-2" />
                                        <select
                                            class="rt-selectactive form-control @error('salary_type') is-invalid @enderror "
                                            name="salary_type">
                                            @foreach ($salary_types as $type)
                                                <option {{ old('salary_type') == $type->id ? 'selected' : '' }}
                                                    value="{{ $type->id }}">
                                                    {{ $type->name }}
                                                </option>
                                            @endforeach
                                        </select>
                                        @error('salary_type')
                                            <span class="error invalid-feedback">{{ $message }}</span>
                                        @enderror
                                    </div>
                                </div>
                            </div>


                            <div class="post-job-item rt-mb-15 tw-w-full tw-overflow-hidden">
                                <h4 class="f-size-18 ft-wt-5 rt-mb-20 lh-1">{{ __('advance_information') }}</h4>
                                <div class="row">
                                    <div class="col-lg-4 col-md-6 rt-mb-20">
                                        <x-forms.label name="education" :required="true" class="tw-text-sm tw-mb-2" />
                                        <select
                                            class="select2-taggable form-control @error('education') is-invalid @enderror "
                                            name="education">
                                            @foreach ($educations as $education)
                                                <option {{ old('education') == $education->id ? 'selected' : '' }}
                                                    value="{{ $education->id }}">
                                                    {{ $education->name }}
                                                </option>
                                            @endforeach
                                        </select>
                                        @error('education')
                                            <span class="error invalid-feedback">{{ $message }}</span>
                                        @enderror
                                    </div>
                                    <div class="col-lg-4 col-md-6 rt-mb-20">
                                        <x-forms.label name="experience" :required="true" class="tw-text-sm tw-mb-2" />
                                        <select
                                            class="select2-taggable form-control @error('experience') is-invalid @enderror "
                                            name="experience">
                                            @foreach ($experiences as $experience)
                                                <option {{ old('experience') == $experience->id ? 'selected' : '' }}
                                                    value="{{ $experience->id }}">
                                                    {{ $experience->name }}
                                                </option>
                                            @endforeach
                                        </select>
                                        @error('experience')
                                            <span class="error invalid-feedback">{{ $message }}</span>
                                        @enderror
                                    </div>
                                    <div class="col-lg-4 col-md-6 rt-mb-20">
                                        <x-forms.label name="job_type" :required="true" class="tw-text-sm tw-mb-2" />
                                        <select
                                            class="rt-selectactive form-control @error('job_type') is-invalid @enderror "
                                            name="job_type">
                                            @foreach ($job_types as $job_type)
                                                <option {{ old('job_type') == $job_type->id ? 'selected' : '' }}
                                                    value="{{ $job_type->id }}">
                                                    {{ $job_type->name }}
                                                </option>
                                            @endforeach
                                        </select>
                                        @error('job_type')
                                            <span class="error invalid-feedback">{{ $message }}</span>
                                        @enderror
                                    </div>
                                    <div class="col-lg-4 col-md-6 rt-mb-20">
                                        <x-forms.label name="Dibutuhkan (orang)" :required="true" class="tw-text-sm tw-mb-2" />
                                        <input value="{{ old('vacancies', 1) }}" name="vacancies" type="number"
                                            placeholder="{{ __('vacancies') }}"
                                            class="form-control @error('vacancies') is-invalid @enderror" id="vacancies">
                                        @error('vacancies')
                                            <span class="error invalid-feedback">{{ $message }}</span>
                                        @enderror
                                        <small class="tw-text-sm tw-font-medium italic tw-text-red-500">Jumlah karyawan yang dibutuhkan.</small>
                                    </div>
                                    <!-- Gender Selection -->
                                    <div class="col-lg-4 col-md-4 rt-mb-20">
                                        <x-forms.label name="Jenis Kelamin" :required="true" class="tw-text-sm tw-mb-2" />
                                        <select name="gender" class="form-control @error('gender') is-invalid @enderror">
                                            <option value="both" {{ old('gender') == 'both' ? 'selected' : '' }}>{{ __('Laki-laki & Perempuan') }}</option>
                                            <option value="male" {{ old('gender') == 'male' ? 'selected' : '' }}>{{ __('Laki-laki') }}</option>
                                            <option value="female" {{ old('gender') == 'female' ? 'selected' : '' }}>{{ __('Perempuan') }}</option>
                                        </select>
                                        @error('gender')
                                            <span class="error invalid-feedback">{{ $message }}</span>
                                        @enderror
                                        <small class="tw-text-sm tw-font-medium italic tw-text-red-500">Pilih jenis kelamin yang dibutuhkan untuk pekerjaan ini.</small>
                                    </div>

                                    <div class="col-lg-4 col-md-6 rt-mb-20">
                                        <x-forms.label name="Batas Lamaran" :required="true"
                                            class="tw-text-sm tw-mb-2" />
                                        <div class="fromGroup">
                                            <div class="form-control-icon date datepicker">
                                                <input value="{{ old('deadline') }}" name="deadline"
                                                    class="form-control @error('deadline') is-invalid @enderror"
                                                    type="text" value="{{ old('deadline') ? old('deadline') : '' }}"
                                                    id="date" placeholder="d/m/y">
                                                <span class="input-group-addon has-badge">
                                                    <span @error('deadline') rt-mr-12 @enderror>
                                                        <x-svg.calendar-icon />
                                                    </span>
                                                </span>
                                                @error('deadline')
                                                    <span class="error invalid-feedback d-block">{{ $message }}</span>
                                                @enderror
                                            </div>
                                        </div>
                                        <div class="tw-text-sm tw-font-medium tw-text-red-500 italic">
                                            {{ __('maximum_deadline_limit') }}:
                                            {{ $setting->job_deadline_expiration_limit }} {{ __('days') }}
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                @if (config('templatecookie.map_show'))
                                    <div class="col-12 rt-mb-15">
                                        @php
                                            $map = $setting->default_map;
                                        @endphp
                                        <div class="location-wrapper">
                                            <div class="row">
                                                <div class="col-12">
                                                    <h2>
                                                        {{ __('location') }} <span class="text-danger">*</span>
                                                        <small class="h6">
                                                            ({{ __('click_to_add_a_pointer') }})
                                                        </small>
                                                    </h2>
                                                </div>
                                                <div class="col-md-12 col-sm-12 rt-mb-24">
                                                    <x-website.map.map-warning />

                                                    <div id="google-map-div"
                                                        class="{{ $map == 'google-map' ? '' : 'd-none' }}">
                                                        <input id="searchInput" class="mapClass" type="text"
                                                            placeholder="{{ __('enter_location') }}">
                                                        <div class="map mymap" id="google-map"></div>
                                                    </div>
                                                    <div class="{{ $map == 'leaflet' ? '' : 'd-none' }}">
                                                        <input type="text" autocomplete="off" id="leaflet_search"
                                                            placeholder="{{ __('enter_city_name') }}"
                                                            class="full-width" />
                                                        <br>
                                                        <div id="leaflet-map"></div>
                                                    </div>
                                                    @error('location')
                                                        <span class="ml-3 text-md text-danger">{{ $message }}</span>
                                                    @enderror
                                                </div>
                                                <div class="col-12 mt-4 custom-checkbox-wrap">
                                                    <label class="main tw-text-sm"
                                                        for="remoteWork">{{ __('fully_remote_position') }}-<span
                                                            class="tw-font-medium">{{ __('worldwide') }}</span>
                                                        <input type="checkbox" name="is_remote" id="remoteWork"
                                                            value="1" {{ old('is_remote') ? 'checked' : '' }}>
                                                        <span class="custom-checkbox"></span>
                                                    </label>
                                                    <input type="checkbox" name="is_remote" id="remoteWork"
                                                        value="1" {{ old('is_remote') ? 'checked' : '' }}>
                                                </div>

                                                <div class="col-12 mt-4">
                                                    @php
                                                        $session_location = session()->get('location');
                                                        $session_country = $session_location && array_key_exists('country', $session_location) ? $session_location['country'] : '-';
                                                        $session_exact_location = $session_location && array_key_exists('exact_location', $session_location) ? $session_location['exact_location'] : '-';
                                                    @endphp
                                                    <div class="card-footer row mt-4 border-0">
                                                        <span>
                                                            <img src="{{ asset('frontend/assets/images/loader.gif') }}"
                                                                alt="loading" width="50px" height="50px"
                                                                class="loader_position d-none">
                                                        </span>
                                                        <div class="location_secion">
                                                            {{ __('country') }}: <span
                                                                class="location_country">{{ $session_country }}</span>
                                                            </br>
                                                            {{ __('full_address') }}: <span
                                                                class="location_full_address">{{ $session_exact_location }}</span>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                @else
                                    <x-forms.label name="location" :required="true" class="tw-text-sm tw-mb-2" />
                                    <div class="card-body pt-0">
                                        <div>
                                            @livewire('country-state-city', ['row' => true])
                                            <!-- Hidden inputs untuk menyimpan lokasi ke database -->
                                            <input type="hidden" name="country" value="Indonesia">
                                            <input type="hidden" name="region" id="hidden_region">
                                            <input type="hidden" name="district" id="hidden_district">
                                            <input type="hidden" name="locality" id="hidden_locality">
                                            @error('location')
                                                <span class="ml-3 text-md text-danger">{{ $message }}</span>
                                            @enderror
                                        </div>
                                    </div>
                                @endif
                            </div>
                            <!-- Disability Friendly -->
                            <div class="post-job-item rt-mb-15 tw-w-full tw-overflow-hidden">
                                <div class="col-12 rt-mb-20">
                                    <x-forms.label name="Ramah Disabilitas" :required="false" class="tw-text-sm tw-mb-2" />
                                    <div class="form-check form-switch d-flex align-items-center">
                                        <div class="tw-mr-3" style="padding-left: 40px;">
                                            <input class="form-check-input" style="width: 3em; height: 1.5em;" type="checkbox" id="disability_friendly" name="disability_friendly" value="1" {{ old('disability_friendly') ? 'checked' : '' }}>
                                        </div>
                                        <label class="form-check-label" for="disability_friendly">Lowongan ini ramah disabilitas</label>
                                    </div>
                                    <small class="tw-text-sm tw-font-medium italic tw-text-red-500">Aktifkan jika lowongan ini cocok untuk penyandang disabilitas.</small>
                                </div>
                            </div>
                            <!-- Disability Types -->
                            <div class="post-job-item rt-mb-32 tw-w-full tw-overflow-hidden" id="disability_types_container" style="display: none;">
                                <h4 class="f-size-18 ft-wt-5 rt-mb-20 lh-1">Jenis Disabilitas yang Cocok</h4>
                                <div class="disability-types-grid" id="disability_types_list">
                                    @foreach (\App\Models\DisabilityType::all() as $disabilityType)
                                        <div class="disability-type-item">
                                            <label for="disability_type_{{ $disabilityType->id }}" class="disability-type-label">
                                                <input
                                                    {{ old('disability_types') ? (in_array($disabilityType->id, old('disability_types', [])) ? 'checked' : '') : '' }}
                                                    type="checkbox" id="disability_type_{{ $disabilityType->id }}" name="disability_types[]"
                                                    value="{{ $disabilityType->id }}" class="disability-checkbox">
                                                <span class="disability-icon"><i class="{{ $disabilityType->icon }}"></i></span>
                                                <span class="disability-name">{{ $disabilityType->name }}</span>
                                                <span class="disability-info" data-toggle="tooltip" title="{{ $disabilityType->description }}" data-html="true">
                                                    <i class="fas fa-question-circle"></i>
                                                </span>
                                            </label>
                                        </div>
                                    @endforeach
                                </div>
                                @error('disability_types')
                                    <span class="error invalid-feedback d-block">{{ $message }}</span>
                                @enderror
                            </div>

                            <div class="post-job-item rt-mb-32 tw-w-full tw-overflow-hidden">
                                <h4 class="f-size-18 ft-wt-5 rt-mb-20 lh-1">{{ __('benefits') }}</h4>
                                <div class="benefits-tags" id="benefit_list">
                                    @foreach ($benefits as $benefit)
                                        <label for="benefit_{{ $benefit->id }}">
                                            <input
                                                {{ old('benefits') ? (in_array($benefit->id, old('benefits')) ? 'checked' : '') : '' }}
                                                type="checkbox" id="benefit_{{ $benefit->id }}" name="benefits[]"
                                                value="{{ $benefit->id }}">
                                            <span>{{ $benefit->name }}</span>
                                        </label>
                                    @endforeach
                                </div>
                                @error('benefits')
                                    <span class="error invalid-feedback d-block">{{ $message }}</span>
                                @enderror

                                <div class="mt-3">
                                    <a onclick="showHideCreateBenefit()" href="javascript:void(0)" class="text-decoration-underline">Buat Benefit Baru</a>
                                    {{-- <a onclick="showHideCreateBenefit()" href="javascript:void(0)" class="text-decoration-underline">{{ __('create_new') }} {{ __('benefit') }}</a> --}}

                                    <div class="d-flex tw-justify-between tw-gap-2 mt-3 d-none" id="create_benefit">
                                        <input value="{{ old('title') }}" name="new_benefit"
                                            class="form-control @error('title') is-invalid @enderror" type="text"
                                            placeholder="{{ __('benefit') }}" id="m">

                                        <button onclick="createBenefit()" type="button"
                                            class="btn btn-primary rt-mr-10">
                                            <span class="button-content-wrapper ">
                                                <span class="button-text">
                                                    {{ __('create') }} {{ __('benefit') }}
                                                </span>
                                                <span class="button-icon align-icon-right">
                                                    <i class="ph ph-plus"></i>
                                                </span>
                                            </span>
                                        </button>
                                    </div>
                                </div>
                            </div>
                            <div class="col-12 rt-mb-20 ">
                                <x-forms.label name="Skill yang Dibutuhkan (Opsional)" :required="false" />
                                <select id="skills" name="skills[]"
                                    class="select2-taggable form-control @error('skills') is-invalid @enderror" multiple>
                                    @foreach ($skills as $skill)
                                        <option
                                            {{ old('skills') ? (in_array($skill->id, old('skills')) ? 'selected' : '') : '' }}
                                            value="{{ $skill->id }}">{{ $skill->name }}</option>
                                    @endforeach
                                </select>
                                @error('skills')
                                    <span class="invalid-feedback" role="alert">{{ __($message) }}</span>
                                @enderror
                            </div>
                            <div class="post-job-item rt-mb-32 tw-w-full tw-overflow-hidden">
                                <h4 class="f-size-18 ft-wt-5 tw-mb-3 lh-1">
                                    {{ __('job_description') }}
                                    <span class="form-label-required text-danger">*</span>
                                </h4>
                                <small class="text-muted italic">{{ __('Minimal 50 karakter.') }}</small>
                                <div class="col-md-12">
                                    <textarea id="image_ckeditorx" class="form-control @error('description') is-invalid @enderror" name="description">{{ old('description') }}
                                    </textarea>
                                    @error('description')
                                        <span class="error invalid-feedback">{{ $message }}</span>
                                    @enderror
                                </div>
                            </div>

                            {{-- Additional Questions --}}
                            @if (currentCompany()->question_feature_enable)
                                <div x-data="appQuestion()" x-init="select2Alpine"
                                    class="post-job-item rt-mb-15 tw-w-full tw-overflow-hidden ">
                                    <h4 class="f-size-18 ft-wt-5 rt-mb-20 lh-1">{{ __('add_screening_questions') }}</h4>
                                    <div class="row">
                                        <div class="rt-mb-20">
                                            <div class="col-lg-12">
                                                <div x-show="isAddingNewQuestion" class="tw-flex justify-content-between">
                                                    <label class="tw-text-sm tw-mb-2 mb-2" for="for">
                                                        {{ __('create_new_screening_question') }}
                                                    </label>
                                                    <a x-show="isAddingNewQuestion" href="#"
                                                        @click.prevent="isAddingNewQuestion = false">
                                                        {{ __('choose_from_existing_question') }}
                                                    </a>
                                                </div>
                                                <div x-show="!isAddingNewQuestion"
                                                    class="tw-flex justify-content-between">
                                                    <label class="tw-text-sm tw-mb-2 mb-2" for="for">
                                                        {{ __('choose_from_existing_question') }}
                                                    </label>
                                                    <a href="#" x-show="!isAddingNewQuestion"
                                                        @click.prevent="isAddingNewQuestion = true"
                                                        href="#">{{ __('create_new_screening_question') }}</a>
                                                </div>
                                                <input x-show="isAddingNewQuestion" value="" x-model="newQuestion"
                                                    class="form-control " type="text" placeholder="Add Question">
                                            </div>
                                            <div x-show="isAddingNewQuestion"
                                                class="tw-flex tw-gap-5 mb-3 flex justify-content-between tw-mt-4">
                                                <div class="tw-flex justify-between ">
                                                    <div
                                                        class="ll-radio tw-flex tw-items-center tw-border tw-border-gray-200 tw-rounded tw-ps-1 tw-mr-4">
                                                        <label class="mt-2">
                                                            <input x-model="newQuestionSave" class="tw-scale-150"
                                                                type="checkbox" style="margin-right: 10px">
                                                            {{ __('save_for_letter') }}
                                                        </label>
                                                    </div>
                                                    <div
                                                        class="ll-radio tw-flex tw-items-center tw-border tw-border-gray-200 tw-rounded tw-ps-1">
                                                        <label class="mt-2">
                                                            <input x-model="isRequired" class="tw-scale-150"
                                                                type="checkbox" style="margin-right: 10px">
                                                            {{ __('candidate_must_answer') }}
                                                        </label>
                                                    </div>
                                                </div>
                                                <div>
                                                    <button @click.prevent="addQuestion" type="button"
                                                        class="btn btn-primary"> {{ __('save') }} </button>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div x-show="isAddingNewQuestion == false" class="q-select">
                                        <select id="questionSelect" multiple="multiple" x-ref="select"
                                            data-placeholder="Select Questions" name="companyQuestions[]" class="select2-taggable form-control">
                                            <option></option>
                                            @foreach ($questions as $question)
                                                <option value="{{ $question->id }}"> {{ $question->title }}
                                                    {{ $question->required ? '(required)' : '' }} </option>
                                            @endforeach
                                        </select>
                                    </div>

                                    <div x-show="selectedQuestions.length">
                                        <h4 class="f-size-18 ft-wt-5 rt-mb-20 lh-1 mt-4">
                                            {{ __('selected_screening_questions') }}</h4>
                                        <ul>
                                            <template x-for="question in selectedQuestions">
                                                <div class="tw-flex justify-content-between my-2">
                                                    <li class="flex-grow-1"
                                                        x-text="question.required  ? question.title+' (required)' : question.title ">
                                                    </li>
                                                    <div class="cursor-pointer f" style="color:red;">
                                                        <svg @click="remove(question.id)" width="20" height="20"
                                                            xmlns="http://www.w3.org/2000/svg" fill="none"
                                                            viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor"
                                                            class="w-6 h-6">
                                                            <path stroke-linecap="round" stroke-linejoin="round"
                                                                d="M9.75 9.75l4.5 4.5m0-4.5l-4.5 4.5M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                                                        </svg>

                                                    </div>
                                                </div>
                                            </template>
                                        </ul>
                                    </div>
                                </div>
                            @endif

                            <div class="row tw-mb-8">
                                <div class="col-12">
                                    <div class="applied-job-on">
                                        <div class="row">
                                            <h2>{{ __('apply_job_on') }}:</h2>
                                            <!-- apply_on -->
                                            <div id="applied_on_app"
                                                class="radio-check col-lg-4 d-flex {{ old('apply_on') === 'app' ? 'checked' : '' }}"
                                                onclick="RadioChecked('app')">
                                                <input type="radio" {{ old('apply_on') === 'app' ? 'checked' : '' }}
                                                    checked name="apply_on" value="app" id="app-app">
                                                <label for="app-app">
                                                    <h4 class="d-inline-block">
                                                        {{ config('app.name') }}</h4>
                                                    <p class="tw-mb-0">{{ __('candidate_will_apply_job_using') }}
                                                        {{ config('app.name') }} &
                                                        {{ __('all_application_will_show_on_your_dashboard') }}.</p>
                                                </label>
                                            </div>
                                            {{-- <div id="applied_on_custom_url"
                                                class="radio-check col-lg-4 d-flex {{ old('apply_on') === 'custom_url' ? 'checked' : '' }}"
                                                onclick="RadioChecked('custom_url')">
                                                <input type="radio"
                                                    {{ old('apply_on') === 'custom_url' ? 'checked' : '' }}
                                                    name="apply_on" value="custom_url" id="app-custom_url">
                                                <label for="app-custom_url">
                                                    <h4 class="d-inline-block">{{ __('external_platform') }}</h4>
                                                    <p class="tw-mb-0">
                                                        {{ __('candidate_apply_job_on_your_website_all_application_on_your_own_website') }}.
                                                    </p>
                                                </label>
                                            </div>
                                            <div id="applied_on_email"
                                                class="radio-check col-lg-4 d-flex {{ old('apply_on') === 'email' ? 'checked' : '' }}"
                                                onclick="RadioChecked('email')">
                                                <input type="radio" {{ old('apply_on') === 'email' ? 'checked' : '' }}
                                                    name="apply_on" value="email" id="app-email">
                                                <label for="app-email">
                                                    <h4 class="d-inline-block">{{ __('on_your_email') }}</h4>
                                                    <p class="tw-mb-0">
                                                        {{ __('candidate_apply_job_on_your_email_address_and_all_application_in_your_email') }}.
                                                    </p>
                                                </label>
                                            </div> --}}
                                            <!-- apply_on end-->
                                            {{-- <div class="col-12 tw-mt-2 d-none" id="apply_on_custom_url">
                                                <x-forms.label name="website_url" :required="true" />
                                                <div class="fromGroup has-icon2">
                                                    <div class="form-control-icon">
                                                        <input value="{{ old('apply_url') }}" name="apply_url"
                                                            class="form-control @error('apply_url') is-invalid @enderror"
                                                            type="url" placeholder="{{ __('website') }}">
                                                        <div class="icon-badge-2 @error('apply_url') mt-n-11 @enderror">
                                                            <x-svg.link-icon />
                                                        </div>
                                                        @error('apply_url')
                                                            <span class="error invalid-feedback">{{ $message }}</span>
                                                        @enderror
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="col-12 tw-mt-2 d-none" id="apply_on_email">
                                                <x-forms.label name="email_address" :required="true" />
                                                <div class="fromGroup has-icon2">
                                                    <div class="form-control-icon">
                                                        <input value="{{ old('apply_email') }}" name="apply_email"
                                                            class="form-control @error('apply_email') is-invalid @enderror"
                                                            type="email" placeholder="{{ __('email_address') }}">
                                                        <div class="icon-badge-2 @error('apply_email') mt-n-11 @enderror">
                                                            <x-svg.envelope-icon />
                                                        </div>
                                                        @error('apply_email')
                                                            <span class="error invalid-feedback">{{ $message }}</span>
                                                        @enderror
                                                    </div>
                                                </div>
                                            </div> --}}
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="post-job-item rt-mb-15 tw-w-full tw-overflow-hidden">
                                <button type="submit" class="btn btn-primary rt-mr-10">
                                    <span class="button-content-wrapper ">
                                        <span class="button-icon align-icon-right">
                                            <i class="ph-arrow-right"></i>
                                        </span>
                                        <span class="button-text">
                                            {{ __('post_job') }}
                                        </span>
                                    </span>
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    @endsection

    @section('css')
        <link rel="stylesheet" href="{{ asset('frontend') }}/assets/css/bootstrap-datepicker.min.css">

        {{-- @if (app()->getLocale() == 'ar')
    <script src="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-datepicker/1.9.0/locales/bootstrap-datepicker.ar.min.js
    "></script>
    @endif --}}
        <x-map.leaflet.map_links />
        <x-map.leaflet.autocomplete_links />
        @include('map::links')
        <style>
            .ck-editor__editable_inline {
                min-height: 300px;
            }

            .mymap {
                border-radius: 12px;
            }

            .mt-n-11 {
                margin-top: -11px;
            }

            .custom-checkbox-wrap .main input:checked~.custom-checkbox:after {
                left: 8% !important;
            }

            /* Tooltip styling */
            [data-toggle="tooltip"] {
                cursor: pointer;
                margin-left: 5px;
            }

            /* Custom tooltip styling */
            .disability-tooltip {
                opacity: 1 !important;
            }

            .disability-tooltip .tooltip-inner {
                background-color: #18191C;
                color: #fff;
                border-radius: 8px;
                padding: 10px 15px;
                max-width: 250px;
                text-align: left;
                font-size: 13px;
                line-height: 1.4;
                box-shadow: 0 4px 10px rgba(0, 0, 0, 0.15);
            }

            /* Styling untuk grid jenis disabilitas */
            .disability-types-grid {
                display: grid;
                grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
                gap: 15px;
                margin-bottom: 20px;
            }

            .disability-type-item {
                position: relative;
            }

            .disability-type-label {
                display: flex;
                align-items: center;
                padding: 10px;
                border: 1px solid #e4e5e8;
                border-radius: 8px;
                cursor: pointer;
                transition: all 0.3s ease;
                background-color: #fff;
                width: 100%;
                margin-bottom: 0;
            }

            .disability-type-label:hover {
                border-color: #138C79;
                background-color: #f8f9fa;
            }

            .disability-checkbox {
                position: absolute;
                opacity: 0;
            }

            .disability-checkbox:checked + .disability-icon + .disability-name {
                font-weight: 600;
                color: #138C79;
            }

            .disability-type-label.checked {
                border-color: #138C79;
                background-color: rgba(19, 140, 121, 0.05);
            }

            .disability-icon {
                display: inline-flex;
                align-items: center;
                justify-content: center;
                width: 30px;
                height: 30px;
                margin-right: 10px;
                color: #138C79;
            }

            .disability-name {
                flex: 1;
                font-size: 14px;
                color: #18191C;
            }

            .disability-info {
                color: #9199A3;
                margin-left: 5px;
                font-size: 14px;
            }

            .disability-info i {
                transition: color 0.3s ease;
            }

            .disability-info:hover i {
                color: #138C79;
            }
        </style>
    @endsection

    @section('frontend_scripts')
        @livewireScripts
        <script>
            $(document).ready(function() {
                $('.select21').select2();

                // Handle popular tag clicks
                $('.popular-tag-item').on('click', function() {
                    var tagId = $(this).data('tag-id');
                    var tagName = $(this).data('tag-name');

                    // Get current selected values
                    var currentValues = $('select[name="tags[]"]').val() || [];

                    // Add tag if not already selected
                    if (!currentValues.includes(tagId.toString())) {
                        currentValues.push(tagId.toString());
                        $('select[name="tags[]"]').val(currentValues).trigger('change');
                    }

                    // Visual feedback
                    $(this).addClass('badge-primary').removeClass('badge-light');
                });

                // Initialize tooltips dengan posisi yang lebih baik
                $('[data-toggle="tooltip"]').tooltip({
                    trigger: 'hover click',
                    placement: function(tooltip, element) {
                        // Deteksi apakah mobile atau desktop berdasarkan lebar layar
                        if (window.innerWidth < 768) {
                            return 'bottom'; // Untuk mobile, tampilkan tooltip di bawah
                        }

                        // Untuk desktop, cek posisi elemen pada layar
                        var position = $(element).offset();
                        var windowWidth = window.innerWidth;

                        // Jika elemen berada di sebelah kiri layar, tampilkan tooltip di kanan
                        if (position.left < windowWidth / 3) {
                            return 'right';
                        }
                        // Jika elemen berada di sebelah kanan layar, tampilkan tooltip di kiri
                        else if (position.left > windowWidth * 2/3) {
                            return 'left';
                        }
                        // Jika elemen berada di tengah, tampilkan tooltip di atas
                        else {
                            return 'top';
                        }
                    },
                    container: 'body',
                    html: true,
                    template: '<div class="tooltip disability-tooltip" role="tooltip"><div class="arrow"></div><div class="tooltip-inner"></div></div>'
                });

                // Styling untuk checkbox disabilitas
                $('.disability-checkbox').change(function() {
                    if ($(this).is(':checked')) {
                        $(this).closest('.disability-type-label').addClass('checked');
                    } else {
                        $(this).closest('.disability-type-label').removeClass('checked');
                    }
                });

                // Set initial state for checkboxes
                $('.disability-checkbox:checked').each(function() {
                    $(this).closest('.disability-type-label').addClass('checked');
                });

                // Tambahkan event listener untuk resize window
                $(window).resize(function() {
                    // Destroy dan reinitialize tooltips saat ukuran layar berubah
                    $('[data-toggle="tooltip"]').tooltip('dispose');
                    $('[data-toggle="tooltip"]').tooltip({
                        trigger: 'hover click',
                        placement: function(tooltip, element) {
                            if (window.innerWidth < 768) {
                                return 'bottom';
                            }

                            var position = $(element).offset();
                            var windowWidth = window.innerWidth;

                            if (position.left < windowWidth / 3) {
                                return 'right';
                            } else if (position.left > windowWidth * 2/3) {
                                return 'left';
                            } else {
                                return 'top';
                            }
                        },
                        container: 'body',
                        html: true,
                        template: '<div class="tooltip disability-tooltip" role="tooltip"><div class="arrow"></div><div class="tooltip-inner"></div></div>'
                    });
                });
            });
            window.addEventListener('render-select2', event => {
                console.log('fired');
                $('.select21').select2();
            })
        </script>
        @stack('js')
        <script src="{{ asset('frontend/assets/js/bootstrap-datepicker.min.js') }}"></script>
        <script defer src="{{ asset('backend/js/alpine.min.js') }}"></script>

        <script>
            // Function to format number with thousands separator
            function formatNumber(input) {
                // Remove all non-digit characters except the period
                let value = input.value.replace(/[^0-9.]/g, '');

                // Split the value into whole number and decimal parts
                let [whole, decimal] = value.split('.');

                // Format the whole number part with commas
                whole = whole.replace(/\B(?=(\d{3})+(?!\d))/g, ',');

                // Combine the formatted whole number with the decimal part if it exists
                input.value = decimal !== undefined ? `${whole}.${decimal}` : whole;
            }

            // Function to remove formatting before submitting to server
            function removeFormatting(input) {
                // Remove commas and set the input value back to a plain number
                input.value = input.value.replace(/,/g, '');
            }

            // Toggle disability types container when disability_friendly checkbox is clicked
            $(document).ready(function() {
                // Check initial state
                if ($('#disability_friendly').is(':checked')) {
                    $('#disability_types_container').show();
                } else {
                    $('#disability_types_container').hide();
                }

                // Add change event listener
                $('#disability_friendly').change(function() {
                    if ($(this).is(':checked')) {
                        $('#disability_types_container').slideDown();
                    } else {
                        $('#disability_types_container').slideUp();
                    }
                });

                // Update hidden inputs for location
                $(document).on('change', '.location.zone', function() {
                    $('#hidden_region').val($(this).val());
                });

                $(document).on('change', '.location.area', function() {
                    $('#hidden_district').val($(this).val());
                });

                $(document).on('change', '.location.kecamatan', function() {
                    $('#hidden_locality').val($(this).val());
                });

                // Set initial values if available
                if ($('.location.zone').val()) {
                    $('#hidden_region').val($('.location.zone').val());
                }

                if ($('.location.area').val()) {
                    $('#hidden_district').val($('.location.area').val());
                }

                if ($('.location.kecamatan').val()) {
                    $('#hidden_locality').val($('.location.kecamatan').val());
                }
            });
        </script>
        <script>
            function appQuestion() {
                return {
                    allQuestions: @json($questions),
                    selectedQuestions: [],
                    selectedQuestionsIds: [],
                    newQuestion: '',
                    isAddingNewQuestion: false,
                    newQuestionSave: true,
                    isRequired: false,
                    addQuestion: function() {

                        if (!this.newQuestion) return;

                        axios.post('/company/pertanyaan-screening', {
                            newQuestion: this.newQuestion,
                            newQuestionSave: this.newQuestionSave,
                            isRequired: this.isRequired

                        }).then((response) => {
                            this.selectedQuestions.push(response.data);
                            this.allQuestions.push(response.data);

                            this.selectedQuestionsIds.push(response.data.id);
                            var optionValue = response.data.id;
                            var optionText = response.data.title;
                            if (response.data.required) {
                                optionText += ' (required)'
                            }
                            var newOption = new Option(optionText, optionValue, false, true);
                            this.select2 = $(this.$refs.select).select2();

                            this.select2.append(newOption).trigger('change');

                            // Reset form
                            this.newQuestion = "";
                            this.newQuestionSave = true;
                            this.isRequired = false;

                            // Show success message
                            Swal.fire({
                                icon: 'success',
                                title: 'Berhasil!',
                                text: 'Pertanyaan screening berhasil dibuat.',
                                timer: 2000,
                                showConfirmButton: false
                            });

                        }).catch((error) => {
                            console.error('Error creating question:', error);
                            Swal.fire({
                                icon: 'error',
                                title: 'Gagal!',
                                text: 'Terjadi kesalahan saat membuat pertanyaan screening.',
                            });
                        });

                    },
                    remove: function(idToRemove) {
                        this.selectedQuestionsIds = this.selectedQuestionsIds.filter((id) => {
                            return id != idToRemove;
                        })
                        this.selectedQuestions = this.selectedQuestions.filter((ques) => {
                            return ques.id != idToRemove;
                        })
                        this.select2 = $(this.$refs.select).select2();
                        this.select2.val(this.selectedQuestionsIds);
                        this.select2.trigger('change');

                    }
                }
            }

            function select2Alpine() {

                this.select2 = $(this.$refs.select).select2();
                this.select2.on("select2:select", (event) => {
                    var values = [];
                    var old_values = [];

                    // copy all option values from selected
                    $(event.currentTarget).find("option:selected").each(function(i, selected) {
                        values[i] = $(selected).val();
                    });

                    this.selectedQuestionsIds = values;
                    console.log(this.allQuestions);
                    var items = [];

                    this.allQuestions.forEach((item) => {
                        if (values.includes(item.id.toString())) {
                            items.push(item);
                        }

                    });

                    this.selectedQuestions = items;



                });
                this.select2.on("select2:unselect", (event) => {
                    var values = [];
                    $(event.currentTarget).find("option:selected").each(function(i, selected) {
                        values[i] = $(selected).val();
                    });

                    this.selectedQuestionsIds = values;
                    console.log(values);
                    var items = [];

                    this.allQuestions.forEach((item) => {
                        console.log(values);
                        console.log(item.id);
                        if (values.includes(item.id.toString())) {

                            items.push(item);
                        }

                    });

                    this.selectedQuestions = items;


                });
            }
        </script>


        <script>
            ClassicEditor
                .create(document.querySelector('#image_ckeditorx'), {
                    ckfinder: {
                        uploadUrl: "{{ route('ckeditor.upload', ['_token' => csrf_token()]) }}"
                    },
                })
                .catch(error => {
                    console.error(error);
                });

            // ClassicEditor
            //     .create(document.querySelector('#image_ckeditor_2'), {
            //         ckfinder: {
            //             uploadUrl: "{{ route('ckeditor.upload', ['_token' => csrf_token()]) }}"
            //         },
            //     })
            //     .catch(error => {
            //         console.error(error);
            //     });
        </script>
        @if (app()->getLocale() == 'ar')
            <script src="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-datepicker/1.9.0/locales/bootstrap-datepicker.ar.min.js"></script>
        @endif
        {{-- @include('map::set-leafletmap') --}}
        <script>
            var max_days = '{{ $setting->job_deadline_expiration_limit }}'

            //init datepicker
            $("#date").attr("autocomplete", "off");
            //init datepicker
            $('#date').off('focus').datepicker({
                format: 'dd-mm-yyyy',
                startDate: '0d',
                endDate: `+${max_days}d`,
                isRTL: "{{ app()->getLocale() == 'ar' ? true : false }}",
                language: "{{ app()->getLocale() }}",
            }).on('click',
                function() {
                    $(this).datepicker('show');
                }
            );
        </script>
        {{-- @include('map::set-googlemap') --}}


        <script>
            var salary_mode = "{!! old('salary_mode') !!}";

            if (salary_mode) {
                salaryModeChange(salary_mode);
            }

            function salaryModeChange(param) {
                var value = param;

                if (value === 'range') {
                    $('#custom_salary_part').addClass('d-none');
                    $('.salary_range_part').removeClass('d-none');
                    $('#salary_rangee').prop('checked', true)
                    $('#custom_salary').prop('checked', false)
                } else {
                    $('#custom_salary_part').removeClass('d-none');
                    $('.salary_range_part').addClass('d-none');
                    $('#salary_rangee').prop('checked', false)
                    $('#custom_salary').prop('checked', true)
                }
            }

            function RadioChecked(param) {
                var value = param;
                if (value === 'email') {
                    $('#applied_on_email').addClass('checked');
                    $('#apply_on_custom_url').addClass('d-none');
                    $('#apply_on_email').removeClass('d-none');
                    $('#applied_on_app').removeClass('checked');
                    $('#applied_on_custom_url').removeClass('checked');
                }
                if (value === 'custom_url') {
                    $('#applied_on_custom_url').addClass('checked');
                    $('#apply_on_email').addClass('d-none');
                    $('#apply_on_custom_url').removeClass('d-none');
                    $('#applied_on_app').removeClass('checked');
                    $('#applied_on_email').removeClass('checked');
                }
                if (value === 'app') {
                    $('#applied_on_app').addClass('checked');
                    $('#applied_on_email').removeClass('checked');
                    $('#applied_on_custom_url').removeClass('checked');
                    $('#apply_on_email').addClass('d-none');
                    $('#apply_on_custom_url').addClass('d-none');
                }
            }
            $('.radio-check').on('click', function() {
                $('input:radio', this).prop('checked', true);
            });

            if ($('#app-app').is(':checked')) {
                $('#applied_on_app').addClass('checked');
            }
            if ($('#app-custom_url').is(':checked')) {
                $('#apply_on_custom_url').removeClass('d-none');
            }
            if ($('#app-email').is(':checked')) {
                $('#apply_on_email').removeClass('d-none');
            }

            var apply_url = "{!! $errors->first('apply_url') !!}";
            var apply_url1 = "{!! old('apply_email') !!}";
            var apply_email = "{!! $errors->first('apply_email') !!}";
            var apply_email1 = "{!! old('apply_email') !!}";

            if (apply_url) {
                $('#apply_on_custom_url').removeClass('d-none');
            }
            if (apply_url1) {
                $('#apply_on_custom_url').removeClass('d-none');
            }
            if (apply_email) {
                $('#apply_on_email').removeClass('d-none');
            }
            if (apply_email1) {
                $('#apply_on_email').removeClass('d-none');
            }


            function showHideCreateBenefit() {
                $('#create_benefit').toggleClass('d-none');
            }

            function createBenefit() {
                var benefit = $('input[name="new_benefit"]').val();

                if (benefit == '') {
                    alert('Please enter benefit name');
                    return false;
                }

                axios.post("/job/benefits/create", {
                    benefit: benefit
                }).then((response) => {
                    var data = response.data;

                    if (data.length && typeof data == 'string') {
                        return Swal.fire('Error', data, 'error');
                    }

                    $('#benefit_list').append(`<label for="benefit_${data.id}">
                    <input type="checkbox" id="benefit_${data.id}" name="benefits[]" value="${data.id}">
                    <span>${data.name}</span>
                </label>`);

                    $('input[name="new_benefit"]').val('');
                }).catch((err) => {
                    this.errors = err.response.data.errors;
                });
            }

            // Toggle disability types container based on checkbox
            $(document).ready(function() {
                // Check initial state
                if ($('#disability_friendly').is(':checked')) {
                    $('#disability_types_container').show();
                } else {
                    $('#disability_types_container').hide();
                }

                // Handle checkbox change
                $('#disability_friendly').change(function() {
                    if ($(this).is(':checked')) {
                        $('#disability_types_container').slideDown();
                    } else {
                        $('#disability_types_container').slideUp();
                    }
                });
            });
        </script>
    @endsection
