@extends('frontend.layouts.app')

@section('title')
    {{ __('settings') }}
@endsection

@section('css')
    <link rel="stylesheet" href="{{ asset('frontend/assets/css/modern-setting.css') }}">
@endsection

@section('main')
    <div class="dashboard-wrapper">
        <div class="container">
            <div class="row">
                {{-- Sidebar --}}
                <x-website.company.sidebar />

                <div class="col-lg-9">
                    <div class="dashboard-right tw-ps-0 lg:tw-ps-5 tw-mt-4 lg:tw-mt-0">
                        <div class="tw-flex tw-justify-between tw-items-center mb-4">
                            <div>
                                <h5 class="tw-mb-0 tw-text-[#18191C] tw-text-2xl tw-font-medium">{{ __('settings') }}</h5>
                                <p class="text-muted mt-1 d-none d-md-block">Kelola informasi dan pengaturan perusa<PERSON>an <PERSON>a</p>
                            </div>
                            <span class="sidebar-open-nav d-lg-none">
                                <i class="ph-list"></i>
                            </span>
                        </div>
                        <div class="modern-card cadidate-dashboard-tabs company tw-min-h-[500px]">
                            <!-- Tab indicator for mobile -->
                            <div class="d-md-none tab-indicator-mobile p-3 bg-light border-bottom">
                                <div class="d-flex justify-content-between align-items-center">
                                    <span class="tab-count">Tab <span class="current-tab">1</span> dari 4</span>
                                    <div class="tab-dots">
                                        <span class="tab-dot active" data-tab="1"></span>
                                        <span class="tab-dot" data-tab="2"></span>
                                        <span class="tab-dot" data-tab="3"></span>
                                        <span class="tab-dot" data-tab="4"></span>
                                    </div>
                                    <span class="swipe-indicator">
                                        <i class="fas fa-chevron-left me-1"></i>
                                        Geser
                                        <i class="fas fa-chevron-right ms-1"></i>
                                    </span>
                                </div>
                            </div>
                            <div class="tw-overflow-x-auto p-3">
                                <ul class="nav nav-pills tw-gap-x-2 modern-tabs" id="pills-tab" role="tablist">
                                    <li class="nav-item" role="presentation">
                                        <button
                                            class="nav-link tw-px-3 {{ !session('type') || session('type') == 'personal' ? 'active' : '' }}"
                                            id="pills-personal-tab" data-bs-toggle="pill" data-bs-target="#pills-personal"
                                            type="button" role="tab" aria-controls="pills-personal"
                                            aria-selected="true">
                                            <x-svg.user-icon />
                                            {{ __('company_info') }}
                                        </button>
                                    </li>
                                    <li class="nav-item" role="presentation">
                                        <button class="nav-link tw-px-3 {{ session('type') == 'profile' ? 'active' : '' }}"
                                            id="pills-profile-tab" data-bs-toggle="pill" data-bs-target="#pills-profile"
                                            type="button" role="tab" aria-controls="pills-profile"
                                            aria-selected="false">
                                            <x-svg.user-round-icon />
                                            {{ __('founding_info') }}
                                        </button>
                                    </li>
                                    <li class="nav-item" role="presentation">
                                        <button class="nav-link tw-px-3 {{ session('type') == 'social' ? 'active' : '' }}"
                                            id="pills-social-tab" data-bs-toggle="pill" data-bs-target="#pills-social"
                                            type="button" role="tab" aria-controls="pills-social"
                                            aria-selected="false">
                                            <x-svg.globe2-icon />
                                            {{ __('social_media_profile') }}
                                        </button>
                                    </li>
                                    <li class="nav-item" role="presentation">
                                        <button
                                            class="nav-link tw-px-3 {{ session('type') == 'account' || session('type') == 'password' || session('type') == 'account-delete' || session('type') == 'contact' ? 'active' : '' }} @error('password') active @enderror"
                                            id="pills-setting-tab" data-bs-toggle="pill" data-bs-target="#pills-setting"
                                            type="button" role="tab" aria-controls="pills-setting"
                                            aria-selected="false">
                                            <x-svg.cog-icon />
                                            {{ __('account_setting') }}
                                        </button>
                                    </li>
                                    <span class="glider"></span>
                                </ul>
                            </div>
                            <div class="tab-content p-4" id="pills-tabContent">
                                <div class="tab-pane fade {{ session('type') == 'personal' ? 'show active' : '' }} {{ (session('type') ? false : true) ? 'show active' : '' }}"
                                    id="pills-personal" role="tabpanel" aria-labelledby="pills-personal-tab">
                                    <form action="{{ route('company.settingUpdateInformation') }}" method="POST"
                                        enctype="multipart/form-data" class="modern-form">
                                        @csrf
                                        @method('PUT')
                                        <input type="hidden" value="personal" name="type">
                                        <div class="form-section modern-card">
                                            <h6 class="section-title">{{ __('logo_banner_image') }}</h6>
                                            <div class="row">
                                                <x-website.company.photo-section :user="$user" />
                                                <x-website.company.banner-section :user="$user" />
                                            </div>
                                        </div>
                                        <div class="form-section modern-card">
                                            <h6 class="section-title">{{ __('employers_information') }}</h6>
                                            <div class="row">
                                                <div class="col-lg-6 mb-3">
                                                    <x-forms.label name="company_name" required="true"
                                                        class="pointer body-font-4 d-block text-gray-900 rt-mb-8" />
                                                    <div class="fromGroup">
                                                        <div class="form-control-icon">
                                                            <x-forms.input type="text" name="name"
                                                                value="{{ formatCompanyName($user->company) }}" placeholder="name"
                                                                id="name" disabled class="form-control" />
                                                            <small class="text-muted">Nama perusahaan tidak dapat diubah di sini</small>

                                                            @error('name')
                                                                <span class="text-danger">{{ $message }}</span>
                                                            @enderror
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="col-lg-12 mb-3">
                                                    <x-forms.label :required="false" name="about_us"
                                                        class="pointer body-font-4 d-block text-gray-900 rt-mb-8" />
                                                    <textarea class="form-control ckedit  @error('about_us') is-invalid @enderror" name="about_us" id="image_ckeditor">
                                                    {!! $user->company->bio !!}</textarea>
                                                    @error('about_us')
                                                        <span class="text-danger">{{ $message }}</span>
                                                    @enderror
                                                </div>
                                                <div class="col-lg-12 mt-4">
                                                    <button type="submit" class="btn btn-primary modern-btn modern-btn-primary">
                                                        <i class="fas fa-save me-2"></i>{{ __('save_changes') }}
                                                    </button>
                                                </div>
                                            </div>
                                        </div>
                                    </form>
                                </div>
                                <div class="tab-pane fade {{ session('type') == 'profile' ? 'show active' : '' }}"
                                    id="pills-profile" role="tabpanel" aria-labelledby="pills-profile-tab">
                                    <div class="form-section modern-card">
                                        <form action="{{ route('company.settingUpdateInformation') }}" method="POST" class="modern-form">
                                            @csrf
                                            @method('PUT')
                                            <input type="hidden" name="type" value="profile">
                                            <h6 class="section-title">{{ __('founding_info') }}</h6>
                                            <div class="row">
                                                <div class="col-lg-4 mb-3">
                                                    <x-forms.label name="organization_type"
                                                        class="body-font-4 d-block text-gray-900 rt-mb-8" />
                                                    <select name="organization_type" class="select2-taggable w-100-p">
                                                        @foreach ($organization_types as $organization_type)
                                                            <option
                                                                {{ $user->company->organization_type_id == $organization_type->id ? 'selected' : '' }}
                                                                value="{{ $organization_type->id }}">
                                                                {{ $organization_type->name }}</option>
                                                        @endforeach
                                                    </select>
                                                </div>
                                                <div class="col-lg-4 mb-3">
                                                    <x-forms.label name="industry_type"
                                                        class="body-font-4 d-block text-gray-900 rt-mb-8" />
                                                    <select name="industry_type" class="select2-industry-type form-control w-100-p" data-placeholder="{{ __('Pilih Jenis Industri') }}">
                                                        <option value=""></option>
                                                        @foreach ($industry_types as $industry_type)
                                                            <option
                                                                {{ $user->company->industry_type_id == $industry_type->id ? 'selected' : '' }}
                                                                value="{{ $industry_type->id }}">
                                                                {{ $industry_type->name }}</option>
                                                        @endforeach
                                                    </select>
                                                </div>
                                                <div class="col-lg-4 mb-3">
                                                    <x-forms.label name="team_size"
                                                        class="body-font-4 d-block text-gray-900 rt-mb-8"
                                                        :required="false" />
                                                    <select name="team_size" class="rt-selectactive w-100-p">
                                                        @foreach ($team_sizes as $team_size)
                                                            <option
                                                                {{ $user->company->team_size_id == $team_size->id ? 'selected' : '' }}
                                                                value="{{ $team_size->id }}">
                                                                {{ $team_size->name }}</option>
                                                        @endforeach
                                                    </select>
                                                </div>
                                                <div class="col-lg-6 mb-3">
                                                    <x-forms.label name="year_of_establishment"
                                                        class="body-font-4 d-block text-gray-900 rt-mb-8"
                                                        :required="false" />
                                                    <div class="fromGroup">
                                                        <div class="form-control-icon">
                                                            <input type="number" name="establishment_date"
                                                                value="{{ $user->company->establishment_date ? date('Y', strtotime($user->company->establishment_date)) : old('establishment_date') }}"
                                                                id="establishment_year" placeholder="Contoh: 2010"
                                                                min="1900" max="{{ date('Y') }}"
                                                                class="form-control @error('establishment_date') is-invalid @enderror" />
                                                            <small class="text-muted">Masukkan tahun pendirian perusahaan (1900-{{ date('Y') }})</small>
                                                        </div>
                                                        @error('establishment_date')
                                                            <span class="text-danger">{{ __($message) }}</span>
                                                        @enderror
                                                    </div>
                                                </div>
                                                <div class="col-lg-6 mb-3">
                                                    <x-forms.label name="website" :required="false"
                                                        class="body-font-4 d-block text-gray-900 rt-mb-8" />
                                                    <div class="fromGroup has-icon2">
                                                        <div class="form-control-icon">
                                                            <x-forms.input type="text" name="website" id="website_input_setting"
                                                                value="{{ $user->company->website }}"
                                                                placeholder="Contoh: websitesaya.com" class="" />
                                                            <div class="icon-badge-2">
                                                                <x-svg.link-icon />
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <small class="text-muted">Cukup masukkan nama domain, https:// akan ditambahkan otomatis</small>
                                                </div>
                                                <div class="col-lg-12 mb-3">
                                                    <label class="body-font-4 d-block text-gray-900 rt-mb-8">
                                                        Visi dan Misi Perusahaan
                                                    </label>
                                                    <textarea name="vision" class="ckedit" id="image_ckeditor_2" placeholder="Tuliskan visi dan misi perusahaan Anda">{{ $user->company->vision }}</textarea>
                                                </div>
                                                <div class="col-lg-12 mt-4">
                                                    <button type="submit" class="btn btn-primary modern-btn modern-btn-primary">
                                                        <i class="fas fa-save me-2"></i>{{ __('save_changes') }}
                                                    </button>
                                                </div>
                                            </div>
                                        </form>
                                    </div>
                                </div>
                                <div class="tab-pane fade {{ session('type') == 'social' ? 'show active' : '' }}"
                                    id="pills-social" role="tabpanel" aria-labelledby="pills-social-tab">
                                    <div class="form-section modern-card">
                                        <form action="{{ route('company.settingUpdateInformation') }}" method="POST" class="modern-form">
                                            @csrf
                                            @method('PUT')
                                            <input type="hidden" value="social" name="type">
                                            <h6 class="section-title">{{ __('social_media_profile') }}</h6>
                                            <div class="row">
                                                @forelse($socials as $social)
                                                    <div class="col-12 mb-3">
                                                        <div class="social-media-item">
                                                            <div class="social-media-select">
                                                                <select class="form-control" name="social_media[]">
                                                                    <option value="" class="d-none" disabled>
                                                                        {{ __('select_one') }}</option>
                                                                    <option
                                                                        {{ $social->social_media == 'facebook' ? 'selected' : '' }}
                                                                        value="facebook">{{ __('facebook') }}
                                                                    </option>
                                                                    <option
                                                                        {{ $social->social_media == 'twitter' ? 'selected' : '' }}
                                                                        value="twitter">{{ __('twitter') }}
                                                                    </option>
                                                                    <option
                                                                        {{ $social->social_media == 'instagram' ? 'selected' : '' }}
                                                                        value="instagram">{{ __('instagram') }}
                                                                    </option>
                                                                    <option
                                                                        {{ $social->social_media == 'youtube' ? 'selected' : '' }}
                                                                        value="youtube">{{ __('youtube') }}
                                                                    </option>
                                                                    <option
                                                                        {{ $social->social_media == 'linkedin' ? 'selected' : '' }}
                                                                        value="linkedin">{{ __('linkedin') }}
                                                                    </option>
                                                                    <option
                                                                        {{ $social->social_media == 'pinterest' ? 'selected' : '' }}
                                                                        value="pinterest">{{ __('pinterest') }}
                                                                    </option>
                                                                    <option
                                                                        {{ $social->social_media == 'reddit' ? 'selected' : '' }}
                                                                        value="reddit">{{ __('reddit') }}
                                                                    </option>
                                                                    <option
                                                                        {{ $social->social_media == 'github' ? 'selected' : '' }}
                                                                        value="github">{{ __('github') }}
                                                                    </option>
                                                                    <option
                                                                        {{ $social->social_media == 'other' ? 'selected' : '' }}
                                                                        value="other">{{ __('other') }}
                                                                    </option>
                                                                </select>
                                                            </div>
                                                            <div class="social-media-url">
                                                                <input class="form-control social-url-input" type="text" name="url[]"
                                                                    placeholder="Masukkan username..."
                                                                    value="{{ $social->url }}"
                                                                    data-platform="{{ $social->social_media }}">
                                                            </div>
                                                            <div class="social-media-action">
                                                                <button
                                                                    class="btn btn-outline-danger btn-sm"
                                                                    type="button" id="remove_item">
                                                                    <i class="fas fa-times"></i>
                                                                </button>
                                                            </div>
                                                        </div>
                                                    </div>
                                                @empty
                                                    <div class="col-12 mb-3">
                                                        <div class="social-media-item">
                                                            <div class="social-media-select">
                                                                <select class="form-control" name="social_media[]">
                                                                    <option value="" class="d-none" disabled
                                                                        selected>{{ __('select_one') }}</option>
                                                                    <option value="facebook">{{ __('facebook') }}</option>
                                                                    <option value="twitter">{{ __('twitter') }}</option>
                                                                    <option value="instagram">{{ __('instagram') }}</option>
                                                                    <option value="youtube">{{ __('youtube') }}</option>
                                                                    <option value="linkedin">{{ __('linkedin') }}</option>
                                                                    <option value="pinterest">{{ __('pinterest') }}</option>
                                                                    <option value="reddit">{{ __('reddit') }}</option>
                                                                    <option value="github">{{ __('github') }}</option>
                                                                    <option value="other">{{ __('other') }}</option>
                                                                </select>
                                                            </div>
                                                            <div class="social-media-url">
                                                                <input class="form-control social-url-input" type="text" name="url[]"
                                                                    placeholder="Masukkan username...">
                                                            </div>
                                                            <div class="social-media-action">
                                                                <button
                                                                    class="btn btn-outline-danger btn-sm"
                                                                    type="button" id="remove_item">
                                                                    <i class="fas fa-times"></i>
                                                                </button>
                                                            </div>
                                                        </div>
                                                    </div>
                                                @endforelse
                                                <div id="multiple_feature_part2">
                                                </div>
                                                <div class="col-12">
                                                    <button class="btn btn-outline-primary modern-btn modern-btn-outline w-100 add-new-social"
                                                        onclick="add_features_field()" type="button">
                                                        <i class="fas fa-plus-circle me-2"></i>
                                                        <span>{{ __('add_new_social_link') }}</span>
                                                    </button>
                                                </div>
                                            </div>
                                    </div>
                                    <button type="submit" class="btn btn-primary modern-btn modern-btn-primary mt-4">
                                        <i class="fas fa-save me-2"></i>{{ __('save_changes') }}
                                    </button>
                                    </form>
                                </div>
                                <div class="tab-pane fade {{ session('type') == 'account' || session('type') == 'password' || session('type') == 'account-delete' || session('type') == 'contact' || session('type') == 'account' ? 'show active' : '' }} @error('password') show active @enderror"
                                    id="pills-setting" role="tabpanel" aria-labelledby="pills-setting-tab">
                                    {{-- Google map key wrong warning  --}}
                                    <form action="{{ route('company.settingUpdateInformation') }}" method="POST" class="modern-form">
                                        @csrf
                                        @method('put')
                                        <input type="hidden" name="type" value="contact">
                                        <input type="hidden" name="lat" id="lat_input" value="{{ $user->company->lat }}">
                                        <input type="hidden" name="long" id="long_input" value="{{ $user->company->long }}">
                                        <div class="form-section modern-card">
                                            <x-website.map.map-warning />
                                            <h6 class="section-title">
                                                {{ __('company_location') }}
                                                <small>
                                                    {{ __('Masukkan Detail Lokasi Perusahaan Anda') }}
                                                </small>
                                            </h6>
                                            @if (config('templatecookie.map_show'))
                                                <div class="row">
                                                    <div class="col-12">
                                                        <div class="map-container">
                                                            <div id="google-map-div"
                                                                class="{{ $setting->default_map == 'google-map' ? '' : 'd-none' }}">
                                                                <input id="searchInput" class="form-control mb-3" type="text"
                                                                    placeholder="Enter a location">
                                                                <div class="map mymap" id="google-map"></div>
                                                            </div>
                                                            <div class="{{ $setting->default_map == 'leaflet' ? '' : 'd-none' }}">
                                                                <input type="text" autocomplete="off" id="leaflet_search"
                                                                    placeholder="{{ __('enter_city_name') }}" class="form-control mb-3"
                                                                    value="{{ $user->company->exact_location ? $user->company->exact_location : $user->company->full_address }}" />
                                                                <div id="leaflet-map"></div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                    @error('location')
                                                        <span class="ml-3 text-md text-danger">{{ $message }}</span>
                                                    @enderror
                                                </div>
                                                @php
                                                    $session_location = session()->get('location');
                                                    $session_country = $session_location && array_key_exists('country', $session_location) ? $session_location['country'] : '-';
                                                    $session_exact_location = $session_location && array_key_exists('exact_location', $session_location) ? $session_location['exact_location'] : '-';

                                                    $company_country = $user->company->country;
                                                    $company_exact_location = $user->company->exact_location;
                                                @endphp
                                                <div class="card-footer row mt-4 border-0">
                                                    <div class="col-12">
                                                        <div class="d-flex align-items-center">
                                                            <span>
                                                                <img src="{{ asset('frontend/assets/images/loader.gif') }}"
                                                                    alt="loading" width="30px" height="30px"
                                                                    class="loader_position d-none me-2">
                                                            </span>
                                                            <div class="location_secion bg-light p-3 rounded w-100">
                                                                <div class="mb-2">
                                                                    <strong>{{ __('country') }}:</strong>
                                                                    <span class="location_country">{{ $company_country ?: $session_country }}</span>
                                                                </div>
                                                                <div>
                                                                    <strong>{{ __('full_address') }}:</strong>
                                                                    <span class="location_full_address">{{ $company_exact_location ?: $session_exact_location }}</span>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            @else
                                                @php
                                                    session([
                                                        'selectedCountryId' => null,
                                                        'selectedStateId' => null,
                                                        'selectedCityId' => null,
                                                        'selectedKecamatanId' => null,
                                                        'selectedKelurahanId' => null,
                                                    ]);
                                                    session([
                                                        'selectedCountryId' => $user->company->country,
                                                        'selectedStateId' => $user->company->region,
                                                        'selectedCityId' => $user->company->district,
                                                        'selectedKecamatanId' => $user->company->locality,
                                                        'selectedKelurahanId' => $user->company->neighborhood,
                                                    ]);
                                                @endphp
                                                <div class="row">
                                                    <div class="col-12">
                                                        @livewire('country-state-city')

                                                        <!-- Hidden inputs untuk mengirim data lokasi -->
                                                        <input type="hidden" name="country" id="country" value="">
                                                        <input type="hidden" name="region" id="region" value="">
                                                        <input type="hidden" name="district" id="district" value="">
                                                        <input type="hidden" name="locality" id="locality" value="">
                                                        <input type="hidden" name="neighborhood" id="neighborhood" value="">
                                                        <input type="hidden" name="kecamatan_id" id="kecamatan_id" value="">
                                                        <input type="hidden" name="kelurahan_id" id="kelurahan_id" value="">
                                                    </div>
                                                </div>

                                                <!-- Alamat Perusahaan -->
                                                <div class="row mt-3">
                                                    <div class="col-12">
                                                        <x-forms.label name="Alamat Perusahaan" :required="false" class="tw-text-sm tw-mb-2" />
                                                        <textarea class="form-control" rows="3" disabled>{{ $user->alamat_ktp ?? $user->company->address }}</textarea>
                                                        {{-- <small class="text-muted">Alamat perusahaan diambil dari data KTP</small> --}}
                                                    </div>
                                                </div>

                                            @endif
                                        </div>

                                        <div class="form-section modern-card mt-4">
                                            <h6 class="section-title">{{ __('company_contact_public') }}</h6>
                                            <div class="row">
                                                <div class="col-lg-6 mb-3">
                                                    <x-forms.label :required="false" name="phone"
                                                        class="pointer tw-text-sm d-block text-gray-900 rt-mb-8" />
                                                    <div class="input-group">
                                                        <span class="input-group-text"><i class="fa-solid fa-phone"></i></span>
                                                        <x-forms.input type="text" id="phone_input_setting" name="phone"
                                                            value="{{ $contact->phone }}"
                                                            placeholder="{{ __('phone_number') }}" class="form-control phonecode" />
                                                    </div>
                                                </div>
                                                <div class="col-lg-6 mb-3">
                                                    <x-forms.label :required="false" name="email"
                                                        class="pointer tw-text-sm d-block text-gray-900 rt-mb-8" />
                                                    <div class="input-group">
                                                        <span class="input-group-text"><i class="fas fa-envelope"></i></span>
                                                        <x-forms.input type="email" name="email"
                                                            value="{{ $contact->email }}"
                                                            placeholder="{{ __('email_address') }}" class="form-control" />
                                                    </div>
                                                </div>

                                            </div>
                                            <button type="submit" class="btn btn-primary modern-btn modern-btn-primary">
                                                <i class="fas fa-save me-2"></i>{{ __('save_changes') }}
                                            </button>
                                        </div>

                                    </form>
                                    <form action="{{ route('company.settingUpdateInformation') }}" method="POST">
                                        @csrf
                                        @method('put')
                                        <input type="hidden" name="type" value="account">
                                        <div class="form-section modern-card">
                                            <h6 class="section-title">{{ __('change_account_user_name_and_email') }} </h6>
                                            <div class="row mb-4">

                                                {{-- user name update --}}
                                                <div class="col-lg-8 mt-2">
                                                    <div class="mb-2">
                                                        <x-forms.label :required="false" name="username"
                                                            class="pointer tw-text-sm d-block text-gray-900 rt-mb-8" />
                                                        <x-forms.input type="text" id="username" name="username"
                                                            value="{{ $user->username }}"
                                                            placeholder="{{ __('username') }}" class="phonecode" disabled />
                                                        <small class="text-muted">Username tidak dapat diubah</small>
                                                    </div>
                                                    <p><b>{{ __('profile_link') }}: </b>
                                                        <a href="{{ config('app.url') }}/perusahaan/{{ $user->username }}"
                                                            target="_blank"> {{ config('app.url') }}/perusahaan/<span
                                                                id="profile_username">{{ $user->username }}</span>
                                                        </a>
                                                    </p>
                                                </div>

                                                {{-- emailupdate --}}
                                                <div class="col-lg-4 mt-2">
                                                    <x-forms.label :required="true" name="email"
                                                        class="f-size-14 text-gray-700 rt-mb-8" />
                                                    <div class="fromGroup rt-mb-15">
                                                        <input name="account_email" value="{{ auth()->user()->email }}"
                                                            class="form-control @error('account_email') is-invalid @enderror"
                                                            id="account_email" type="email"
                                                            placeholder="{{ __('email_address') }}" disabled>
                                                        <small class="text-muted">Email tidak dapat diubah di sini</small>
                                                    </div>
                                                    @error('account_email')
                                                        <span class="text-danger">{{ $message }}</span>
                                                    @enderror
                                                </div>

                                            </div>
                                            <button type="submit" class="btn btn-primary modern-btn modern-btn-primary">
                                                <i class="fas fa-save me-2"></i>{{ __('save_changes') }}
                                            </button>
                                        </div>
                                    </form>

                                    <hr>
                                    <div class="form-section modern-card mt-4">
                                        <h6 class="section-title">{{ __('change_password') }}</h6>
                                        <form action="{{ route('company.settingUpdateInformation') }}" method="POST" class="modern-form">
                                            @csrf
                                            @method('put')
                                            <input type="hidden" name="type" value="password">
                                            <div class="row">
                                                <div class="col-lg-6 mb-4">
                                                    <x-forms.label :required="true" name="new_password"
                                                        class="f-size-14 text-gray-700 rt-mb-8" />
                                                    <div class="input-group">
                                                        <input name="password"
                                                            class="form-control @error('password') is-invalid @enderror"
                                                            id="password-hide_show" type="password"
                                                            placeholder="{{ __('password') }}" required="">
                                                        <span class="input-group-text">
                                                            <i class="ph-eye @error('password') m-3 @enderror"></i>
                                                        </span>
                                                    </div>
                                                    @error('password')
                                                        <span class="text-danger">{{ $message }}</span>
                                                    @enderror
                                                </div>
                                                <div class="col-lg-6 mb-4">
                                                    <x-forms.label :required="true" name="confirm_password"
                                                        class="f-size-14 text-gray-700 rt-mb-8" />
                                                    <div class="input-group">
                                                        <input name="password_confirmation"
                                                            class="form-control @error('password_confirmation') is-invalid @enderror"
                                                            id="password-hide_show1" type="password"
                                                            placeholder="{{ __('confirm_password') }}" required="">
                                                        <span class="input-group-text">
                                                            <i class="ph-eye"></i>
                                                        </span>
                                                    </div>
                                                    @error('password_confirmation')
                                                        <span class="text-danger">{{ $message }}</span>
                                                    @enderror
                                                </div>
                                                <div>
                                                    <button type="submit" class="btn btn-primary">
                                                        <i class="fas fa-save me-2"></i>{{ __('save_changes') }}
                                                    </button>
                                                </div>
                                            </div>
                                        </form>
                                    </div>
                                    <div class="form-section modern-card mt-4">
                                        <div class="row">
                                            <div class="col-lg-12">
                                                <h6 class="section-title text-danger">{{ __('close') }}/{{ __('delete') }} {{ __('account') }}</h6>
                                                <div class="alert alert-warning">
                                                    <p class="mb-0">{{ __('account_delete_msg') }}</p>
                                                </div>
                                                <form action="{{ route('company.settingUpdateInformation') }}"
                                                    id="AccountDelete" method="POST">
                                                    @csrf
                                                    @method('put')
                                                    <input type="hidden" name="type" value="account-delete">
                                                    <button type="button" onclick="AccountDelete()"
                                                        class="btn btn-outline-danger modern-btn">
                                                        <i class="fas fa-trash-alt me-2"></i>
                                                        <span>{{ __('close_account') }}</span>
                                                    </button>
                                                </form>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Floating scroll indicator for mobile -->
                        <div class="scroll-indicator d-md-none">
                            <i class="fas fa-arrow-right"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="dashboard-footer text-center body-font-4 text-gray-500">
            <x-website.footer-copyright />
        </div>
    </div>
@endsection

@section('css')
    <link rel="stylesheet" href="{{ asset('backend') }}/plugins/select2/css/select2.min.css">
    <link rel="stylesheet" href="{{ asset('frontend') }}/assets/css/bootstrap-datepicker.min.css">
    <!-- >=>Leaflet Map<=< -->
    <x-map.leaflet.map_links />
    <x-map.leaflet.autocomplete_links />
    @include('map::links')
    <style>
        .ck-editor__editable_inline {
            min-height: 350px;
        }

        .input-group-text-custom {
            max-height: 48px;
            padding: 12px;
            background-color: #e9ecef;
            border-radius: 0 5px 5px 0;
        }

        .has-badge-cutom {
            top: 34% !important;
        }

        .border-cutom {
            border-radius: 5px 0 0 5px !important;
        }

        /* Modern styling overrides */
        .mymap {
            border-radius: 12px;
            height: 300px;
        }

        .input-group-text {
            background-color: #f8f9fa;
            border-color: #ced4da;
        }

        .input-group-text i {
            color: #138C79;
        }

        .form-control:focus {
            border-color: #138C79;
            box-shadow: 0 0 0 0.2rem rgba(19, 140, 121, 0.25);
        }

        /* Select2 Styling */
        .select2-container--default .select2-selection--single {
            height: 38px;
            border-radius: 8px;
            border: 1px solid #ced4da;
        }

        .select2-container--default .select2-selection--single .select2-selection__rendered {
            line-height: 38px;
            padding-left: 12px;
        }

        .select2-container--default .select2-selection--single .select2-selection__arrow {
            height: 36px;
            right: 6px;
        }

        /* Fix double arrow in select2 */
        .select2-container--default .select2-selection--single .select2-selection__arrow b {
            display: block !important;
        }

        .form-select .select2-selection__arrow b,
        .form-control .select2-selection__arrow b {
            display: none !important;
        }

        /* Styling khusus untuk select2-industry-type */
        .select2-container--bootstrap4.select2-container--focus .select2-selection {
            border-color: #138C79;
            box-shadow: 0 0 0 0.2rem rgba(19, 140, 121, 0.25);
        }

        .select2-container--bootstrap4 .select2-selection {
            border: 1px solid #ced4da !important;
            border-radius: 8px !important;
            height: 38px !important;
        }

        .select2-container--bootstrap4 .select2-selection--single .select2-selection__rendered {
            line-height: 38px !important;
            padding-left: 12px !important;
        }

        .select2-container--bootstrap4 .select2-selection--single .select2-selection__arrow {
            height: 36px !important;
            right: 6px !important;
        }

        /* CKEditor Styling */
        .ck-editor__editable_inline {
            min-height: 350px;
            border: 1px solid #ced4da !important;
            border-top: none !important;
        }

        .ck.ck-toolbar {
            border: 1px solid #ced4da !important;
            border-radius: 8px 8px 0 0 !important;
        }

        .ck.ck-editor__main>.ck-editor__editable {
            border-radius: 0 0 8px 8px !important;
        }

        /* Tab indicator for mobile */
        .tab-indicator-mobile {
            background-color: #f8f9fa;
            border-bottom: 1px solid #e9ecef;
        }

        .tab-count {
            font-size: 14px;
            font-weight: 500;
            color: #6c757d;
        }

        .tab-dots {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .tab-dot {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background-color: #dee2e6;
            transition: all 0.3s ease;
        }

        .tab-dot.active {
            width: 12px;
            height: 12px;
            background-color: #138C79;
        }

        .swipe-indicator {
            font-size: 12px;
            color: #6c757d;
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0% { opacity: 0.7; }
            50% { opacity: 1; }
            100% { opacity: 0.7; }
        }

        /* Mobile responsiveness */
        @media (max-width: 768px) {
            .dashboard-right {
                padding: 15px !important;
            }

            .modern-card {
                padding: 15px !important;
            }

            .section-title {
                font-size: 16px;
            }

            .form-section {
                padding: 15px;
            }

            /* Enhanced tab styling for mobile */
            .modern-tabs {
                display: flex;
                overflow-x: auto;
                -webkit-overflow-scrolling: touch;
                scroll-snap-type: x mandatory;
                padding-bottom: 15px;
                margin-bottom: 0;
                position: relative;
            }

            .modern-tabs:after {
                content: "";
                position: absolute;
                right: 0;
                top: 0;
                bottom: 0;
                width: 40px;
                background: linear-gradient(to right, rgba(255,255,255,0), rgba(255,255,255,1));
                pointer-events: none;
                z-index: 1;
            }

            .modern-tabs .nav-item {
                flex: 0 0 auto;
                scroll-snap-align: start;
            }

            .nav-pills .nav-link {
                padding: 10px 16px;
                font-size: 14px;
                white-space: nowrap;
                border: 1px solid #e9ecef;
                margin-right: 8px;
                position: relative;
                overflow: hidden;
            }

            .nav-pills .nav-link:after {
                content: "";
                position: absolute;
                bottom: 0;
                left: 0;
                width: 100%;
                height: 3px;
                background-color: #138C79;
                transform: scaleX(0);
                transition: transform 0.3s ease;
            }

            .nav-pills .nav-link.active:after {
                transform: scaleX(1);
            }

            .nav-pills .nav-link.active {
                background-color: #fff;
                color: #138C79;
                border-color: #138C79;
                font-weight: 600;
            }

            /* Add floating scroll indicator */
            .scroll-indicator {
                position: fixed;
                bottom: 70px;
                right: 20px;
                width: 40px;
                height: 40px;
                border-radius: 50%;
                background-color: #138C79;
                color: white;
                display: flex;
                align-items: center;
                justify-content: center;
                box-shadow: 0 2px 10px rgba(0,0,0,0.2);
                z-index: 100;
                animation: bounce 2s infinite;
            }

            @keyframes bounce {
                0%, 20%, 50%, 80%, 100% {transform: translateY(0);}
                40% {transform: translateY(-10px);}
                60% {transform: translateY(-5px);}
            }
        }
    </style>
@endsection

@section('script')
    @livewireScripts
    <script src="https://ajax.googleapis.com/ajax/libs/jquery/3.5.1/jquery.min.js"></script>
    <script src="{{ asset('frontend/assets/js/bootstrap-datepicker.min.js') }}"></script>
    <script>
        $(document).ready(function() {
            $('.select21').select2();

            // Inisialisasi Select2 untuk jenis industri
            $('.select2-industry-type').select2({
                theme: 'bootstrap4',
                width: '100%',
                placeholder: function() {
                    return $(this).data('placeholder');
                },
                allowClear: true,
                tags: true,
                createTag: function(params) {
                    return {
                        id: params.term,
                        text: params.term,
                        newTag: true
                    };
                }
            });
        });

        window.addEventListener('render-select2', event => {
            console.log('fired');
            $('.select21').select2();

            // Reinisialisasi Select2 untuk jenis industri setelah render
            $('.select2-industry-type').select2({
                theme: 'bootstrap4',
                width: '100%',
                placeholder: function() {
                    return $(this).data('placeholder');
                },
                allowClear: true,
                tags: true,
                createTag: function(params) {
                    return {
                        id: params.term,
                        text: params.term,
                        newTag: true
                    };
                }
            });
        })
    </script>
    @stack('js')
    <script src="{{ asset('backend/plugins/select2/js/select2.full.min.js') }}"></script>

    {{-- Leaflet  --}}
    @include('map::set-edit-leafletmap', ['lat' => $user->company->lat, 'long' => $user->company->long])
    <script>
        $('#username').keyup(function() {
            var username = $(this).val();

            if (username.length) {
                axios.get('/check/username/' + username, {
                        params: {
                            type: "company_username"
                        }
                    })
                    .then(function(response) {
                        var exists = response.data

                        if (exists) {
                            $('#username').addClass('is-invalid')
                            $('#username_error').removeClass('d-none')
                            $('#username_submit_btn').attr('disabled', 'disabled')
                        } else {
                            $('#username').removeClass('is-invalid')
                            $('#username_error').addClass('d-none')
                            $('#username_submit_btn').removeAttr('disabled')
                        }

                        $('#profile_username').html(username);
                    })
            }
        });

        function UploadMode(param) {
            if (param === 'photo') {
                $('#photo-uploadMode').removeClass('d-none');
                $('#photo-oldMode').addClass('d-none');
            } else {
                $('#banner-uploadMode').removeClass('d-none');
                $('#banner-oldMode').addClass('d-none');
            }
        }
        //init datepicker
        // $("#date").attr("autocomplete", "off");
        // //init datepicker
        // $('#date').off('focus').datepicker({
        //     format: 'dd-mm-yyyy',
        //     isRTL: "{{ app()->getLocale() == 'ar' ? true : false }}",
        //     language: "{{ app()->getLocale() }}",
        // }).on('click',
        //     function() {
        //         $(this).datepicker('show');
        //     }
        // );
        $('#date').datepicker({
            format: "dd-mm-yyyy",
            autoclose: true
        });
        // feature field
        function add_features_field() {
            $("#multiple_feature_part2").append(`
            <div class="col-12 mb-3">
                <div class="social-media-item">
                    <div class="social-media-select">
                        <select class="form-control" name="social_media[]">
                            <option value="" class="d-none" disabled selected>{{ __('select_one') }}</option>
                            <option value="facebook">{{ __('facebook') }}</option>
                            <option value="twitter">{{ __('twitter') }}</option>
                            <option value="instagram">{{ __('instagram') }}</option>
                            <option value="youtube">{{ __('youtube') }}</option>
                            <option value="linkedin">{{ __('linkedin') }}</option>
                            <option value="pinterest">{{ __('pinterest') }}</option>
                            <option value="reddit">{{ __('reddit') }}</option>
                            <option value="github">{{ __('github') }}</option>
                            <option value="other">{{ __('other') }}</option>
                        </select>
                    </div>
                    <div class="social-media-url">
                        <input class="form-control" type="url" name="url[]" placeholder="{{ __('profile_link_url') }}...">
                    </div>
                    <div class="social-media-action">
                        <button class="btn btn-outline-danger btn-sm" type="button" id="remove_item">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                </div>
            </div>
        `);
            // Initialize select2 for new fields
            $('.social-media-select select').select2({
                theme: 'bootstrap4',
                width: '100%',
                minimumResultsForSearch: Infinity
            });
        }
        $(document).on("click", "#remove_item", function() {
            $(this).parent().parent().parent('div').remove();
        });

        $('#visibility').on('change', function() {
            $(this).submit();
        });
        $('#alert').on('change', function() {
            $(this).submit();
        });

        function AccountDelete() {
            if (confirm("{{ __('are_you_sure') }}") == true) {
                $('#AccountDelete').submit();
            } else {
                return false;
            }
        }
        setTimeout(function() {
            {{ session()->forget('type') }}
        }, 10000);

        var item = {!! $user->company !!};

        // Modern mobile responsive enhancements
        $(document).ready(function() {
            // Adjust padding on mobile
            function adjustLayout() {
                if ($(window).width() < 768) {
                    $('.dashboard-right').addClass('p-2');
                    $('.form-section').addClass('p-3');
                } else {
                    $('.dashboard-right').removeClass('p-2');
                    $('.form-section').removeClass('p-3');
                }
            }

            // Run on load
            adjustLayout();

            // Run on window resize
            $(window).resize(function() {
                adjustLayout();
            });

            // Add smooth scrolling when switching tabs
            $('.nav-link').on('click', function() {
                setTimeout(function() {
                    $('html, body').animate({
                        scrollTop: $('.tab-content').offset().top - 100
                    }, 300);
                }, 200);

                // Update tab indicator for mobile
                if ($(window).width() < 768) {
                    let tabIndex = $(this).parent().index() + 1;
                    $('.current-tab').text(tabIndex);

                    // Update active dot
                    $('.tab-dot').removeClass('active');
                    $(`.tab-dot[data-tab="${tabIndex}"]`).addClass('active');
                }
            });

            // Initialize tab indicator based on active tab
            if ($(window).width() < 768) {
                let activeTabIndex = $('.nav-link.active').parent().index() + 1;
                $('.current-tab').text(activeTabIndex);
                $('.tab-dot').removeClass('active');
                $(`.tab-dot[data-tab="${activeTabIndex}"]`).addClass('active');
            }

            // Scroll indicator click handler
            $('.scroll-indicator').on('click', function() {
                // Scroll to next tab
                let $tabs = $('.nav-link');
                let activeTabIndex = $('.nav-link.active').parent().index();
                let nextTabIndex = (activeTabIndex + 1) % $tabs.length;

                // Click the next tab
                $tabs.eq(nextTabIndex).click();

                // Scroll tabs container to show the next tab
                let $tabsContainer = $('.modern-tabs');
                let $nextTab = $('.nav-item').eq(nextTabIndex);
                let scrollPosition = $nextTab.position().left - 20;

                $tabsContainer.animate({
                    scrollLeft: scrollPosition
                }, 300);
            });

            // Tab dots click handler
            $('.tab-dot').on('click', function() {
                let tabIndex = $(this).data('tab') - 1;
                $('.nav-link').eq(tabIndex).click();
            });

            // Add swipe hint animation
            setTimeout(function() {
                if ($('.modern-tabs')[0].scrollWidth > $('.modern-tabs').width()) {
                    $('.swipe-indicator').addClass('active');

                    // Auto-hide after 5 seconds
                    setTimeout(function() {
                        $('.swipe-indicator').removeClass('active');
                    }, 5000);
                }
            }, 1000);
        });
    </script>

    @if ($setting->default_map == 'google-map')
        <!-- ============== google map ========= -->
        <x-website.map.google-map-check />
        <script>
            function initMap() {
                var token = "{{ $setting->google_map_key }}";
                var oldlat = parseFloat(item.lat);
                var oldlng = parseFloat(item.long);
                const map = new google.maps.Map(document.getElementById("google-map"), {
                    zoom: 7,
                    center: {
                        lat: oldlat,
                        lng: oldlng
                    },
                });
                const image =
                    "https://gisgeography.com/wp-content/uploads/2018/01/map-marker-3-116x200.png";
                const beachMarker = new google.maps.Marker({
                    draggable: true,
                    position: {
                        lat: oldlat,
                        lng: oldlng
                    },
                    map,
                    // icon: image
                });
                google.maps.event.addListener(map, 'click',
                    function(event) {
                        $('.loader_position').removeClass('d-none');
                        $('.location_secion').addClass('d-none');

                        pos = event.latLng
                        beachMarker.setPosition(pos);
                        let lat = beachMarker.position.lat();
                        let lng = beachMarker.position.lng();
                        axios.post(
                            `https://maps.googleapis.com/maps/api/geocode/json?latlng=${lat},${lng}&key=${token}`
                        ).then((data) => {
                            if (data.data.error_message) {
                                toastr.error(data.data.error_message, 'Error!');
                                toastr.error('Your location is not set because of a wrong API key.', 'Error!');
                            }

                            const total = data.data.results.length;
                            let amount = '';
                            if (total > 1) {
                                amount = total - 2;
                            }
                            const result = data.data.results.slice(amount);
                            let country = '';
                            let region = '';
                            for (let index = 0; index < result.length; index++) {
                                const element = result[index];
                                if (element.types[0] == 'country') {
                                    country = element.formatted_address;
                                }
                                if (element.types[0] == 'administrative_area_level_1') {
                                    const str = element.formatted_address;
                                    const first = str.split(',').shift()
                                    region = first;
                                }
                            }
                            var form = new FormData();
                            form.append('lat', lat);
                            form.append('lng', lng);
                            form.append('country', country);
                            form.append('region', region);
                            form.append('exact_location', data.data.results[0].formatted_address);

                            setLocationSession(form);

                            $('.location_country').text(country);
                            $('.location_full_address').text(data.data.results[0].formatted_address ||
                                'No address found');
                            $('.loader_position').addClass('d-none');
                            $('.location_secion').removeClass('d-none');
                        })
                    });
                google.maps.event.addListener(beachMarker, 'dragend',
                    function() {
                        $('.loader_position').removeClass('d-none');
                        $('.location_secion').addClass('d-none');

                        let lat = beachMarker.position.lat();
                        let lng = beachMarker.position.lng();
                        axios.post(
                            `https://maps.googleapis.com/maps/api/geocode/json?latlng=${lat},${lng}&key=${token}`
                        ).then((data) => {
                            if (data.data.error_message) {
                                toastr.error(data.data.error_message, 'Error!');
                                toastr.error('Your location is not set because of a wrong API key.', 'Error!');
                            }

                            const total = data.data.results.length;
                            let amount = '';
                            if (total > 1) {
                                amount = total - 2;
                            }
                            const result = data.data.results.slice(amount);
                            let country = '';
                            let region = '';
                            for (let index = 0; index < result.length; index++) {
                                const element = result[index];
                                if (element.types[0] == 'country') {
                                    country = element.formatted_address;
                                }
                                if (element.types[0] == 'administrative_area_level_1') {
                                    const str = element.formatted_address;
                                    const first = str.split(' ').shift()
                                    region = first;
                                }
                            }
                            var form = new FormData();
                            form.append('lat', lat);
                            form.append('lng', lng);
                            form.append('country', country);
                            form.append('region', region);
                            form.append('exact_location', data.data.results[0].formatted_address);

                            setLocationSession(form);

                            $('.location_country').text(country);
                            $('.location_full_address').text(data.data.results[0].formatted_address ||
                                'No address found');
                            $('.loader_position').addClass('d-none');
                            $('.location_secion').removeClass('d-none');
                        })
                    });
                // Search
                var input = document.getElementById('searchInput');
                map.controls[google.maps.ControlPosition.TOP_LEFT].push(input);

                let country_code = '{{ current_country_code() }}';
                if (country_code) {
                    var options = {
                        componentRestrictions: {
                            country: country_code
                        }
                    };
                    var autocomplete = new google.maps.places.Autocomplete(input, options);
                } else {
                    var autocomplete = new google.maps.places.Autocomplete(input);
                }

                autocomplete.bindTo('bounds', map);
                var infowindow = new google.maps.InfoWindow();
                var marker = new google.maps.Marker({
                    map: map,
                    anchorPoint: new google.maps.Point(0, -29)
                });
                autocomplete.addListener('place_changed', function() {
                    infowindow.close();
                    marker.setVisible(false);
                    var place = autocomplete.getPlace();
                    if (place.geometry.viewport) {
                        map.fitBounds(place.geometry.viewport);
                    } else {
                        map.setCenter(place.geometry.location);
                        map.setZoom(17);
                    }
                });
            }
            window.initMap = initMap;

            @php
                $link1 = 'https://maps.googleapis.com/maps/api/js?key=';
                $link2 = $setting->google_map_key;
                $Link3 = '&callback=initMap&libraries=places,geometry';
                $scr = $link1 . $link2 . $Link3;
            @endphp;
        </script>
        <script src="{{ $scr }}" async defer></script>
    @endif

    <script>
        $('#pills-setting-tab').on('click', function() {
            setTimeout(() => {
                leaflet_map.invalidateSize(true);
            }, 200);
        });

        // Konfigurasi CKEditor untuk menghapus kemampuan upload media
        document.addEventListener('DOMContentLoaded', function() {
            if (typeof CKEDITOR !== 'undefined') {
                // Konfigurasi untuk semua instance CKEditor
                CKEDITOR.on('instanceReady', function(ev) {
                    // Hapus tombol upload gambar dan media
                    ev.editor.config.removeButtons = 'Image,Flash,Table,HorizontalRule,Smiley,SpecialChar,PageBreak,Iframe';

                    // Hapus plugin upload
                    ev.editor.config.removePlugins = 'image,uploadimage,uploadfile';

                    // Batasi toolbar hanya untuk formatting teks
                    ev.editor.config.toolbar = [
                        { name: 'basicstyles', items: [ 'Bold', 'Italic', 'Underline', 'Strike', '-', 'RemoveFormat' ] },
                        { name: 'paragraph', items: [ 'NumberedList', 'BulletedList', '-', 'Outdent', 'Indent', '-', 'Blockquote' ] },
                        { name: 'links', items: [ 'Link', 'Unlink' ] },
                        { name: 'styles', items: [ 'Format', 'FontSize' ] },
                        { name: 'colors', items: [ 'TextColor', 'BGColor' ] },
                        { name: 'tools', items: [ 'Maximize' ] }
                    ];

                    // Refresh editor dengan konfigurasi baru
                    ev.editor.ui.space('contents').setStyle('height', '350px');
                    ev.editor.resize('100%', '350px');
                });
            }

            // Update hidden inputs dengan data dari session/livewire
            updateLocationHiddenInputs();
        });

        // Function untuk update hidden inputs dengan data lokasi
        function updateLocationHiddenInputs() {
            // Ambil data dari session yang sudah diset oleh PHP
            const sessionData = {
                country: '{{ session("selectedCountryId") }}',
                region: '{{ session("selectedStateId") }}',
                district: '{{ session("selectedCityId") }}',
                locality: '{{ session("selectedKecamatanId") }}',
                neighborhood: '{{ session("selectedKelurahanId") }}'
            };

            // Update hidden inputs
            document.getElementById('country').value = sessionData.country || '';
            document.getElementById('region').value = sessionData.region || '';
            document.getElementById('district').value = sessionData.district || '';
            document.getElementById('locality').value = sessionData.locality || '';
            document.getElementById('neighborhood').value = sessionData.neighborhood || '';
        }

        // Listen untuk perubahan dari Livewire component
        document.addEventListener('livewire:load', function () {
            Livewire.on('locationUpdated', function () {
                setTimeout(updateLocationHiddenInputs, 100);
            });
        });

        // Update hidden inputs sebelum form submit
        document.querySelector('form[action*="settingUpdateInformation"]').addEventListener('submit', function(e) {
            updateLocationHiddenInputs();
        });

        // Override setLocationSession function untuk menyimpan koordinat ke hidden input
        window.setLocationSession = function(form) {
            // Update hidden inputs dengan koordinat yang dipilih
            const lat = form.get('lat');
            const lng = form.get('lng');

            if (lat && lng) {
                document.getElementById('lat_input').value = lat;
                document.getElementById('long_input').value = lng;
            }

            // Panggil fungsi asli untuk menyimpan ke session
            axios.post('{{ route('website.set.session') }}', form)
                .then(function(response) {
                    console.log('Location saved to session');
                })
                .catch(function(error) {
                    console.error('Error saving location:', error);
                });
        };

        // Auto-add https:// to website field
        const websiteInput = document.getElementById('website_input_setting');
        if (websiteInput) {
            websiteInput.addEventListener('blur', function() {
                let value = this.value.trim();
                if (value && !value.startsWith('http://') && !value.startsWith('https://')) {
                    this.value = 'https://' + value;
                }
            });
        }

        // Format phone number
        const phoneInput = document.getElementById('phone_input_setting');
        if (phoneInput) {
            phoneInput.addEventListener('blur', function() {
                let value = this.value.trim();

                // Remove any non-digit characters except +
                value = value.replace(/[^\d+]/g, '');

                // Format phone number
                if (value.startsWith('08')) {
                    // Replace 08 with 628
                    value = '628' + value.substring(2);
                } else if (value.startsWith('02')) {
                    // Replace 02 with 622
                    value = '622' + value.substring(2);
                } else if (value.startsWith('0')) {
                    // Replace other 0 prefixes with 62
                    value = '62' + value.substring(1);
                } else if (!value.startsWith('62') && !value.startsWith('+62')) {
                    // If doesn't start with 62 or +62, assume it's local number starting with 8
                    if (value.startsWith('8')) {
                        value = '62' + value;
                    }
                }

                // Ensure it starts with +62
                if (value.startsWith('62') && !value.startsWith('+62')) {
                    value = '+' + value;
                }

                this.value = value;
            });
        }

        // Function to update placeholder based on selected platform
        function updatePlaceholder(selectElement) {
            const urlInput = selectElement.closest('.social-media-item').querySelector('.social-url-input');
            const platform = selectElement.value;

            if (platform === 'other') {
                urlInput.placeholder = 'Masukkan URL lengkap (https://...)';
            } else if (platform) {
                urlInput.placeholder = 'Masukkan username saja';
            } else {
                urlInput.placeholder = 'Masukkan username...';
            }
        }

        // Handle existing social media selects
        document.querySelectorAll('select[name="social_media[]"]').forEach(function(select) {
            updatePlaceholder(select);
            select.addEventListener('change', function() {
                updatePlaceholder(this);
            });
        });

        // Handle dynamically added social media fields
        document.addEventListener('change', 'select[name="social_media[]"]', function() {
            updatePlaceholder(this);
        });
    </script>
@endsection
