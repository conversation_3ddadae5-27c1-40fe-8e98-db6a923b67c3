@extends('frontend.layouts.app')

@section('description')
    @php
    $data = metaData('company-details');
    @endphp
    {{ $data->description }}
@endsection
@section('og:image')
    {{ asset($data->image) }}
@endsection
@section('title')
    Profil {{ formatCompanyName($user) }}
@endsection

@section('main')
    @php
        $lat = $user->company->lat;
        $long = $user->company->long;
    @endphp
    @if($user->userPlan && $user->userPlan->plan->profile_verify && !$user->company->is_profile_verified )
        <div class="text-center mt-2 text-red">
            <small class="text-xs">Akun Anda belum diaktivasi. <a href="{{route('company.verify.documents.index')}}"> Lihat dokumen Anda.</a>  </small>
        </div>
    @endif

    <!-- Modern Breadcrumb -->
    <div class="tw-bg-[#F1F2F4] tw-py-4">
        <div class="container">
            <div class="tw-flex tw-justify-between tw-items-center">
                <div class="tw-flex tw-items-center tw-gap-2">
                    <a href="{{ route('website.home') }}" class="tw-text-primary-500 hover:tw-text-[#0A65CC] tw-font-medium">{{ __('home') }}</a>
                    <span class="tw-text-gray-400">/</span>
                    <a href="{{ route('website.company') }}" class="tw-text-primary-500 hover:tw-text-[#0A65CC] tw-font-medium">{{ __('company') }}</a>
                    <span class="tw-text-gray-400">/</span>
                    <span class="tw-text-gray-600">{{ formatCompanyName($user) }}</span>
                </div>
            </div>
        </div>
    </div>

    <!-- Company Banner -->
    <div class="tw-bg-white tw-py-4">
        <div class="container">
            <!-- Banner Image -->
            <div class="job-details-header tw-mb-0 tw-p-0">
                <div id="company-banner" class="tw-relative tw-w-full tw-rounded-lg tw-overflow-hidden tw-cursor-pointer lightbox-trigger" data-src="{{ $user->company->banner_url }}" data-type="banner" data-title="{{ $companyDetails->organization ? ucfirst($companyDetails->organization->name) . ' ' : '' }}{{ $user->name }}">
                    <img src="{{ $user->company->banner_url }}" alt="Banner {{ $user->name }}"
                        class="tw-w-full tw-h-full tw-object-cover">

                    <!-- Overlay Gradient for Better Text Visibility -->
                    <div class="tw-absolute tw-inset-0 tw-bg-gradient-to-t tw-from-black/40 tw-to-transparent"></div>

                    <!-- Zoom Icon -->
                    <div class="tw-absolute tw-top-2 tw-right-2 tw-bg-white tw-bg-opacity-90 tw-rounded-full tw-p-2 tw-text-primary-500 tw-shadow-sm hover:tw-bg-primary-500 hover:tw-text-white tw-transition-colors">
                        <i class="fas fa-search-plus"></i>
                    </div>


                </div>
            </div>
        </div>
    </div>

    <!-- Company Header Section -->
    <div class="tw-bg-white">
        <div class="container tw-py-4">
            <div class="job-details-header tw-mb-4">
                <div class="tw-flex tw-flex-col sm:tw-flex-row tw-items-center tw-gap-3">
                    <!-- Company Logo -->
                    <div class="tw-flex-shrink-0">
                        <div class="tw-w-16 tw-h-16 md:tw-w-18 md:tw-h-18 tw-rounded-full tw-overflow-hidden tw-border-3 tw-border-white tw-bg-white tw-shadow-md tw-cursor-pointer lightbox-trigger" data-src="{{ $user->company->logo_url }}" data-type="logo" data-title="{{ $companyDetails->organization ? ucfirst($companyDetails->organization->name) . ' ' : '' }}{{ $user->name }}">
                            <img src="{{ $user->company->logo_url }}" alt="Logo {{ $user->name }}"
                                 class="tw-w-full tw-h-full tw-object-contain">
                        </div>
                    </div>

                    <!-- Company Info -->
                    <div class="tw-flex-grow tw-text-center md:tw-text-left">
                        <!-- Organization Type -->
                        @if($companyDetails->organization)
                            <p class="tw-text-gray-500 tw-text-sm tw-mb-1 sm:tw-text-center md:tw-text-left">
                                {{ ucfirst($companyDetails->organization->name) }}
                            </p>
                        @endif

                        <!-- Company Name with Verification Badge -->
                        <div class="tw-flex tw-flex-wrap tw-items-center tw-justify-center md:tw-justify-start tw-gap-1 tw-mb-1">
                            <h1 class="tw-text-xl tw-font-bold tw-text-gray-900">{{ $user->name }}</h1>
                            @if($companyDetails->is_profile_verified)
                                <div class="tw-text-green-600 tw-ml-1">
                                    <i class="fas fa-check-circle tw-text-lg"></i>
                                </div>
                            @endif
                        </div>

                        <!-- Industry -->
                        @if ($companyDetails->industry)
                            <p class="tw-text-gray-600 tw-mb-1 tw-text-center md:tw-text-left tw-flex tw-items-center tw-justify-center md:tw-justify-start">
                                <i class="fas fa-briefcase tw-mr-1 tw-text-gray-400"></i>
                                <span>{{ $companyDetails->industry->name }}</span>
                            </p>
                        @endif

                        <!-- Location -->
                        @if ($user->company->city || $user->company->district)
                            <p class="tw-text-gray-500 tw-mb-1 tw-text-center md:tw-text-left tw-flex tw-items-center tw-justify-center md:tw-justify-start">
                                <i class="fas fa-map-marker-alt tw-mr-1 tw-text-gray-400"></i>
                                <span>
                                    @if ($user->company->city)
                                        {{ $user->company->city }}
                                    @elseif ($user->company->district)
                                        {{ $user->company->district }}
                                    @endif
                                </span>
                            </p>
                        @endif
                    </div>

                    <!-- Open Positions Button -->
                    <div class="tw-flex-shrink-0 tw-mt-4 md:tw-mt-0">
                        <a href="#open_position"
                           class="open-jobs-btn tw-inline-flex tw-items-center tw-px-5 tw-py-2.5 tw-rounded-lg tw-font-medium tw-text-white tw-shadow-sm tw-relative tw-overflow-hidden {{ $open_jobs->isEmpty() ? 'tw-bg-gray-400' : 'tw-bg-primary-500' }} tw-transition-all {{ $open_jobs->isEmpty() ? 'tw-cursor-not-allowed' : 'hover:tw-shadow-md' }}">
                            <div class="tw-relative tw-z-10 tw-flex tw-items-center">
                                <i class="btn-hover-text fas fa-briefcase tw-mr-2"></i>
                                <span class="btn-hover-text">{{ $open_jobs->isEmpty() ? __('Loker Tidak Tersedia') : __('Lowongan Tersedia') }}</span>

                                <div class="tw-bg-white tw-text-primary-500 tw-bg-opacity-20 tw-rounded-full tw-px-2.5 tw-py-0.5 tw-text-xs tw-ml-2 tw-font-bold">
                                    {{ $open_jobs->total() }}
                                </div>

                                @if (!$open_jobs->isEmpty())
                                    <i class="btn-hover-text fas fa-arrow-right tw-ml-2 tw-text-sm tw-animate-pulse"></i>
                                @endif
                            </div>
                            @if (!$open_jobs->isEmpty())
                            <div class="btn-bg-animation"></div>
                            @endif
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>


    <!--Company Content Area-->
    <div class="tw-py-8">
        <div class="container">
            <div class="row">
                <div class="col-lg-7 tw-mb-8 lg:tw-mb-0">
                    <!-- Company Description -->
                    <div class="job-details-content">
                        <h3 class="tw-text-xl tw-font-semibold tw-mb-4">Tentang Perusahaan</h3>
                        <div class="tw-prose tw-max-w-none tw-text-gray-600">
                            {!! $user->company->bio !!}
                        </div>
                    </div>

                    <!-- Company Vision & Mission -->
                    @if ($user->company->vision)
                        <div class="job-details-content tw-mt-8">
                            <h3 class="tw-text-xl tw-font-semibold tw-mb-4">Visi dan Misi</h3>
                            <div class="tw-prose tw-max-w-none tw-text-gray-600">
                                {!! $user->company->vision !!}
                            </div>
                        </div>
                    @endif

                    <!-- Share Profile -->
                    <div class="job-details-content">
                        <h3 class="tw-text-base tw-font-medium tw-mb-4">{{ __('share_this_profile') }}</h3>
                        <div class="tw-flex tw-flex-wrap tw-gap-2">
                            <a href="{{ socialMediaShareLinks(url()->current(), 'facebook') }}"
                               class="tw-w-10 tw-h-10 tw-rounded-full tw-flex tw-items-center tw-justify-center tw-bg-white tw-border tw-border-[#3b5998] tw-text-[#3b5998] hover:tw-bg-[#3b5998] hover:tw-text-white tw-transition-all tw-duration-200">
                                <i class="fab fa-facebook-f"></i>
                            </a>
                            <a href="{{ socialMediaShareLinks(url()->current(), 'twitter') }}"
                               class="tw-w-10 tw-h-10 tw-rounded-full tw-flex tw-items-center tw-justify-center tw-bg-white tw-border tw-border-[#1da1f2] tw-text-[#1da1f2] hover:tw-bg-[#1da1f2] hover:tw-text-white tw-transition-all tw-duration-200">
                                <i class="fab fa-twitter"></i>
                            </a>
                            <a href="{{ socialMediaShareLinks(url()->current(), 'linkedin') }}"
                               class="tw-w-10 tw-h-10 tw-rounded-full tw-flex tw-items-center tw-justify-center tw-bg-white tw-border tw-border-[#0077b5] tw-text-[#0077b5] hover:tw-bg-[#0077b5] hover:tw-text-white tw-transition-all tw-duration-200">
                                <i class="fab fa-linkedin-in"></i>
                            </a>
                            <a href="{{ socialMediaShareLinks(url()->current(), 'pinterest') }}"
                               class="tw-w-10 tw-h-10 tw-rounded-full tw-flex tw-items-center tw-justify-center tw-bg-white tw-border tw-border-[#e60023] tw-text-[#e60023] hover:tw-bg-[#e60023] hover:tw-text-white tw-transition-all tw-duration-200">
                                <i class="fab fa-pinterest-p"></i>
                            </a>
                            <a href="mailto:?subject={{ urlencode($user->name) }}&body={{ urlencode('Lihat profil perusahaan ini: ' . url()->current()) }}"
                               class="tw-w-10 tw-h-10 tw-rounded-full tw-flex tw-items-center tw-justify-center tw-bg-white tw-border tw-border-[#6c757d] tw-text-[#6c757d] hover:tw-bg-[#6c757d] hover:tw-text-white tw-transition-all tw-duration-200">
                                <i class="fas fa-envelope"></i>
                            </a>
                        </div>
                    </div>
                </div>
                <div class="col-lg-5">
                    <!-- Company Information Sidebar -->
                    <div class="tw-space-y-6">
                        <!-- Company Stats -->
                        @if ($user->company->establishment_date || $companyDetails->organization || $companyDetails->team_size)
                            <div class="job-details-sidebar">
                                <h3 class="tw-text-lg tw-font-semibold tw-mb-4">{{ __('company_information') }}</h3>
                                <div class="tw-grid tw-grid-cols-1 sm:tw-grid-cols-2 tw-gap-4">
                                    @if ($user->company->establishment_date)
                                        <div class="tw-flex tw-items-start tw-gap-3">
                                            <div class="tw-w-10 tw-h-10 tw-rounded-full tw-bg-blue-100 tw-flex tw-items-center tw-justify-center tw-text-blue-600 tw-flex-shrink-0">
                                                <i class="fas fa-calendar-alt"></i>
                                            </div>
                                            <div>
                                                <h4 class="tw-text-sm tw-text-gray-500 tw-mb-1">{{ __('founded_in') }}</h4>
                                                <p class="tw-font-medium tw-text-gray-900">
                                                    {{ formatTanggalIndonesia($user->company->establishment_date) }}
                                                </p>
                                            </div>
                                        </div>
                                    @endif

                                    @if ($companyDetails->organization)
                                        <div class="tw-flex tw-items-start tw-gap-3">
                                            <div class="tw-w-10 tw-h-10 tw-rounded-full tw-bg-purple-100 tw-flex tw-items-center tw-justify-center tw-text-purple-600 tw-flex-shrink-0">
                                                <i class="fas fa-building"></i>
                                            </div>
                                            <div>
                                                <h4 class="tw-text-sm tw-text-gray-500 tw-mb-1">{{ __('organization_type') }}</h4>
                                                <p class="tw-font-medium tw-text-gray-900">
                                                    {{ $companyDetails->organization ? ucfirst($companyDetails->organization->name) : '' }}
                                                </p>
                                            </div>
                                        </div>
                                    @endif

                                    @if ($companyDetails->team_size)
                                        <div class="tw-flex tw-items-start tw-gap-3">
                                            <div class="tw-w-10 tw-h-10 tw-rounded-full tw-bg-green-100 tw-flex tw-items-center tw-justify-center tw-text-green-600 tw-flex-shrink-0">
                                                <i class="fas fa-users"></i>
                                            </div>
                                            <div>
                                                <h4 class="tw-text-sm tw-text-gray-500 tw-mb-1">{{ __('Jumlah Karyawan') }}</h4>
                                                <p class="tw-font-medium tw-text-gray-900">
                                                    {{ $companyDetails->team_size ? $companyDetails->team_size->name : '' }}
                                                </p>
                                            </div>
                                        </div>
                                    @endif
                                </div>
                            </div>
                        @endif

                        <!-- Contact Information -->
                        <div class="job-details-sidebar">
                            <h3 class="tw-text-lg tw-font-semibold tw-mb-4">{{ __('contact_information') }}</h3>

                            @auth
                                <!-- Show contact information for logged in users -->
                                <div class="tw-space-y-4">
                                    @if ($user->contactInfo && $user->contactInfo->phone)
                                        <div class="tw-flex tw-items-start tw-gap-3">
                                            <div class="tw-w-10 tw-h-10 tw-rounded-full tw-bg-blue-100 tw-flex tw-items-center tw-justify-center tw-text-blue-600 tw-flex-shrink-0">
                                                <i class="fas fa-phone-alt"></i>
                                            </div>
                                            <div>
                                                <h4 class="tw-text-sm tw-text-gray-500 tw-mb-1">{{ __('phone') }}</h4>
                                                <p class="tw-font-medium tw-text-gray-900">
                                                    <a href="tel:{{ $user->contactInfo->phone }}" class="hover:tw-text-primary-500">
                                                        {{ str_starts_with($user->contactInfo->phone, '62') && !str_starts_with($user->contactInfo->phone, '+62') ? '+' . $user->contactInfo->phone : $user->contactInfo->phone }}
                                                    </a>
                                                </p>
                                            </div>
                                        </div>
                                    @endif

                                    @if ($user->email)
                                        <div class="tw-flex tw-items-start tw-gap-3">
                                            <div class="tw-w-10 tw-h-10 tw-rounded-full tw-bg-red-100 tw-flex tw-items-center tw-justify-center tw-text-red-600 tw-flex-shrink-0">
                                                <i class="fas fa-envelope"></i>
                                            </div>
                                            <div>
                                                <h4 class="tw-text-sm tw-text-gray-500 tw-mb-1">{{ __('email') }}</h4>
                                                <p class="tw-font-medium tw-text-gray-900">
                                                    <a href="mailto:{{ $user->email }}" class="hover:tw-text-primary-500">
                                                        {{ $user->email }}
                                                    </a>
                                                </p>
                                            </div>
                                        </div>
                                    @endif

                                    @if ($user->company && $user->company->website)
                                        <div class="tw-flex tw-items-start tw-gap-3">
                                            <div class="tw-w-10 tw-h-10 tw-rounded-full tw-bg-indigo-100 tw-flex tw-items-center tw-justify-center tw-text-indigo-600 tw-flex-shrink-0">
                                                <i class="fas fa-globe"></i>
                                            </div>
                                            <div>
                                                <h4 class="tw-text-sm tw-text-gray-500 tw-mb-1">{{ __('website') }}</h4>
                                                <p class="tw-font-medium tw-text-gray-900">
                                                    <a href="{{ $user->company->website }}" target="_blank" class="hover:tw-text-primary-500">
                                                        {{ $user->company->website }}
                                                    </a>
                                                </p>
                                            </div>
                                        </div>
                                    @endif

                                    <!-- Social Media -->
                                    @if ($user->socialInfo && $user->socialInfo->count() > 0)
                                        <div class="tw-mt-6">
                                            <h4 class="tw-text-sm tw-text-gray-500 tw-mb-3">{{ __('Media Sosial Kami') }}</h4>
                                            <div class="tw-flex tw-flex-wrap tw-gap-3">
                                                @foreach ($user->socialInfo as $contact)
                                                    <a target="_blank" href="{{ $contact->url }}"
                                                    class="tw-w-10 tw-h-10 tw-rounded-full tw-flex tw-items-center tw-justify-center tw-bg-white tw-border-2 tw-border-gray-200 hover:tw-border-primary-500 tw-text-gray-600 hover:tw-text-primary-500 tw-transition-all tw-duration-200 tw-shadow-sm">
                                                        @switch($contact->social_media)
                                                            @case('facebook')
                                                                <i class="fab fa-facebook-f"></i>
                                                                @break
                                                            @case('twitter')
                                                                <i class="fab fa-twitter"></i>
                                                                @break
                                                            @case('instagram')
                                                                <i class="fab fa-instagram"></i>
                                                                @break
                                                            @case('youtube')
                                                                <i class="fab fa-youtube"></i>
                                                                @break
                                                            @case('linkedin')
                                                                <i class="fab fa-linkedin-in"></i>
                                                                @break
                                                            @case('pinterest')
                                                                <i class="fab fa-pinterest-p"></i>
                                                                @break
                                                            @case('reddit')
                                                                <i class="fab fa-reddit-alien"></i>
                                                                @break
                                                            @case('github')
                                                                <i class="fab fa-github"></i>
                                                                @break
                                                            @case('other')
                                                                <i class="fas fa-link"></i>
                                                                @break
                                                            @default
                                                                <i class="fas fa-link"></i>
                                                        @endswitch
                                                    </a>
                                                @endforeach
                                            </div>
                                        </div>
                                    @endif
                                </div>
                            @else
                                <!-- Show login message for guests -->
                                <div class="tw-bg-gray-50 tw-rounded-lg tw-p-6 tw-text-center">
                                    <div class="tw-flex tw-flex-col tw-items-center tw-justify-center tw-gap-3">
                                        <div class="tw-w-12 tw-h-12 tw-rounded-full tw-bg-gray-100 tw-flex tw-items-center tw-justify-center">
                                            <i class="fas fa-lock tw-text-gray-400 tw-text-xl"></i>
                                        </div>
                                        <div>
                                            <p class="tw-text-gray-600 tw-mb-2">Informasi kontak hanya tersedia untuk pengguna yang sudah login</p>
                                            <a href="{{ route('login') }}" class="tw-bg-primary-500 tw-text-white tw-px-4 tw-py-2 tw-rounded-md tw-inline-block hover:tw-bg-primary-600 tw-transition-colors">
                                                <i class="fas fa-sign-in-alt tw-mr-1"></i> Login untuk melihat
                                            </a>
                                        </div>
                                    </div>
                                </div>
                            @endauth
                        </div>

                        <!-- Location Map -->
                        <div class="job-details-sidebar tw-overflow-hidden">
                            <div class="tw-pb-4">
                                <h3 class="tw-text-lg tw-font-semibold tw-mb-2">Domisili Perusahaan</h3>
                                @php
                                    $locationParts = [];

                                    // Ambil data dari user (registrasi) sebagai prioritas
                                    if ($user->kelurahan) $locationParts[] = $user->kelurahan;
                                    if ($user->kecamatan) $locationParts[] = $user->kecamatan;
                                    if ($user->kabupaten_kota) $locationParts[] = $user->kabupaten_kota;
                                    if ($user->provinsi) $locationParts[] = $user->provinsi;

                                    // Jika tidak ada data dari user, gunakan data company
                                    if (empty($locationParts)) {
                                        if ($user->company->neighborhood) $locationParts[] = $user->company->neighborhood;
                                        if ($user->company->locality) $locationParts[] = $user->company->locality;
                                        if ($user->company->district) $locationParts[] = $user->company->district;
                                        if ($user->company->region) $locationParts[] = $user->company->region;
                                    }

                                    $locationText = !empty($locationParts) ? implode(', ', $locationParts) : 'Domisili Tidak Diketahui';
                                @endphp
                                <p class="tw-text-gray-600 tw-mb-4">
                                    <i class="fas fa-map-marker-alt tw-mr-2 tw-text-gray-400"></i> {{ $locationText }}
                                </p>
                            </div>
                            <div class="tw-h-[160px] tw-overflow-hidden tw-mx-4 tw-mb-4 tw-rounded-lg">
                                @php
                                    $map = $setting->default_map;
                                @endphp

                                @if ($map == 'google-map')
                                    <div class="map tw-rounded-lg" id="google-map"></div>
                                @else
                                    <div id="leaflet-map" class="tw-rounded-lg"></div>
                                @endif
                            </div>
                        </div>


                    </div>
                </div>
            </div>
        </div>
    </div>
    <!-- Open Positions Section -->
    <section class="tw-py-12 tw-bg-gray-50" id="open_position">
        <div class="container">
            <div class="job-details-header tw-mb-8">
                <h2 class="tw-text-2xl tw-font-bold tw-text-gray-900">{{ __('open_positions') }} <span class="tw-text-primary-500" id="job-count">({{ $open_jobs->total() }})</span></h2>
                <p class="tw-text-gray-600 tw-mt-2">Lowongan kerja yang tersedia di {{ $user->name }}</p>
            </div>

            <!-- Job List Container -->
            <div class="row" id="job-list-container">
                @include('frontend.pages.partials.company-jobs', ['jobs' => $open_jobs])
            </div>

            <!-- Loading Indicator -->
            <div id="jobs-loading" class="tw-hidden tw-flex tw-justify-center tw-items-center tw-py-8">
                <div class="tw-flex tw-flex-col tw-items-center">
                    <div class="loading-animation">
                        <div class="loading-briefcase">
                            <i class="fas fa-briefcase"></i>
                        </div>
                        <div class="loading-dots">
                            <span></span>
                            <span></span>
                            <span></span>
                        </div>
                    </div>
                    <span class="tw-text-gray-600 tw-mt-3">Memuat lowongan kerja...</span>
                </div>
            </div>

            <!-- Pagination -->
            <div class="tw-mt-8 tw-flex tw-justify-center" id="pagination-container">
                {{ $open_jobs->links('vendor.pagination.frontend') }}
            </div>
        </div>
    </section>
@endsection

@section('frontend_links')
    <!-- >=>Leaflet Map<=< -->
    <x-map.leaflet.map_links />
    @include('map::links')

    <style>
        /* Modern Job Details Styling */
        .job-details-header {
            background-color: #fff;
            border-radius: 12px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.05);
            padding: 24px;
            margin-bottom: 24px;
        }

        .job-details-content {
            background-color: #fff;
            border-radius: 12px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.05);
            padding: 24px;
            margin-bottom: 24px;
        }

        .job-details-sidebar {
            background-color: #fff;
            border-radius: 12px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.05);
            padding: 24px;
            margin-bottom: 24px;
            position: sticky;
            top: 20px;
        }

        /* Fix for banner height issues */
        #company-banner {
            height: 140px !important; /* Default height for all devices */
        }

        @media (max-width: 576px) {
            #company-banner {
                height: 100px !important; /* Height for mobile */
            }
            .job-details-header {
                padding: 16px;
            }
            .job-details-content,
            .job-details-sidebar {
                padding: 16px;
            }
        }
        /* Map styling */
        #leaflet-map, #google-map {
            border-radius: 0 0 12px 12px;
            height: 160px !important;
            width: 100%;
            max-height: 160px !important;
            overflow: hidden;
        }

        /* Fix for Leaflet map container */
        .leaflet-container {
            height: 160px !important;
            max-height: 160px !important;
        }

        /* Fix for Leaflet layers */
        .leaflet-pane {
            max-height: 160px !important;
        }

        /* Styling for map popups */
        .company-popup .leaflet-popup-content-wrapper {
            border-radius: 8px;
            padding: 0;
        }

        .company-popup .leaflet-popup-content {
            margin: 0;
            min-width: 150px;
        }

        /* Prose styling for company description */
        .tw-prose {
            color: #374151;
            max-width: 65ch;
            font-size: 1rem;
            line-height: 1.75;
        }

        .tw-prose p {
            margin-top: 1.25em;
            margin-bottom: 1.25em;
        }

        .tw-prose ul {
            margin-top: 1.25em;
            margin-bottom: 1.25em;
            padding-left: 1.625em;
        }

        .tw-prose li {
            margin-top: 0.5em;
            margin-bottom: 0.5em;
            padding-left: 0.375em;
        }

        .tw-prose strong {
            font-weight: 600;
            color: #111827;
        }

        .tw-max-w-none {
            max-width: none;
        }

        /* Card hover effects */
        .tw-transition-all {
            transition: all 0.3s ease;
        }

        /* Lightbox styles */
        .lightbox {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.9);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 9999;
            opacity: 0;
            visibility: hidden;
            transition: opacity 0.3s ease, visibility 0.3s ease;
        }

        .lightbox.active {
            opacity: 1;
            visibility: visible;
        }

        .lightbox-content {
            position: relative;
            max-width: 90%;
            max-height: 90%;
            transform: scale(0.9);
            transition: transform 0.3s ease;
        }

        .lightbox.active .lightbox-content {
            transform: scale(1);
        }

        .lightbox-image {
            max-width: 100%;
            max-height: 90vh;
            display: block;
            border-radius: 4px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.5);
        }

        .lightbox-close {
            position: absolute;
            top: -40px;
            right: 0;
            width: 30px;
            height: 30px;
            background-color: white;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            color: #333;
            font-size: 18px;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
        }

        .lightbox-title {
            position: absolute;
            bottom: -40px;
            left: 0;
            right: 0;
            text-align: center;
            color: white;
            font-size: 16px;
            font-weight: 500;
            text-shadow: 0 1px 3px rgba(0, 0, 0, 0.5);
        }

        .lightbox-trigger {
            cursor: pointer;
        }

        /* Responsive styles */
        @media (max-width: 767px) {
            .tw-h-48 {
                height: 12rem;
            }

            .lightbox-content {
                max-width: 95%;
            }
        }

        /* Open Jobs Button Animation */
        .open-jobs-btn {
            transition: all 0.3s ease;
        }

        .open-jobs-btn:hover {
            transform: translateY(-2px);
        }

        /* Hover text color change */
        .btn-hover-text {
            transition: color 0.3s ease;
        }

        .open-jobs-btn:hover .btn-hover-text {
            color: #FFD700; /* Warna kuning */
        }

        .btn-bg-animation {
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(
                90deg,
                rgba(255, 255, 255, 0) 0%,
                rgba(255, 255, 255, 0.2) 50%,
                rgba(255, 255, 255, 0) 100%
            );
            animation: btn-shine 3s infinite;
            z-index: 1;
        }

        @keyframes btn-shine {
            0% {
                left: -100%;
            }
            20% {
                left: 100%;
            }
            100% {
                left: 100%;
            }
        }

        /* Custom Loading Animation */
        .loading-animation {
            position: relative;
            width: 80px;
            height: 80px;
            display: flex;
            justify-content: center;
            align-items: center;
        }

        .loading-briefcase {
            width: 40px;
            height: 40px;
            background-color: #138C79;
            border-radius: 8px;
            display: flex;
            justify-content: center;
            align-items: center;
            color: white;
            font-size: 20px;
            animation: briefcase-bounce 1.5s infinite;
            box-shadow: 0 4px 10px rgba(19, 140, 121, 0.3);
        }

        .loading-dots {
            position: absolute;
            bottom: 10px;
            display: flex;
            justify-content: center;
            gap: 6px;
        }

        .loading-dots span {
            width: 8px;
            height: 8px;
            background-color: #138C79;
            border-radius: 50%;
            opacity: 0;
        }

        .loading-dots span:nth-child(1) {
            animation: dot-fade 1.5s infinite 0s;
        }

        .loading-dots span:nth-child(2) {
            animation: dot-fade 1.5s infinite 0.3s;
        }

        .loading-dots span:nth-child(3) {
            animation: dot-fade 1.5s infinite 0.6s;
        }

        @keyframes briefcase-bounce {
            0%, 20%, 50%, 80%, 100% {
                transform: translateY(0);
            }
            40% {
                transform: translateY(-15px);
            }
            60% {
                transform: translateY(-7px);
            }
        }

        @keyframes dot-fade {
            0%, 100% {
                opacity: 0;
            }
            50% {
                opacity: 1;
            }
        }
    </style>
@endsection

<!-- Lightbox Container -->
<div id="lightbox" class="lightbox">
    <div class="lightbox-content">
        <div class="lightbox-close">
            <i class="fas fa-times"></i>
        </div>
        <img id="lightbox-image" class="lightbox-image" src="" alt="Lightbox Image">
        <div id="lightbox-title" class="lightbox-title"></div>
    </div>
</div>

@section('script')
<script>
    $(document).ready(function() {
        // Set initial page size to 3
        const pageSize = 3;

        // Handle pagination clicks
        $(document).on('click', '#pagination-container .page-link', function(e) {
            e.preventDefault();

            const url = $(this).attr('href');
            if (url) {
                loadJobs(url);

                // Update URL without refreshing the page
                const newUrl = updateURLParameter(window.location.href, 'page', getParameterByName('page', url));
                window.history.pushState({ path: newUrl }, '', newUrl);
            }

            return false;
        });

        // Function to load jobs via AJAX
        function loadJobs(url) {
            // Show loading indicator
            $('#jobs-loading').removeClass('tw-hidden');

            // Add per_page parameter if not present
            url = updateURLParameter(url, 'per_page', pageSize);

            $.ajax({
                url: url,
                type: 'GET',
                headers: {
                    'X-Requested-With': 'XMLHttpRequest'
                },
                success: function(response) {
                    // Update job list
                    $('#job-list-container').html(response.html);

                    // Update pagination
                    $('#pagination-container').html(response.pagination);

                    // Update job count
                    $('#job-count').text('(' + response.total + ')');

                    // Hide loading indicator
                    $('#jobs-loading').addClass('tw-hidden');

                    // Scroll to job list
                    $('#open_position').get(0).scrollIntoView({ behavior: 'smooth', block: 'start' });
                },
                error: function(xhr) {
                    console.error('Error loading jobs:', xhr);
                    $('#jobs-loading').addClass('tw-hidden');
                }
            });
        }

        // Helper function to get URL parameter
        function getParameterByName(name, url) {
            if (!url) url = window.location.href;
            name = name.replace(/[\[\]]/g, '\\$&');
            const regex = new RegExp('[?&]' + name + '(=([^&#]*)|&|#|$)'),
                results = regex.exec(url);
            if (!results) return null;
            if (!results[2]) return '';
            return decodeURIComponent(results[2].replace(/\+/g, ' '));
        }

        // Helper function to update URL parameter
        function updateURLParameter(url, param, paramVal) {
            let newAdditionalURL = "";
            let tempArray = url.split("?");
            let baseURL = tempArray[0];
            let additionalURL = tempArray[1];
            let temp = "";

            if (additionalURL) {
                tempArray = additionalURL.split("&");
                for (let i = 0; i < tempArray.length; i++) {
                    if (tempArray[i].split('=')[0] != param) {
                        newAdditionalURL += temp + tempArray[i];
                        temp = "&";
                    }
                }
            }

            let rows_txt = temp + "" + param + "=" + paramVal;
            return baseURL + "?" + newAdditionalURL + rows_txt;
        }

        // Check if the URL contains a pagination query parameter (e.g., ?page=2)
        const queryParams = new URLSearchParams(window.location.search);
        const page = queryParams.get('page');

        // If a pagination query parameter is present, scroll to the pagination container
        if (page) {
            const paginationContainer = document.getElementById('pagination-container');
            if (paginationContainer) {
                paginationContainer.scrollIntoView({ behavior: 'smooth' });
            }
        }

        // Lightbox Functionality
        const lightbox = document.getElementById('lightbox');
        const lightboxImage = document.getElementById('lightbox-image');
        const lightboxTitle = document.getElementById('lightbox-title');
        const lightboxClose = document.querySelector('.lightbox-close');
        const lightboxTriggers = document.querySelectorAll('.lightbox-trigger');

        // Open lightbox when trigger is clicked
        lightboxTriggers.forEach(trigger => {
            trigger.addEventListener('click', function() {
                const imgSrc = this.getAttribute('data-src');
                const imgTitle = this.getAttribute('data-title');
                const imgType = this.getAttribute('data-type');

                lightboxImage.src = imgSrc;

                if (imgType === 'logo') {
                    lightboxTitle.textContent = `Logo ${imgTitle}`;
                    lightboxImage.style.objectFit = 'contain';
                    lightboxImage.style.backgroundColor = 'white';
                    lightboxImage.style.padding = '20px';
                } else {
                    lightboxTitle.textContent = imgTitle;
                    lightboxImage.style.objectFit = 'cover';
                    lightboxImage.style.backgroundColor = 'transparent';
                    lightboxImage.style.padding = '0';
                }

                lightbox.classList.add('active');
                document.body.style.overflow = 'hidden'; // Prevent scrolling
            });
        });

        // Close lightbox when close button is clicked
        lightboxClose.addEventListener('click', function() {
            lightbox.classList.remove('active');
            document.body.style.overflow = ''; // Re-enable scrolling
        });

        // Close lightbox when clicking outside the image
        lightbox.addEventListener('click', function(e) {
            if (e.target === lightbox) {
                lightbox.classList.remove('active');
                document.body.style.overflow = ''; // Re-enable scrolling
            }
        });

        // Close lightbox with Escape key
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape' && lightbox.classList.contains('active')) {
                lightbox.classList.remove('active');
                document.body.style.overflow = ''; // Re-enable scrolling
            }
        });
    });

    // Toggle for "show more" button
    $(document).on('click', '#show-more', function() {
        var value = $(this).attr('aria-expanded');
        if (value == 'true') {
            $('#show-more').html('Sembunyikan informasi');
        } else {
            $('#show-more').html('Tampilkan informasi kontak');
        }
    });
</script>

<!-- Leaflet  -->
<x-map.leaflet.map_scripts />
<script>
    document.addEventListener('DOMContentLoaded', function() {
        var oldlat = {!! $lat ? $lat : $setting->default_lat !!};
        var oldlng = {!! $long ? $long : $setting->default_long !!};

        // Map preview
        var element = document.getElementById('leaflet-map');

        if (element) {
            // Set explicit height
            element.style.height = '200px';

            // Create Leaflet map on map element with fixed height
            var leaflet_map = L.map(element, {
                attributionControl: false,
                zoomControl: true,
                scrollWheelZoom: false
            });

            // Add OSM tile layer to the Leaflet map.
            L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
                attribution: '&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
            }).addTo(leaflet_map);

            // Target's GPS coordinates.
            var target = L.latLng(oldlat, oldlng);

            // Set map's center to target with zoom 13.
            const zoom = 13;
            leaflet_map.setView(target, zoom);

            // Place a marker on the same location.
            var marker = L.marker(target, {
                title: "{{ $user->name }}"
            }).addTo(leaflet_map);

            // Add popup to marker with company logo, name and location
            var popupContent = `
                <div class="tw-text-center tw-p-1">
                    <div class="tw-flex tw-justify-center tw-mb-2">
                        <img src="{{ $user->company->logo_url }}" alt="Logo {{ $user->name }}" class="tw-w-12 tw-h-12 tw-object-contain tw-rounded-full tw-border tw-border-gray-200">
                    </div>
                    <div class="tw-font-medium tw-text-gray-900 tw-mb-1">
                        @if($companyDetails->organization)
                            <span class="tw-text-xs tw-text-gray-500">{{ $companyDetails->organization->name }}</span>
                        @endif
                        <div class="tw-text-sm">{{ $user->name }}</div>
                    </div>
                    <div class="tw-text-xs tw-text-gray-500">
                        {{ $user->company->locality ? $user->company->locality.', ' : '' }}{{ $user->company->district }}
                    </div>
                </div>
            `;

            marker.bindPopup(popupContent, {
                maxWidth: 200,
                minWidth: 150,
                className: 'company-popup'
            }).openPopup();

            // Force map to invalidate size after rendering
            setTimeout(function() {
                leaflet_map.invalidateSize();
            }, 100);
        }
    });
</script>

<!-- ================ google map ============== -->
@if ($map == 'google-map')
<script>
    function initMap() {
        var token = "{{ $setting->google_map_key }}";
        var oldlat = {!! $lat ? $lat : $setting->default_lat !!};
        var oldlng = {!! $long ? $long : $setting->default_long !!};
        var companyName = "{{ $user->name }}";
        var companyAddress = "{{ $user->company->address }}";

        // Set explicit height for map container
        var mapElement = document.getElementById("google-map");
        mapElement.style.height = '200px';

        // Create map
        const map = new google.maps.Map(mapElement, {
            zoom: 13,
            center: {
                lat: oldlat,
                lng: oldlng
            },
            mapTypeControl: false,
            streetViewControl: false,
            fullscreenControl: false,
            zoomControl: true,
            scrollwheel: false,
            styles: [
                {
                    featureType: "poi",
                    elementType: "labels",
                    stylers: [{ visibility: "off" }]
                }
            ]
        });

        // Create marker
        const marker = new google.maps.Marker({
            draggable: false,
            position: {
                lat: oldlat,
                lng: oldlng
            },
            map: map,
            animation: google.maps.Animation.DROP,
            title: companyName
        });

        // Create info window with company logo, name and location
        const infowindow = new google.maps.InfoWindow({
            content: `
                <div style="text-align: center; padding: 8px;">
                    <div style="display: flex; justify-content: center; margin-bottom: 8px;">
                        <img src="{{ $user->company->logo_url }}" alt="Logo ${companyName}"
                             style="width: 48px; height: 48px; object-fit: contain; border-radius: 50%; border: 1px solid #e5e7eb;">
                    </div>
                    <div style="font-weight: 500; color: #111827; margin-bottom: 4px;">
                        @if($companyDetails->organization)
                            <div style="font-size: 12px; color: #6b7280;">{{ $companyDetails->organization->name }}</div>
                        @endif
                        <div style="font-size: 14px;">${companyName}</div>
                    </div>
                    <div style="font-size: 12px; color: #6b7280;">
                        {{ $user->company->locality ? $user->company->locality.', ' : '' }}{{ $user->company->district }}
                    </div>
                </div>
            `,
            maxWidth: 200
        });

        // Open info window on marker click
        marker.addListener("click", () => {
            infowindow.open(map, marker);
        });

        // Open info window by default
        infowindow.open(map, marker);

        // Force resize after rendering
        google.maps.event.addListenerOnce(map, 'idle', function() {
            google.maps.event.trigger(map, 'resize');
        });
    }
    window.initMap = initMap;
</script>
<script>
    @php
        $link1 = 'https://maps.googleapis.com/maps/api/js?key=';
        $link2 = $setting->google_map_key;
        $Link3 = '&callback=initMap&libraries=places,geometry';
        $scr = $link1 . $link2 . $Link3;
    @endphp;
</script>
<script src="{{ $scr }}" async defer></script>
@endif
<!-- ================ google map ============== -->
@endsection
