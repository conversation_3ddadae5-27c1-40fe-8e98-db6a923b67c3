@extends('frontend.layouts.app')

@section('description')
    @php
        $data = metaData('company');
    @endphp
    {{ $data->description }}
@endsection
@section('og:image')
    {{ asset($data->image) }}
@endsection
@section('title')
    {{ $data->title }}
@endsection

@section('main')
    <div class="breadcrumbs style-two">
        <div class="container">
            <div class="row align-items-center ">
                <div class="col-12 position-relative ">
                    <div class="breadcrumb-menu">
                        <h6 class="f-size-18 m-0">Daftar Perusahaan</h6>
                        <ul>
                            <li><a href="{{ route('website.home') }}">Beranda</a></li>
                            <li>/</li>
                            <li>Perusahaan</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="container mt-5">
        <div class="row">
            <!-- Left Sidebar - Filters (Desktop) -->
            <div class="col-lg-3 d-none d-lg-block">
                <div class="card tw-sticky" id="desktop-filter-card">
                    <div class="card-header tw-bg-white">
                        <h5 class="tw-mb-0">Filter Perusahaan</h5>
                    </div>
                    <div class="card-body">
                        <form id="company-filter-form" action="{{ route('website.company') }}" method="GET">

                            <!-- Keyword Search -->
                            <div class="form-group mb-4">
                                <label for="keyword" class="tw-font-medium tw-mb-2 tw-block">Kata Kunci</label>
                                <input type="text" name="keyword" id="keyword" class="form-control" placeholder="Cari nama perusahaan" value="{{ request('keyword') }}">
                            </div>

                            <!-- Organization Type (Badan Hukum) -->
                            <div class="form-group mb-4">
                                <label for="organization_types" class="tw-font-medium tw-mb-2 tw-block">Badan Hukum</label>
                                <select name="organization_type" id="organization_types" class="form-control select2-organization">
                                    @foreach($organization_types as $type)
                                        <option value="{{ $type->id }}"
                                            {{ request('organization_type') == $type->id ? 'selected' : '' }}>
                                            {{ $type->name }}
                                        </option>
                                    @endforeach
                                </select>
                            </div>

                            <!-- Industry Type (Select2) -->
                            <div class="form-group mb-4">
                                <label for="industry_types" class="tw-font-medium tw-mb-2 tw-block">Industri</label>
                                <select name="industry_type[]" id="industry_types" multiple class="form-control select2-industry">
                                    @php
                                        // Get top 5 industries with most companies
                                        $topIndustries = $industries->take(5);
                                        $otherIndustries = $industries->slice(5);
                                    @endphp

                                    <optgroup label="Industri Teratas">
                                        @foreach($topIndustries as $industry)
                                            <option value="{{ $industry->id }}"
                                                {{ in_array($industry->id, (array)request('industry_type', [])) ? 'selected' : '' }}>
                                                {{ $industry->name }}
                                            </option>
                                        @endforeach
                                    </optgroup>

                                    <optgroup label="Industri Lainnya">
                                        @foreach($otherIndustries as $industry)
                                            <option value="{{ $industry->id }}"
                                                {{ in_array($industry->id, (array)request('industry_type', [])) ? 'selected' : '' }}>
                                                {{ $industry->name }}
                                            </option>
                                        @endforeach
                                    </optgroup>
                                </select>
                            </div>

                            <!-- Lokasi Perusahaan -->
                            <div class="form-group mb-4">
                                <label for="location" class="tw-font-medium tw-mb-2 tw-block">Lokasi Perusahaan</label>
                                <select name="lokasi" id="location" class="form-control select2-location">
                                    <option value="">Pilih Lokasi</option>
                                    @php
                                        $locations = \App\Models\Company::select('district')->whereNotNull('district')->distinct()->get()->pluck('district');
                                    @endphp
                                    @foreach($locations as $location)
                                        <option value="{{ $location }}" {{ request('lokasi') == $location ? 'selected' : '' }}>
                                            {{ $location }}
                                        </option>
                                    @endforeach
                                </select>
                            </div>

                            <!-- Filter Buttons -->
                            <div class="tw-flex tw-gap-2 tw-mt-4">
                                <button type="submit" class="btn btn-primary tw-w-full">
                                    Terapkan
                                </button>
                                <button type="button" id="reset-filter" class="btn btn-outline-secondary tw-w-full">
                                    Reset
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>

            <!-- Mobile Filter Button -->
            <button type="button" class="mobile-filter-btn d-lg-none" id="mobile-filter-button">
                <i class="ph-funnel"></i>
            </button>

            <!-- Filter Overlay -->
            <div id="filter-overlay" class="filter-overlay"></div>

            <!-- Mobile Filter Sidebar -->
            <div id="mobile-filter-sidebar" class="filter-sidebar">
                <div class="tw-absolute tw-left-0 tw-top-0 tw-bottom-0 tw-h-screen tw-w-4/5 tw-max-w-sm tw-bg-white tw-shadow-lg" id="mobile-filter-content">
                    <div class="tw-flex tw-justify-between tw-items-center tw-p-4 tw-border-b">
                        <h5 class="tw-mb-0">Filter Perusahaan</h5>
                        <button type="button" id="close-mobile-filter" class="tw-bg-transparent tw-border-0 tw-text-gray-500 tw-text-xl">
                            <i class="ph-x-circle"></i>
                        </button>
                    </div>
                    <div class="tw-p-4 tw-overflow-y-auto" style="max-height: calc(100vh - 60px);">
                        <form id="mobile-company-filter-form" action="{{ route('website.company') }}" method="GET">

                            <!-- Keyword Search -->
                            <div class="form-group mb-4">
                                <label for="mobile-keyword" class="tw-font-medium tw-mb-2 tw-block">Kata Kunci</label>
                                <input type="text" name="keyword" id="mobile-keyword" class="form-control" placeholder="Cari nama perusahaan" value="{{ request('keyword') }}">
                            </div>

                            <!-- Organization Type (Badan Hukum) -->
                            <div class="form-group mb-4">
                                <label for="mobile_organization_types" class="tw-font-medium tw-mb-2 tw-block">Badan Hukum</label>
                                <select name="organization_type" id="mobile_organization_types" class="form-control select2-organization-mobile">
                                    <option value="">Pilih Badan Hukum</option>
                                    @foreach($organization_types as $type)
                                        <option value="{{ $type->id }}"
                                            {{ request('organization_type') == $type->id ? 'selected' : '' }}>
                                            {{ \Illuminate\Support\Str::limit($type->name, 25) }}
                                        </option>
                                    @endforeach
                                </select>
                            </div>

                            <!-- Industry Type (Select2) -->
                            <div class="form-group mb-4">
                                <label for="mobile_industry_types" class="tw-font-medium tw-mb-2 tw-block">Industri</label>
                                <select name="industry_type" id="mobile_industry_types" class="form-control select2-industry-mobile">
                                    <option value="">Pilih Industri</option>
                                    @php
                                        // Get top 5 industries with most companies
                                        $topIndustries = $industries->take(5);
                                        $otherIndustries = $industries->slice(5);
                                    @endphp

                                    <optgroup label="Industri Teratas">
                                        @foreach($topIndustries as $industry)
                                            <option value="{{ $industry->id }}"
                                                {{ request('industry_type') == $industry->id ? 'selected' : '' }}>
                                                {{ \Illuminate\Support\Str::limit($industry->name, 25) }}
                                            </option>
                                        @endforeach
                                    </optgroup>

                                    <optgroup label="Industri Lainnya">
                                        @foreach($otherIndustries as $industry)
                                            <option value="{{ $industry->id }}"
                                                {{ request('industry_type') == $industry->id ? 'selected' : '' }}>
                                                {{ \Illuminate\Support\Str::limit($industry->name, 25) }}
                                            </option>
                                        @endforeach
                                    </optgroup>
                                </select>
                            </div>

                            <!-- Lokasi Perusahaan -->
                            <div class="form-group mb-4">
                                <label for="mobile_location" class="tw-font-medium tw-mb-2 tw-block">Lokasi Perusahaan</label>
                                <select name="lokasi" id="mobile_location" class="form-control select2-location-mobile">
                                    <option value="">Pilih Lokasi</option>
                                    @php
                                        $locations = \App\Models\Company::select('district')->whereNotNull('district')->distinct()->get()->pluck('district');
                                    @endphp
                                    @foreach($locations as $location)
                                        <option value="{{ $location }}" {{ request('lokasi') == $location ? 'selected' : '' }}>
                                            {{ \Illuminate\Support\Str::limit($location, 25) }}
                                        </option>
                                    @endforeach
                                </select>
                            </div>

                            <!-- Filter Buttons -->
                            <div class="tw-flex tw-gap-2 tw-mt-4">
                                <button type="submit" class="btn btn-primary tw-w-full">
                                    Terapkan Filter
                                </button>
                                <button type="button" id="mobile-reset-filter" class="btn btn-outline-secondary tw-w-full">
                                    Reset
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>

            <!-- Right Content - Company List -->
            <div class="col-lg-9">
                <!-- Active Filters Display -->
                @if (request('keyword') || request('organization_type') || request('industry_type') || request('lokasi'))
                    <div class="card mb-4">
                        <div class="card-body">
                            <div class="tw-flex tw-flex-wrap tw-gap-2 tw-items-center">
                                <h6 class="tw-mb-0 tw-mr-2">Filter Aktif:</h6>

                                @if (request('keyword'))
                                    <div class="tw-bg-gray-100 tw-px-3 tw-py-1 tw-rounded-full tw-flex tw-items-center tw-gap-2 active-filter-item">
                                        <span>Kata Kunci: {{ request('keyword') }}</span>
                                        <button type="button" onclick="removeFilter('keyword')" class="tw-text-gray-500 hover:tw-text-red-500 tw-bg-transparent tw-border-0">
                                            <i class="ph-x-circle"></i>
                                        </button>
                                    </div>
                                @endif

                                @if (request('organization_type'))
                                    @php
                                        $orgId = request('organization_type');
                                        $orgType = $organization_types->where('id', $orgId)->first();
                                        $orgName = $orgType ? $orgType->name : '';
                                    @endphp
                                    @if($orgName)
                                        <div class="tw-bg-gray-100 tw-px-3 tw-py-1 tw-rounded-full tw-flex tw-items-center tw-gap-2 active-filter-item">
                                            <span>Badan Hukum: {{ $orgName }}</span>
                                            <button type="button" onclick="removeOrganization('{{ $orgId }}')" class="tw-text-gray-500 hover:tw-text-red-500 tw-bg-transparent tw-border-0">
                                                <i class="ph-x-circle"></i>
                                            </button>
                                        </div>
                                    @endif
                                @endif

                                @if (request('industry_type'))
                                    @foreach((array)request('industry_type') as $indId)
                                        @php
                                            $indName = $industries->where('id', $indId)->first()->name ?? '';
                                        @endphp
                                        @if($indName)
                                            <div class="tw-bg-gray-100 tw-px-3 tw-py-1 tw-rounded-full tw-flex tw-items-center tw-gap-2 active-filter-item">
                                                <span>Industri: {{ $indName }}</span>
                                                <button type="button" onclick="removeIndustry('{{ $indId }}')" class="tw-text-gray-500 hover:tw-text-red-500 tw-bg-transparent tw-border-0">
                                                    <i class="ph-x-circle"></i>
                                                </button>
                                            </div>
                                        @endif
                                    @endforeach
                                @endif

                                @if (request('lokasi'))
                                    <div class="tw-bg-gray-100 tw-px-3 tw-py-1 tw-rounded-full tw-flex tw-items-center tw-gap-2 active-filter-item">
                                        <span>Lokasi: {{ request('lokasi') }}</span>
                                        <button type="button" onclick="removeLocation()" class="tw-text-gray-500 hover:tw-text-red-500 tw-bg-transparent tw-border-0">
                                            <i class="ph-x-circle"></i>
                                        </button>
                                    </div>
                                @endif


                            </div>
                        </div>
                    </div>
                @endif

                <!-- Company Count -->
                <div class="tw-flex tw-justify-between tw-items-center tw-mb-4">
                    <h5 class="tw-mb-0">Daftar Perusahaan (<span id="total-companies">{{ $companies->total() }}</span>)</h5>
                </div>

                <!-- Company List -->
                <div id="companies-container" class="row">
                    @if ($companies->count() > 0)
                        @foreach ($companies as $company)
                            <div class="col-md-6 col-xl-4 fade-in-bottom condition_class rt-mb-24 tw-self-stretch">
                                <a href="{{ route('website.employe.details', $company->user->username) }}"
                                    class="card jobcardStyle1 tw-relative tw-h-full">
                                    <div class="tw-p-6 !tw-pb-[72px]">
                                        <div class="rt-single-icon-box tw-gap-3">
                                            <div class="tw-w-14 tw-h-14 tw-rounded-full tw-overflow-hidden">
                                                <img class="tw-w-full tw-h-full tw-object-cover"
                                                    src="{{ $company->logo_url ? $company->logo_url : asset('backend/image/default.png') }}"
                                                    alt="Logo {{ $company->user->name }}" draggable="false">
                                            </div>
                                            <div class="iconbox-content">
                                                <div class="">
                                                    <span class="tw-text-[#191F33] tw-text-lg tw-font-medium tw-inline-block">
                                                        @if($company->organization)
                                                            <span class="tw-text-gray-500 tw-text-sm">{{ ucfirst($company->organization->prefix ?? $company->organization->name) }}</span>
                                                        @endif
                                                        {{ $company->user->name }}
                                                    </span>
                                                </div>
                                                @isset($company->country)
                                                    <span class="loacton text-gray-400 ">
                                                        <i class="ph-map-pin"></i>
                                                        {{ $company->district }}
                                                    </span>
                                                @endisset
                                            </div>
                                        </div>
                                        <div class="post-info">
                                            <div class="tw-flex tw-flex-wrap tw-gap-3">
                                                <span
                                                    class="tw-px-3 tw-py-1 tw-inline-block tw-text-sm tw-font-medium tw-text-[#474C54] tw-rounded-[52px] ll-gray-border">{{ $company?->industry?->name ?? '' }}</span>

                                                @if ($company->activejobs !== 0)
                                                    <span
                                                        class="tw-px-3 tw-py-1 tw-inline-block tw-text-sm tw-font-medium tw-text-[#474C54] tw-rounded-[52px] ll-gray-border">{{ $company->activejobs }}
                                                        {{ __('open_job') }}</span>
                                                @endif

                                            </div>
                                            <div
                                                class="tw-absolute tw-bottom-6 tw-left-6 tw-text-base tw-font-semibold tw-capitalize tw-inline-flex tw-items-center tw-gap-1">
                                                <span>{{ __('view_profile') }}</span>
                                                <i class="ph-bold ph-arrow-right"></i>
                                            </div>
                                        </div>
                                    </div>
                                </a>
                            </div>
                        @endforeach
                    @else
                        <div class="col-md-12">
                            <div class="card text-center">
                                <x-not-found message="{{ __('no_data_found') }}" />
                            </div>
                        </div>
                    @endif
                </div>

                <!-- Loading Overlay -->
                <div id="companies-loading-overlay" class="tw-hidden tw-fixed tw-inset-0 tw-bg-black tw-bg-opacity-50 tw-z-50 tw-flex tw-justify-center tw-items-center">
                    <div class="tw-flex tw-flex-col tw-items-center tw-bg-white tw-p-6 tw-rounded-lg tw-shadow-lg">
                        <div class="briefcase-loader">
                            <i class="ph-briefcase tw-text-4xl tw-text-primary-500"></i>
                        </div>
                        <span class="tw-text-gray-600 tw-mt-3 tw-font-medium">Memuat perusahaan...</span>
                    </div>
                </div>

                <!-- Skeleton Loading -->
                <div id="companies-skeleton" class="tw-hidden row">
                    @for ($i = 0; $i < 6; $i++)
                        <div class="col-md-6 col-xl-4 fade-in-bottom condition_class rt-mb-24 tw-self-stretch">
                            <div class="card jobcardStyle1 tw-relative tw-h-full tw-animate-pulse">
                                <div class="tw-p-6 !tw-pb-[72px]">
                                    <div class="rt-single-icon-box tw-gap-3">
                                        <div class="tw-w-14 tw-h-14 tw-rounded-full tw-overflow-hidden tw-bg-gray-200"></div>
                                        <div class="iconbox-content tw-w-full">
                                            <div class="tw-w-3/4 tw-h-5 tw-bg-gray-200 tw-rounded tw-mb-2"></div>
                                            <div class="tw-w-1/2 tw-h-4 tw-bg-gray-200 tw-rounded"></div>
                                        </div>
                                    </div>
                                    <div class="post-info tw-mt-4">
                                        <div class="tw-flex tw-flex-wrap tw-gap-3">
                                            <div class="tw-w-1/3 tw-h-6 tw-bg-gray-200 tw-rounded-full"></div>
                                            <div class="tw-w-1/4 tw-h-6 tw-bg-gray-200 tw-rounded-full"></div>
                                        </div>
                                        <div class="tw-absolute tw-bottom-6 tw-left-6 tw-w-1/3 tw-h-6 tw-bg-gray-200 tw-rounded"></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    @endfor
                </div>

                <!-- Pagination -->
                <div id="pagination-container" class="rt-pt-30">
                    <nav>
                        {{ $companies->links('vendor.pagination.frontend') }}
                    </nav>
                </div>
            </div>
        </div>
    </div>

    <div class="rt-spacer-100 rt-spacer-md-50"></div>

    {{-- Subscribe Newsletter --}}
    <x-website.subscribe-newsletter />
@endsection

@push('frontend_links')
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css">
    <style>
        .alphabet-filter a.active {
            background-color: var(--primary-500);
            color: white;
        }

        /* Select2 Styling */
        .select2-container--default .select2-selection--multiple {
            border: 1px solid #ced4da;
            border-radius: 0.25rem;
            min-height: 38px;
        }

        .select2-container--default .select2-selection--multiple .select2-selection__choice {
            background-color: var(--primary-500);
            color: white;
            border: none;
            border-radius: 4px;
            margin: 4px;
            padding: 2px 8px;
        }

        .select2-container--default .select2-selection--multiple .select2-selection__choice__remove {
            color: white;
            margin-right: 5px;
        }

        .select2-container--default .select2-selection--multiple .select2-selection__choice__remove:hover {
            color: #f8f9fa;
        }

        .select2-container--default .select2-results__option--highlighted[aria-selected] {
            background-color: var(--primary-500);
        }

        .select2-container--default .select2-results__group {
            font-weight: bold;
            padding: 6px;
            background-color: #f8f9fa;
        }

        .select2-container--default .select2-results__option[aria-selected=true] {
            background-color: #e9ecef;
        }

        .select2-dropdown {
            border: 1px solid #ced4da;
            border-radius: 0 0 0.25rem 0.25rem;
        }

        .select2-container--open .select2-dropdown--below {
            border-top: none;
        }

        .select2-search--dropdown .select2-search__field {
            padding: 8px;
            border: 1px solid #ced4da;
        }

        /* Loading Animation Styles */
        .briefcase-loader {
            display: flex;
            align-items: center;
            justify-content: center;
            height: 80px;
            width: 80px;
            position: relative;
            margin: 0 auto;
        }

        .briefcase-loader i {
            font-size: 2.5rem;
            color: var(--primary-500);
            animation: briefcase-bounce 1.5s infinite ease-in-out;
        }

        @keyframes briefcase-bounce {
            0%, 20%, 50%, 80%, 100% {
                transform: translateY(0);
            }
            40% {
                transform: translateY(-20px);
            }
            60% {
                transform: translateY(-10px);
            }
        }

        /* Button Loading Animation */
        .btn-loading {
            position: relative;
            pointer-events: none;
        }

        .btn-loading::after {
            content: '';
            position: absolute;
            width: 16px;
            height: 16px;
            top: calc(50% - 8px);
            right: 10px;
            border: 2px solid transparent;
            border-top-color: currentColor;
            border-right-color: currentColor;
            border-radius: 50%;
            animation: button-loading-spinner 0.6s linear infinite;
        }

        @keyframes button-loading-spinner {
            from {
                transform: rotate(0turn);
            }
            to {
                transform: rotate(1turn);
            }
        }

        /* Mobile Filter Styles */
        /* Mobile filter button */
        .mobile-filter-btn {
            display: flex;
            position: fixed;
            bottom: 20px;
            left: 20px;
            width: 50px !important;
            height: 50px !important;
            border-radius: 50% !important;
            background-color: var(--primary-500);
            color: white;
            border: none;
            box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2);
            z-index: 1000;
            justify-content: center;
            align-items: center;
            font-size: 20px;
            cursor: pointer;
            animation: pulse 2s infinite;
            padding: 0 !important;
            min-width: 50px !important;
            max-width: 50px !important;
            line-height: 1 !important;
            text-align: center !important;
        }

        .mobile-filter-btn:hover {
            background-color: #0850a0;
        }

        /* Mobile filter sidebar */
        .filter-sidebar {
            position: fixed;
            top: 0;
            left: -100%;
            width: 85%;
            height: 100vh !important;
            background-color: white;
            z-index: 1001;
            overflow-y: auto;
            transition: left 0.3s ease;
            box-shadow: 2px 0 10px rgba(0, 0, 0, 0.1);
            padding: 20px;
        }

        .filter-sidebar.active {
            left: 0;
        }

        .filter-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
            z-index: 1000;
            display: none;
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .filter-overlay.active {
            opacity: 1;
        }

        @keyframes pulse {
            0% {
                box-shadow: 0 0 0 0 rgba(19, 140, 121, 0.7);
            }
            70% {
                box-shadow: 0 0 0 10px rgba(19, 140, 121, 0);
            }
            100% {
                box-shadow: 0 0 0 0 rgba(19, 140, 121, 0);
            }
        }

        /* Briefcase loader animation */
        .briefcase-loader {
            display: flex;
            justify-content: center;
            align-items: center;
            animation: briefcase-bounce 1.5s infinite;
        }

        @keyframes briefcase-bounce {
            0%, 20%, 50%, 80%, 100% {
                transform: translateY(0);
            }
            40% {
                transform: translateY(-20px);
            }
            60% {
                transform: translateY(-10px);
            }
        }

        /* Skeleton animation */
        .tw-animate-pulse {
            animation: pulse-animation 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
        }

        @keyframes pulse-animation {
            0%, 100% {
                opacity: 1;
            }
            50% {
                opacity: 0.5;
            }
        }

        /* Select2 Mobile Styles */
        .select2-container {
            width: 100% !important;
        }

        @media (max-width: 767px) {
            .select2-container--open .select2-dropdown {
                width: 100% !important;
                left: 0 !important;
            }
        }

        /* Desktop Filter Floating Styles */
        #desktop-filter-card {
            position: sticky;
            top: 20px;
            max-height: calc(100vh - 40px);
            overflow-y: auto;
        }

        /* Hide scrollbar for desktop filter */
        #desktop-filter-card::-webkit-scrollbar {
            width: 0;
            background: transparent;
        }

        #desktop-filter-card {
            scrollbar-width: none;
            -ms-overflow-style: none;
        }

        /* Active Filter Styles */
        .active-filter-item {
            transition: all 0.3s ease;
        }

        .active-filter-item:hover {
            background-color: #f3f4f6;
        }

        .active-filter-item button {
            transition: all 0.2s ease;
        }

        .active-filter-item button:hover {
            transform: scale(1.1);
        }
    </style>
@endpush

@push('frontend_scripts')
    <script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>
    <script>
        $(document).ready(function() {
            // Initialize Select2 for desktop
            $('.select2-organization').select2({
                placeholder: 'Pilih Badan Hukum',
                allowClear: true,
                closeOnSelect: true,
                language: {
                    noResults: function() {
                        return "Tidak ada hasil yang ditemukan";
                    }
                }
            });

            $('.select2-industry').select2({
                placeholder: 'Pilih Industri',
                allowClear: true,
                closeOnSelect: true,
                language: {
                    noResults: function() {
                        return "Tidak ada hasil yang ditemukan";
                    }
                }
            });

            $('.select2-location').select2({
                placeholder: 'Pilih Lokasi',
                allowClear: true,
                closeOnSelect: true,
                language: {
                    noResults: function() {
                        return "Tidak ada hasil yang ditemukan";
                    }
                }
            });

            // Initialize Select2 for mobile (single selection)
            $('.select2-organization-mobile').select2({
                placeholder: 'Pilih Badan Hukum',
                allowClear: true,
                closeOnSelect: true,
                dropdownParent: $('#mobile-filter-content'),
                language: {
                    noResults: function() {
                        return "Tidak ada hasil yang ditemukan";
                    }
                }
            });

            $('.select2-industry-mobile').select2({
                placeholder: 'Pilih Industri',
                allowClear: true,
                closeOnSelect: true,
                dropdownParent: $('#mobile-filter-content'),
                language: {
                    noResults: function() {
                        return "Tidak ada hasil yang ditemukan";
                    }
                }
            });

            $('.select2-location-mobile').select2({
                placeholder: 'Pilih Lokasi',
                allowClear: true,
                closeOnSelect: true,
                dropdownParent: $('#mobile-filter-content'),
                language: {
                    noResults: function() {
                        return "Tidak ada hasil yang ditemukan";
                    }
                }
            });

            // Mobile filter sidebar toggle
            $('#mobile-filter-button').on('click', function() {
                $('#mobile-filter-sidebar').addClass('active');
                $('#filter-overlay').show().addClass('active');
            });

            $('#close-mobile-filter').on('click', function() {
                closeFilterSidebar();
            });

            // Close mobile filter when clicking overlay
            $('#filter-overlay').on('click', function() {
                closeFilterSidebar();
            });

            // Function to close filter sidebar
            function closeFilterSidebar() {
                $('#mobile-filter-sidebar').removeClass('active');
                $('#filter-overlay').removeClass('active');
                setTimeout(function() {
                    $('#filter-overlay').hide();
                }, 300);
            }

            // No need to sync desktop and mobile filters since they have different selection modes
            // Desktop is multiple selection, mobile is single selection

            // Mobile reset filter
            $('#mobile-reset-filter').on('click', function(e) {
                e.preventDefault();

                // Show loading overlay and skeleton
                $('#companies-loading-overlay').removeClass('tw-hidden');
                $('#companies-container').addClass('tw-hidden');
                $('#companies-skeleton').removeClass('tw-hidden');

                // Add loading animation to button
                $(this).addClass('btn-loading');

                // Close mobile filter sidebar
                closeFilterSidebar();

                // Reset URL without parameters
                var url = '{{ route("website.company") }}';
                window.history.pushState({}, '', url);

                // Make AJAX request
                $.ajax({
                    url: url,
                    type: 'GET',
                    headers: {
                        'X-Requested-With': 'XMLHttpRequest'
                    },
                    success: function(response) {
                        // Hide loading overlay and skeleton
                        $('#companies-loading-overlay').addClass('tw-hidden');
                        $('#companies-skeleton').addClass('tw-hidden');
                        $('#companies-container').removeClass('tw-hidden');

                        // Remove loading animation from all buttons
                        $('.btn-loading').removeClass('btn-loading');

                        // Update companies container
                        $('#companies-container').html(response.html);

                        // Update pagination
                        $('#pagination-container').html(response.pagination);

                        // Update total count
                        $('#total-companies').text(response.total);

                        // Reset all form fields
                        $('#company-filter-form')[0].reset();
                        $('#mobile-company-filter-form')[0].reset();

                        // Reset all select2 fields
                        $('#organization_types, #industry_types, #location').val(null).trigger('change');
                        $('#mobile_organization_types, #mobile_industry_types, #mobile_location').val(null).trigger('change');

                        // Scroll to top of companies container
                        $('html, body').animate({
                            scrollTop: $('#companies-container').offset().top - 100
                        }, 800);
                    },
                    error: function(xhr) {
                        // Hide loading overlay and skeleton
                        $('#companies-loading-overlay').addClass('tw-hidden');
                        $('#companies-skeleton').addClass('tw-hidden');
                        $('#companies-container').removeClass('tw-hidden');

                        // Remove loading animation from all buttons
                        $('.btn-loading').removeClass('btn-loading');

                        console.log(xhr.responseText);
                    }
                });
            });



            // Handle pagination click with AJAX
            $(document).on('click', '.pagination a', function(e) {
                e.preventDefault();
                var page = $(this).attr('href').split('page=')[1];
                var url = $('#company-filter-form').attr('action') + '?' + $('#company-filter-form').serialize() + '&page=' + page;

                // Update URL with filter parameters
                window.history.pushState({}, '', url);

                // Show loading overlay and skeleton
                $('#companies-loading-overlay').removeClass('tw-hidden');
                $('#companies-container').addClass('tw-hidden');
                $('#companies-skeleton').removeClass('tw-hidden');

                // Add loading animation to button
                $(this).addClass('btn-loading');

                $.ajax({
                    url: url,
                    type: 'GET',
                    headers: {
                        'X-Requested-With': 'XMLHttpRequest'
                    },
                    success: function(response) {
                        // Hide loading overlay and skeleton
                        $('#companies-loading-overlay').addClass('tw-hidden');
                        $('#companies-skeleton').addClass('tw-hidden');
                        $('#companies-container').removeClass('tw-hidden');

                        // Update companies container
                        $('#companies-container').html(response.html);

                        // Update pagination
                        $('#pagination-container').html(response.pagination);

                        // Update total count
                        $('#total-companies').text(response.total);

                        // Scroll to top of companies container
                        $('html, body').animate({
                            scrollTop: $('#companies-container').offset().top - 100
                        }, 500);
                    },
                    error: function(xhr) {
                        // Hide loading overlay and skeleton
                        $('#companies-loading-overlay').addClass('tw-hidden');
                        $('#companies-skeleton').addClass('tw-hidden');
                        $('#companies-container').removeClass('tw-hidden');

                        console.log(xhr.responseText);
                    }
                });
            });

            // Handle form submission with AJAX
            $('#company-filter-form, #mobile-company-filter-form').on('submit', function(e) {
                e.preventDefault();

                // Prepare URL with all current filters
                var url = $(this).attr('action') + '?' + $(this).serialize();

                // Update URL with filter parameters
                window.history.pushState({}, '', url);

                // Show loading overlay and skeleton
                $('#companies-loading-overlay').removeClass('tw-hidden');
                $('#companies-container').addClass('tw-hidden');
                $('#companies-skeleton').removeClass('tw-hidden');

                // Add loading animation to button and disable it
                var submitBtn = $('button[type="submit"]', this);
                submitBtn.prop('disabled', true).addClass('btn-loading');

                // Close mobile filter sidebar if open
                closeFilterSidebar();

                // Set a timeout to simulate loading for 3 seconds
                setTimeout(function() {
                    // Make AJAX request
                    $.ajax({
                        url: url,
                        type: 'GET',
                        headers: {
                            'X-Requested-With': 'XMLHttpRequest'
                        },
                        success: function(response) {
                            // Hide loading overlay and skeleton
                            $('#companies-loading-overlay').addClass('tw-hidden');
                            $('#companies-skeleton').addClass('tw-hidden');
                            $('#companies-container').removeClass('tw-hidden');

                            // Remove loading animation from all buttons and enable them
                            $('.btn-loading').removeClass('btn-loading').prop('disabled', false);

                            // Update companies container
                            $('#companies-container').html(response.html);

                            // Update pagination
                            $('#pagination-container').html(response.pagination);

                            // Update total count
                            $('#total-companies').text(response.total);

                            // Scroll to top of companies container
                            $('html, body').animate({
                                scrollTop: $('#companies-container').offset().top - 100
                            }, 800);
                        },
                        error: function(xhr) {
                            // Hide loading overlay and skeleton
                            $('#companies-loading-overlay').addClass('tw-hidden');
                            $('#companies-skeleton').addClass('tw-hidden');
                            $('#companies-container').removeClass('tw-hidden');

                            // Remove loading animation from all buttons and enable them
                            $('.btn-loading').removeClass('btn-loading').prop('disabled', false);

                            console.log(xhr.responseText);
                        }
                    });
                }, 3000); // 3 seconds delay
            });

            // Handle reset filter
            $('#reset-filter').on('click', function(e) {
                e.preventDefault();

                // Show loading overlay and skeleton
                $('#companies-loading-overlay').removeClass('tw-hidden');
                $('#companies-container').addClass('tw-hidden');
                $('#companies-skeleton').removeClass('tw-hidden');

                // Add loading animation to button
                $(this).addClass('btn-loading');

                // Close mobile filter sidebar if open
                closeFilterSidebar();

                // Reset URL without parameters
                var url = '{{ route("website.company") }}';
                window.history.pushState({}, '', url);

                // Make AJAX request
                $.ajax({
                    url: url,
                    type: 'GET',
                    headers: {
                        'X-Requested-With': 'XMLHttpRequest'
                    },
                    success: function(response) {
                        // Hide loading overlay and skeleton
                        $('#companies-loading-overlay').addClass('tw-hidden');
                        $('#companies-skeleton').addClass('tw-hidden');
                        $('#companies-container').removeClass('tw-hidden');

                        // Remove loading animation from all buttons
                        $('.btn-loading').removeClass('btn-loading');

                        // Update companies container
                        $('#companies-container').html(response.html);

                        // Update pagination
                        $('#pagination-container').html(response.pagination);

                        // Update total count
                        $('#total-companies').text(response.total);

                        // Reset all form fields
                        $('#company-filter-form')[0].reset();
                        $('#mobile-company-filter-form')[0].reset();

                        // Reset all select2 fields
                        $('#organization_types, #industry_types, #location').val(null).trigger('change');
                        $('#mobile_organization_types, #mobile_industry_types, #mobile_location').val(null).trigger('change');

                        // Scroll to top of companies container
                        $('html, body').animate({
                            scrollTop: $('#companies-container').offset().top - 100
                        }, 800);
                    },
                    error: function(xhr) {
                        // Hide loading overlay and skeleton
                        $('#companies-loading-overlay').addClass('tw-hidden');
                        $('#companies-skeleton').addClass('tw-hidden');
                        $('#companies-container').removeClass('tw-hidden');

                        // Remove loading animation from all buttons
                        $('.btn-loading').removeClass('btn-loading');

                        console.log(xhr.responseText);
                    }
                });
            });
        });

        // Functions to remove individual filters
        function removeFilter(name) {
            // Clear the filter value
            $('[name="' + name + '"]').val('');

            // Add loading animation to the clicked button
            var btn = $('button[onclick="removeFilter(\'' + name + '\')"]');
            btn.addClass('btn-loading');

            // Remove the active filter display
            btn.closest('.active-filter-item').remove();

            // Close mobile filter sidebar if open
            closeFilterSidebar();

            // Submit the form
            $('#company-filter-form').submit();
        }

        function removeOrganization(id) {
            // Clear organization type filter
            $('#organization_types').val('').trigger('change');

            // Add loading animation to the clicked button
            var btn = $('button[onclick="removeOrganization(\'' + id + '\')"]');
            btn.addClass('btn-loading');

            // Remove the active filter display
            btn.closest('.active-filter-item').remove();

            // Close mobile filter sidebar if open
            closeFilterSidebar();

            // Submit the form
            $('#company-filter-form').submit();
        }

        function removeIndustry(id) {
            // Remove the item from desktop select2 only
            var currentValues = $('#industry_types').val() || [];
            var newValues = currentValues.filter(function(value) {
                return value != id;
            });

            $('#industry_types').val(newValues).trigger('change');

            // Add loading animation to the clicked button
            var btn = $('button[onclick="removeIndustry(\'' + id + '\')"]');
            btn.addClass('btn-loading');

            // Remove the active filter display
            btn.closest('.active-filter-item').remove();

            // Close mobile filter sidebar if open
            closeFilterSidebar();

            // Submit the form
            $('#company-filter-form').submit();
        }

        function removeLocation() {
            // Clear location filter
            $('#location').val('').trigger('change');
            $('#mobile_location').val('').trigger('change');

            // Add loading animation to the clicked button
            var btn = $('button[onclick="removeLocation()"]');
            btn.addClass('btn-loading');

            // Remove the active filter display
            btn.closest('.active-filter-item').remove();

            // Close mobile filter sidebar if open
            closeFilterSidebar();

            // Submit the form
            $('#company-filter-form').submit();
        }
    </script>
@endpush
