@extends('frontend.layouts.app')

@section('description')
    @php
        $data = metaData('contact');
    @endphp
    {{ $data->description }}
@endsection
@section('og:image')
    {{ asset($data->image) }}
@endsection
@section('title')
    Saran dan <PERSON>
@endsection

@section('main')
    <div class="breadcrumbs-custom breadcrumbs-height">
        <div class="container">
            <div class="breadcrumb-menu">
                <h6 class="f-size-18 m-0">Saran dan <PERSON></h6>
                <ul>
                    <li><a href="{{ route('website.home') }}">{{ __('home') }}</a></li>
                    <li>/</li>
                    <li>Saran dan <PERSON></li>
                </ul>
            </div>
        </div>
    </div>
    <div class="rt-contact">
        <div class="container">
            <div class="rt-spacer-100 rt-spacer-md-50"></div>
            <div class="row align-items-center">
                <div class="col-xl-6 col-lg-6 rt-mb-lg-30 ">
                    <div class="pl30">
                        <span class="body-font-3 ft-wt-5 text-primary-500 rt-mb-15 d-inline-block">Kotak Saran</span>
                        <h2 class="lg:tw-mb-8 md:tw-mb-6 tw-mb-4">Kami Menghargai Masukan Anda</h2>
                        <p class="body-font-2 text-gray-500 rt-mb-32">
                            Masukan dan saran Anda sangat berharga bagi kami untuk terus meningkatkan layanan. Silakan berikan penilaian dan masukan Anda melalui formulir ini.
                        </p>
                        <a href="mailto:{{ $setting->email }}" target="__blank" class="btn btn-primary btn-lg">
                            {{ __('email_support') }}</a>
                    </div>
                </div>
                <div class="col-xl-6 col-lg-6">
                    <div class="contact-auth-box ct-wrap">
                        <form id="feedbackForm" action="{{ route('website.feedback.store') }}" class="rt-form" method="POST">
                            @csrf
                            <h5 class="rt-mb-32">Berikan Masukan Anda</h5>
                            <div class="row">
                                <div class="col-xl-6 col-lg-6">
                                    <div class="fromGroup rt-mb-15">
                                        <input id="name" class="form-control @error('name') is-invalid @enderror"
                                            type="text" placeholder="Nama" name="name"
                                            value="{{ auth()->check() ? auth()->user()->name : old('name') }}" {{ auth()->check() ? 'readonly' : '' }}>
                                        @error('name')
                                            <span class="invalid-feedback"
                                                role="alert"><strong>{{ $message }}</strong></span>
                                        @enderror
                                    </div>
                                </div>
                                <div class="col-xl-6 col-lg-6">
                                    <div class="fromGroup rt-mb-15">
                                        <input id="email" class="form-control @error('email') is-invalid @enderror"
                                            type="email" placeholder="Email" name="email"
                                            value="{{ auth()->check() ? auth()->user()->email : old('email') }}" {{ auth()->check() ? 'readonly' : '' }}>
                                        @error('email')
                                            <span class="invalid-feedback"
                                                role="alert"><strong>{{ $message }}</strong></span>
                                        @enderror
                                    </div>
                                </div>
                            </div>
                            <div class="fromGroup rt-mb-15">
                                <input id="phone" class="form-control @error('phone') is-invalid @enderror"
                                    type="text" placeholder="Nomor Telepon" name="phone"
                                    value="{{ auth()->check() && auth()->user()->contactInfo ? auth()->user()->contactInfo->phone : old('phone') }}" {{ auth()->check() && auth()->user()->contactInfo ? 'readonly' : '' }}>
                                @error('phone')
                                    <span class="invalid-feedback" role="alert"><strong>{{ $message }}</strong></span>
                                @enderror
                            </div>
                            <div class="rt-mb-15">
                                <label class="body-font-4 text-gray-700 rt-mb-8">Berikan Penilaian</label>
                                <div class="rating-stars rt-mb-15">
                                    <div class="d-flex align-items-center">
                                        <div class="rating-group">
                                            <input type="radio" class="rating-input" id="rating-5" name="rating" value="5" {{ old('rating') == 5 ? 'checked' : '' }}>
                                            <label for="rating-5" class="rating-label">
                                                <i class="ph-star-fill"></i>
                                            </label>
                                            <input type="radio" class="rating-input" id="rating-4" name="rating" value="4" {{ old('rating') == 4 ? 'checked' : '' }}>
                                            <label for="rating-4" class="rating-label">
                                                <i class="ph-star-fill"></i>
                                            </label>
                                            <input type="radio" class="rating-input" id="rating-3" name="rating" value="3" {{ old('rating') == 3 ? 'checked' : '' }}>
                                            <label for="rating-3" class="rating-label">
                                                <i class="ph-star-fill"></i>
                                            </label>
                                            <input type="radio" class="rating-input" id="rating-2" name="rating" value="2" {{ old('rating') == 2 ? 'checked' : '' }}>
                                            <label for="rating-2" class="rating-label">
                                                <i class="ph-star-fill"></i>
                                            </label>
                                            <input type="radio" class="rating-input" id="rating-1" name="rating" value="1" {{ old('rating') == 1 ? 'checked' : '' }}>
                                            <label for="rating-1" class="rating-label">
                                                <i class="ph-star-fill"></i>
                                            </label>
                                        </div>
                                        <span class="rating-value ms-2" id="rating-value">0/5</span>
                                    </div>
                                    @error('rating')
                                        <span class="text-danger">
                                            <strong>{{ $message }}</strong>
                                        </span>
                                    @enderror
                                </div>
                            </div>
                            <div class="rt-mb-15 tarea-dafault">
                                <textarea id="message" class="form-control @error('message') is-invalid @enderror" type="text"
                                    placeholder="Masukan dan Saran" name="message" rows="5">{{ old('message') }}</textarea>
                                @error('message')
                                    <span class="invalid-feedback" role="alert"><strong>{{ $message }}</strong></span>
                                @enderror
                            </div>
                            @if (config('captcha.active'))
                                <div class="rt-mb-10 tarea-dafault g-custom-css">
                                    {!! NoCaptcha::display() !!}
                                    @error('g-recaptcha-response')
                                        <span class="text-danger">
                                            <strong>{{ $message }}</strong>
                                        </span>
                                    @enderror
                                </div>
                            @endif
                            <button type="submit" class="btn btn-primary d-block rt-mb-15" id="submitButton" disabled>
                                <span class="button-content-wrapper ">
                                    <span class="button-icon align-icon-right">
                                        <svg width="24" height="24" viewBox="0 0 24 24" fill="none"
                                            xmlns="http://www.w3.org/2000/svg">
                                            <path d="M22 2L11 13" stroke="white" stroke-width="1.5" stroke-linecap="round"
                                                stroke-linejoin="round" />
                                            <path d="M22 2L15 22L11 13L2 9L22 2Z" stroke="white" stroke-width="1.5"
                                                stroke-linecap="round" stroke-linejoin="round" />
                                        </svg>
                                    </span>
                                    <span class="button-text rt-mr-8">
                                        Kirim Masukan
                                    </span>
                                </span>
                            </button>
                        </form>
                    </div>
                </div>
            </div>
            <div class="rt-spacer-100 rt-spacer-md-50"></div>
        </div>
    </div>

    {{-- Subscribe Newsletter --}}
    <x-website.subscribe-newsletter />
@endsection

@section('css')
    <style>
        .breadcrumbs-custom {
            padding: 20px;
            background-color: var(--gray-20);
            transition: all 0.24s ease-in-out;
        }

        .rating-group {
            display: flex;
            flex-direction: row-reverse;
            position: relative;
        }

        .rating-input {
            position: absolute;
            left: -9999px;
        }

        .rating-label {
            cursor: pointer;
            padding: 0 0.1em;
            font-size: 2rem;
            color: #ddd;
        }

        .rating-label:hover,
        .rating-label:hover ~ .rating-label,
        .rating-input:checked ~ .rating-label {
            color: #FFD700;
        }

        .rating-value {
            font-size: 1rem;
            font-weight: bold;
        }

        /* Mobile responsive */
        @media (max-width: 768px) {
            .rating-label {
                font-size: 1.5rem;
            }
        }
    </style>
@endsection

@section('script')
    <script src='https://www.google.com/recaptcha/api.js'></script>
    <script>
        $(document).ready(function() {
            validate();
            $('#name, #email, #message').keyup(validate);
            $('input[name="rating"]').change(validate);

            // Update rating value display
            $('input[name="rating"]').change(function() {
                $('#rating-value').text($(this).val() + '/5');
            });

            // Check if a rating is already selected (e.g., from old input)
            const selectedRating = $('input[name="rating"]:checked').val();
            if (selectedRating) {
                $('#rating-value').text(selectedRating + '/5');
            }

            // AJAX form submission
            $('#feedbackForm').submit(function(e) {
                e.preventDefault();
                
                const submitBtn = $('#submitButton');
                const originalBtnText = submitBtn.html();
                
                // Show loading state
                submitBtn.html('<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> Mengirim...');
                submitBtn.prop('disabled', true);
                
                $.ajax({
                    url: $(this).attr('action'),
                    type: 'POST',
                    data: $(this).serialize(),
                    dataType: 'json',
                    success: function(response) {
                        // Show success message
                        toastr.success(response.message || 'Terima kasih atas saran dan masukan Anda!');
                        
                        // Reset form
                        $('#feedbackForm')[0].reset();
                        $('#rating-value').text('0/5');
                        
                        // Reset button
                        submitBtn.html(originalBtnText);
                        submitBtn.prop('disabled', true);
                    },
                    error: function(xhr) {
                        // Reset button
                        submitBtn.html(originalBtnText);
                        submitBtn.prop('disabled', false);
                        
                        // Show error message
                        if (xhr.responseJSON && xhr.responseJSON.errors) {
                            const errors = xhr.responseJSON.errors;
                            for (const key in errors) {
                                toastr.error(errors[key][0]);
                            }
                        } else {
                            toastr.error('Terjadi kesalahan. Silakan coba lagi.');
                        }
                    }
                });
            });
        });

        function validate() {
            if (
                $('#name').val().length > 0 &&
                $('#email').val().length > 0 &&
                $('#message').val().length > 0 &&
                $('input[name="rating"]:checked').length > 0
            ) {
                $('#submitButton').prop('disabled', false);
            } else {
                $('#submitButton').prop('disabled', true);
            }
        }
    </script>
@endsection
