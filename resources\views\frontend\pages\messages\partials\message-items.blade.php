@foreach($groupedMessages as $date => $messages)
    <div class="message-date-header text-center my-3 position-relative">
        <span class="badge bg-light text-dark px-3 py-2 position-relative">
            {{ \Carbon\Carbon::parse($date)->format('d F Y') }}
        </span>
    </div>
    @foreach($messages as $index => $message)
        @include('frontend.pages.messages.partials.single-message', ['message' => $message, 'index' => $startIndex + $index])
    @endforeach
@endforeach
