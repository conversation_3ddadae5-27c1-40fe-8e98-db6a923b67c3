<div class="message-item mb-3 {{ auth()->id() == $message->sender_id ? 'sender' : 'receiver' }}" id="message-{{ $message->id }}">
    <div class="d-flex {{ auth()->id() == $message->sender_id ? 'justify-content-end' : 'justify-content-start' }}">
        @if(auth()->id() != $message->sender_id)
            <div class="avatar me-2">
                @if($message->sender->role == 'company')
                    <img src="{{ asset(str_replace('/company/uploads/', '/uploads/', $message->sender->company->logo ?? 'frontend/assets/images/default-company.png')) }}" alt="{{ $message->sender->name }}" class="rounded-circle" width="40" height="40" style="object-fit: cover;">
                @elseif($message->sender->role == 'candidate')
                    <img src="{{ asset($message->sender->candidate->photo ?? 'frontend/assets/images/default-user.png') }}" alt="{{ $message->sender->name }}" class="rounded-circle" width="40" height="40" style="object-fit: cover;">
                @elseif($message->sender->role == 'admin')
                    <img src="{{ asset('frontend/assets/images/admin-avatar.png') }}" alt="Admin" class="rounded-circle" width="40" height="40" style="object-fit: cover;">
                @endif
            </div>
        @endif

        <div class="message-content {{ auth()->id() == $message->sender_id ? 'bg-light-success' : 'bg-light' }}" style="max-width: 75%; border-radius: 10px; padding: 10px 15px;">
            <small class="text-muted fst-italic d-block mb-1">{{ $message->created_at->format('d/m/Y - H:i') }}</small>
            <div class="message-header d-flex justify-content-between align-items-center mb-2">
                <span class="fw-bold">
                    @if($message->sender->role == 'company')
                        {{ $message->sender->company->organization_type ?? '' }} {{ $message->sender->name }}
                    @else
                        {{ $message->sender->name }}
                    @endif
                </span>
            </div>
            <div class="message-text">
                {!! nl2br(e($message->body)) !!}
            </div>
            @if($message->attachment && !empty($message->attachment))
                <div class="message-attachments mt-2">
                    @php
                        // Handle different attachment formats
                        $attachments = [];
                        if (is_array($message->attachment)) {
                            if (isset($message->attachment['url'])) {
                                // Format 1: Single attachment with direct url
                                $attachments[] = [
                                    'url' => $message->attachment['url'],
                                    'name' => $message->attachment['name'] ?? 'Lampiran',
                                    'type' => $message->attachment['type'] ?? ''
                                ];
                            } elseif (isset($message->attachment[0]) && is_array($message->attachment[0])) {
                                // Format 2: Array of attachments with path
                                $attachments = $message->attachment;
                            } else {
                                // Try to process as a regular array
                                $attachments = [$message->attachment];
                            }
                        } else {
                            // String format (unlikely but handle it)
                            $attachments[] = [
                                'url' => $message->attachment,
                                'name' => 'Lampiran',
                                'type' => ''
                            ];
                        }

                        // Debug attachment info
                        // echo "<pre>Attachment: " . json_encode($message->attachment, JSON_PRETTY_PRINT) . "</pre>";
                    @endphp

                    @foreach($attachments as $attachment)
                        <div class="attachment mb-2">
                            @php
                                // Determine file URL based on format
                                if (isset($attachment['url'])) {
                                    $fileUrl = $attachment['url'];
                                    $fileName = $attachment['name'] ?? 'Lampiran';
                                    $fileType = $attachment['type'] ?? '';
                                } elseif (isset($attachment['path'])) {
                                    $fileUrl = asset('public/storage/' . $attachment['path']);
                                    $fileName = $attachment['name'] ?? 'Lampiran';
                                    $fileType = $attachment['type'] ?? '';
                                } else {
                                    $fileUrl = '';
                                    $fileName = 'Lampiran';
                                    $fileType = '';
                                }

                                // Determine file type from extension if not provided
                                if (!$fileType || $fileType === '') {
                                    $extension = strtolower(pathinfo($fileUrl, PATHINFO_EXTENSION));
                                    if (in_array($extension, ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'svg', 'webp'])) {
                                        $fileType = 'image';
                                    } elseif (in_array($extension, ['pdf', 'doc', 'docx'])) {
                                        $fileType = 'document';
                                    }
                                }

                                // If type is a MIME type, extract the main type
                                if (strpos($fileType, '/') !== false) {
                                    list($mainType, $subType) = explode('/', $fileType, 2);
                                    if ($mainType === 'image') {
                                        $fileType = 'image';
                                    } elseif ($mainType === 'application' && ($subType === 'pdf' || strpos($subType, 'pdf') !== false)) {
                                        $fileType = 'document';
                                    }
                                }

                                // Debug info
                                // echo "File URL: $fileUrl, Type: $fileType, Name: $fileName<br>";
                            @endphp

                            @if($fileType == 'document')
                                <div class="pdf-attachment p-3 border rounded bg-light">
                                    <div class="d-flex align-items-center mb-2">
                                        <i class="fas fa-file-pdf text-danger fs-4 me-2"></i>
                                        <span class="text-truncate" style="max-width: 200px;" title="{{ $fileName }}">
                                            {{ $fileName }}
                                        </span>
                                    </div>
                                    <div class="btn-group btn-group-sm w-100">
                                        <a href="{{ $fileUrl }}" target="_blank" class="btn btn-outline-primary">
                                            <i class="fas fa-download me-1"></i> {{ __('Unduh') }}
                                        </a>
                                        <button type="button" class="btn btn-outline-secondary pdf-preview-btn" data-pdf-url="{{ $fileUrl }}" data-pdf-name="{{ $fileName }}">
                                            <i class="fas fa-eye me-1"></i> {{ __('Pratinjau') }}
                                        </button>
                                    </div>
                                </div>
                            @elseif($fileType == 'image')
                                <div class="image-attachment">
                                    <a href="{{ $fileUrl }}" class="image-lightbox" data-caption="{{ $message->body }}">
                                        <img src="{{ $fileUrl }}" alt="{{ $fileName }}" class="img-fluid rounded" style="max-height: 200px;">
                                    </a>
                                </div>
                            @else
                                <a href="{{ $fileUrl }}" target="_blank" class="btn btn-sm btn-outline-primary">
                                    <i class="fas fa-paperclip me-1"></i> {{ $fileName }}
                                </a>
                            @endif
                        </div>
                    @endforeach
                </div>
            @endif
        </div>

        @if(auth()->id() == $message->sender_id)
            <div class="avatar ms-2">
                @if($message->sender->role == 'company')
                    <img src="{{ asset(str_replace('/company/uploads/', '/uploads/', $message->sender->company->logo ?? 'frontend/assets/images/default-company.png')) }}" alt="{{ $message->sender->name }}" class="rounded-circle" width="40" height="40" style="object-fit: cover;">
                @elseif($message->sender->role == 'candidate')
                    <img src="{{ asset($message->sender->candidate->photo ?? 'frontend/assets/images/default-user.png') }}" alt="{{ $message->sender->name }}" class="rounded-circle" width="40" height="40" style="object-fit: cover;">
                @elseif($message->sender->role == 'admin')
                    <img src="{{ asset('frontend/assets/images/admin-avatar.png') }}" alt="Admin" class="rounded-circle" width="40" height="40" style="object-fit: cover;">
                @endif
            </div>
        @endif
    </div>
</div>
