<!--  canidates   -->
<div class="" id="togglclass1">
    <div class="tab-content" id="nav-tabContent">
        <!-- Loading Animation -->
        <div id="candidate-loading" class="d-none">
            <div class="tw-flex tw-justify-between tw-items-center tw-mb-3">
                <div class="skeleton-item tw-h-7 tw-w-32 tw-rounded-md"></div>
                <div class="tw-flex tw-flex-col tw-items-end">
                    <div class="skeleton-item tw-h-5 tw-w-40 tw-rounded-md tw-mb-1"></div>
                    <div class="skeleton-item tw-h-5 tw-w-32 tw-rounded-md"></div>
                </div>
            </div>
            <div class="row">
                @for ($i = 0; $i < 9; $i++)
                    <div class="@if (request('education') || request('gender') || request('experience') || request('skills')) col-md-6 fade-in-bottom rt-mb-24 @else col-md-6 fade-in-bottom rt-mb-24 col-xl-4 @endif">
                        <div class="card jobcardStyle1 body-24 tw-animate-pulse skeleton-card">
                            <div class="card-body">
                                <div class="rt-single-icon-box icb-clmn-lg tw-reltaive">
                                    <div class="icon-thumb tw-relative">
                                        <div class="profile-image tw-bg-gray-200 tw-rounded-full tw-w-16 tw-h-16"></div>
                                        <div class="tw-absolute tw-top-0 tw-left-1">
                                            <div class="tw-inline-flex">
                                                <div class="tw-bg-gray-200 tw-rounded-full tw-w-3.5 tw-h-3.5"></div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="iconbox-content !tw-m-0">
                                        <div class="job-mini-title">
                                            <div class="tw-bg-gray-200 tw-h-5 tw-w-32 tw-rounded-md"></div>
                                        </div>
                                        <div class="tw-bg-gray-200 tw-h-4 tw-w-24 tw-rounded-md tw-mt-2"></div>
                                        <div class="tw-bg-gray-200 tw-h-4 tw-w-20 tw-rounded-md tw-mt-2"></div>
                                        <div class="bottom-link mt-1">
                                            <div class="tw-bg-gray-200 tw-h-4 tw-w-28 tw-rounded-md tw-mt-2"></div>
                                        </div>
                                    </div>
                                    <div class="tw-inline-flex tw-justify-center tw-items-center tw-absolute tw-top-3 tw-right-3">
                                        <div class="tw-bg-gray-200 tw-rounded-full tw-w-5 tw-h-5"></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                @endfor
            </div>
        </div>

        <!-- Actual Candidate Listings -->
        <div class="tab-pane show active" id="nav-home" role="tabpanel" aria-labelledby="nav-home-tab">
            <div id="candidate-listings">
                @if ($candidates->count() > 0)
                    <div class="tw-flex tw-justify-between tw-items-center tw-mb-3">
                        <h5>List Pencaker</h5>
                        <p class="tw-text-sm tw-text-[#767F8C]">
                            Halaman {{ $candidates->currentPage() }} dari {{ $candidates->lastPage() }}
                        </p>
                    </div>
                    <div class="row">
                        @foreach ($candidates as $candidate)
                            @if ($candidate->user)
                            <div class="@if (request('education') || request('gender') || request('experience') || request('skills')) col-md-6 fade-in-bottom condition_class rt-mb-24 @else col-md-6 fade-in-bottom condition_class rt-mb-24 col-xl-4 @endif">
                                <div class="card jobcardStyle1 body-24 position-relative">
                                    <div class="card-body">
                                        <a href="{{ route('website.candidate.profile', $candidate->user->username) }}" class="text-decoration-none">
                                            <div class="rt-single-icon-box icb-clmn-lg">
                                                <div class="icon-thumb tw-relative">
                                                    <div class="profile-image">
                                                        <img src="{{ $candidate->photo }}" alt="Foto {{ $candidate->user->username }}">
                                                    </div>
                                                    <div class="tw-absolute tw-top-0 tw-left-1">
                                                        @if ($candidate->status == 'available')
                                                            <div class="tw-inline-flex">
                                                                <svg width="14" height="14" viewBox="0 0 14 14"
                                                                    fill="none" xmlns="http://www.w3.org/2000/svg">
                                                                    <circle cx="7" cy="7" r="6"
                                                                        fill="#2ecc71" stroke="white" stroke-width="2">
                                                                    </circle>
                                                                </svg>
                                                            </div>
                                                        @endif
                                                    </div>
                                                </div>
                                                <div class="iconbox-content">
                                                    <div class="job-mini-title">
                                                        @if (auth('user')->check())
                                                            <span>{{ $candidate->user->name }}</span>
                                                        @else
                                                            <span class="login_required">{{ maskFullName($candidate->user->name) }}</span>
                                                        @endif
                                                    </div>
                                                    @if ($candidate->birth_date)
                                                        <span class="loacton text-gray-500">
                                                            {{ \Carbon\Carbon::parse($candidate->birth_date)->age }} Tahun
                                                        </span>
                                                    @endif
                                                    <span class="body-font-4 mt-1 text-gray-900 d-block">
                                                        {{ $candidate->education ? $candidate->education->name : 'Pendidikan tidak diketahui' }}
                                                    </span>
                                                </div>
                                            </div>
                                        </a>
                                        <div class="mt-3">
                                            @if (auth('user')->check())
                                                <button type="button" onclick="showCandidateProfileModal('{{ $candidate->user->username }}')"
                                                    class="btn btn-outline-primary btn-sm w-100 candidate-resume-btn">
                                                    <span class="button-text">{{ __('view_resume') }}</span>
                                                    <span class="button-loading d-none">
                                                        <span class="spinner-border spinner-border-sm me-2" role="status"></span>Loading...
                                                    </span>
                                                </button>
                                            @else
                                                <button type="button" class="btn btn-outline-primary btn-sm w-100 login_required">
                                                    {{ __('view_resume') }}
                                                </button>
                                            @endif
                                        </div>
                                    </div>
                                    <!-- Eye icon for viewed candidates -->
                                    @if ($candidate->already_view)
                                        <div class="position-absolute" style="top: 12px; right: 12px;">
                                            <div data-bs-toggle="tooltip" data-bs-placement="top"
                                                title="{{ __('you_have_seen_the_cv_on') }} {{ $candidate->already_views && $candidate->already_views[0] ? $candidate->already_views[0]->view_date_time : '-' }}. After {{ $candidate->already_views && $candidate->already_views[0] ? $candidate->already_views[0]->expired_date : '-' }} {{ __('the_view_count_will_be_reset') }}"
                                                class="cursor-pointer">
                                                <x-svg.eye-icon fill="#767F8C" />
                                            </div>
                                        </div>
                                    @endif
                                </div>
                            </div>
                            @endif
                        @endforeach
                    </div>
                @else
                    <div class="col-md-12">
                        <div class="card text-center">
                            <x-not-found message="{{ __('no_data_found') }}" />
                        </div>
                    </div>
                @endif

                @if (request('perpage') != 'all' && $candidates->total() > $candidates->count())
                    <div class="rt-pt-30">
                        <div class="tw-flex tw-flex-col tw-items-center">
                            <div class="tw-flex tw-justify-end tw-w-full tw-mb-2">
                                <p class="tw-text-sm tw-text-[#767F8C]">
                                    Menampilkan {{ $candidates->count() }} pencaker dari {{ $candidates->total() }}
                                </p>
                            </div>
                            <div id="pagination-container" class="tw-flex tw-justify-center">
                                <nav>
                                    {{ $candidates->links('vendor.pagination.frontend') }}
                                </nav>
                            </div>
                        </div>
                    </div>
                @endif
            </div>
        </div>

    </div>
</div>
