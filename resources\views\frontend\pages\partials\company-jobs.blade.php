@forelse ($jobs as $job)
    <div class="col-lg-4 col-md-6 tw-mb-6">
        <div class="tw-bg-white tw-rounded-lg tw-shadow-sm tw-overflow-hidden tw-h-full tw-transition-all tw-duration-300 hover:tw-shadow-md">
            <div class="tw-p-6">
                <!-- Job Header -->
                <div class="tw-flex tw-items-start tw-justify-between tw-mb-4">
                    <div class="tw-flex tw-items-center tw-gap-3">
                        <div class="tw-w-12 tw-h-12 tw-rounded-full tw-overflow-hidden tw-flex-shrink-0">
                            <img src="{{ $job->company->logo_url }}" alt="logo" draggable="false" 
                                 class="tw-w-full tw-h-full tw-object-cover">
                        </div>
                        <div>
                            <h3 class="tw-text-base tw-font-medium tw-text-gray-900 tw-mb-1">
                                <a href="{{ route('website.job.details', $job->slug) }}" class="hover:tw-text-primary-500">
                                    {{ \Illuminate\Support\Str::limit($job->title, 40) }}
                                </a>
                            </h3>
                            <p class="tw-text-sm tw-text-gray-500">
                                {{ $job->company->user->name }}
                            </p>
                        </div>
                    </div>
                    @if ($job->featured)
                        <span class="tw-bg-red-100 tw-text-red-600 tw-text-xs tw-font-medium tw-px-2 tw-py-1 tw-rounded-full">
                            {{ __('Dibutuhkan Segera!') }}
                        </span>
                    @endif
                </div>
                
                <!-- Job Info -->
                <div class="tw-mb-4">
                    <div class="tw-flex tw-flex-wrap tw-gap-2 tw-mb-3">
                        @if ($job->job_type)
                            <span class="tw-bg-blue-100 tw-text-blue-600 tw-text-xs tw-font-medium tw-px-2.5 tw-py-0.5 tw-rounded-full">
                                {{ $job->job_type->name }}
                            </span>
                        @endif
                        
                        @if ($job->education)
                            <span class="tw-bg-purple-100 tw-text-purple-600 tw-text-xs tw-font-medium tw-px-2.5 tw-py-0.5 tw-rounded-full">
                                {{ $job->education->name }}
                            </span>
                        @endif
                        
                        @if ($job->disability_friendly)
                            <span class="tw-bg-green-100 tw-text-green-600 tw-text-xs tw-font-medium tw-px-2.5 tw-py-0.5 tw-rounded-full">
                                <i class="fas fa-wheelchair tw-mr-1"></i> Ramah Disabilitas
                            </span>
                        @endif
                    </div>
                    
                    <div class="tw-flex tw-items-center tw-text-sm tw-text-gray-500 tw-mb-1">
                        <i class="fas fa-map-marker-alt tw-mr-2"></i>
                        {{ $job->district }}
                    </div>
                    
                    <div class="tw-flex tw-items-center tw-text-sm tw-text-gray-500">
                        <i class="fas fa-money-bill-wave tw-mr-2"></i>
                        @if ($job->salary_mode == 'range')
                            {{ currencyAmountShort($job->min_salary) }} - {{ currencyAmountShort($job->max_salary) }} {{ currentCurrencyCode() }}
                        @else
                            {{ $job->custom_salary }}
                        @endif
                    </div>
                </div>
                
                <!-- Job Footer -->
                <div class="tw-flex tw-items-center tw-justify-between tw-mt-auto">
                    <span class="tw-text-xs tw-text-gray-500">
                        <i class="far fa-clock tw-mr-1"></i> {{ $job->deadline_active ? $job->days_remaining : 'Expired' }}
                    </span>
                    <a href="{{ route('website.job.details', $job->slug) }}" class="tw-text-primary-500 tw-text-sm tw-font-medium hover:tw-underline">
                        Lihat Detail <i class="fas fa-arrow-right tw-ml-1"></i>
                    </a>
                </div>
            </div>
        </div>
    </div>
@empty
    <div class="col-12">
        <div class="tw-bg-white tw-rounded-lg tw-shadow-sm tw-p-8 tw-text-center">
            <div class="tw-flex tw-flex-col tw-items-center tw-justify-center">
                <div class="tw-w-16 tw-h-16 tw-rounded-full tw-bg-gray-100 tw-flex tw-items-center tw-justify-center tw-mb-4">
                    <i class="fas fa-briefcase tw-text-gray-400 tw-text-2xl"></i>
                </div>
                <h3 class="tw-text-lg tw-font-medium tw-text-gray-900 tw-mb-2">{{ __('no_data_found') }}</h3>
                <p class="tw-text-gray-600">Saat ini tidak ada lowongan kerja yang tersedia di perusahaan ini.</p>
            </div>
        </div>
    </div>
@endforelse
