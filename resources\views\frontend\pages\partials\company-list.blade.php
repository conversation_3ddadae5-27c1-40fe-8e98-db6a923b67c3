@if ($companies->count() > 0)
    @foreach ($companies as $company)
        <div class="col-xl-4 col-md-6 fade-in-bottom condition_class rt-mb-24 tw-self-stretch">
            <a href="{{ route('website.employe.details', $company->user->username) }}"
                class="card jobcardStyle1 tw-relative tw-h-full">
                <div class="tw-p-6 !tw-pb-[72px]">
                    <div class="rt-single-icon-box tw-gap-3">
                        <div class="tw-w-14 tw-h-14 tw-rounded-full tw-overflow-hidden">
                            <img class="tw-w-full tw-h-full tw-object-cover"
                                src="{{ $company->logo_url ? $company->logo_url : asset('backend/image/default.png') }}"
                                alt="Logo {{ $company->user->name }}" draggable="false">
                        </div>
                        <div class="iconbox-content">
                            <div class="">
                                <span class="tw-text-[#191F33] tw-text-lg tw-font-medium tw-inline-block">
                                    {{ formatCompanyName($company) }}
                                </span>
                            </div>
                            @isset($company->country)
                                <span class="loacton text-gray-400 ">
                                    <i class="ph-map-pin"></i>
                                    {{ $company->district }}
                                </span>
                            @endisset
                        </div>
                    </div>
                    <div class="post-info">
                        <div class="tw-flex tw-flex-wrap tw-gap-3">
                            <span
                                class="tw-px-3 tw-py-1 tw-inline-block tw-text-sm tw-font-medium tw-text-[#474C54] tw-rounded-[52px] ll-gray-border">{{ $company?->industry?->name ?? '' }}</span>

                            @if ($company->activejobs !== 0)
                                <span
                                    class="tw-px-3 tw-py-1 tw-inline-block tw-text-sm tw-font-medium tw-text-[#474C54] tw-rounded-[52px] ll-gray-border">{{ $company->activejobs }}
                                    {{ __('open_job') }}</span>
                            @endif

                        </div>
                        <div
                            class="tw-absolute tw-bottom-6 tw-left-6 tw-text-base tw-font-semibold tw-capitalize tw-inline-flex tw-items-center tw-gap-1">
                            <span>{{ __('view_profile') }}</span>
                            <i class="ph-bold ph-arrow-right"></i>
                        </div>
                    </div>
                </div>
            </a>
        </div>
    @endforeach
@else
    <div class="col-md-12">
        <div class="card text-center">
            <x-not-found message="{{ __('no_data_found') }}" />
        </div>
    </div>
@endif
