<div class="message-area d-flex flex-column" style="height: 964px; margin-top: 20px;">
    @if(!$selectedThread)
        <!-- Loading State -->
        <div class="loading-state d-flex justify-content-center pt-5">
            <div class="spinner-border text-primary" role="status" style="width: 3rem; height: 3rem;">
                <span class="visually-hidden">Loading...</span>
            </div>
        </div>
    @else
        <!-- Forum Thread Header -->
        <div class="forum-thread-header p-3 border-bottom">
            <div class="thread-title-area mb-3">
                <div class="d-flex justify-content-between align-items-start mb-2">
                    <h4 class="thread-title mb-1">
                        @if($thread->ticket_number)
                            <span class="ticket-number text-muted me-2">[{{ $thread->ticket_number }}]</span>
                        @endif
                        {{ $thread->subject }}
                    </h4>

                    <div class="ticket-badges">
                        @if($thread->ticket_number)
                            <div class="d-flex gap-2">
                                {!! $thread->status_badge !!}
                            </div>
                        @endif
                    </div>
                </div>

                <div class="thread-meta d-flex align-items-center flex-wrap">
                    <span class="thread-date me-3">
                        <i class="fas fa-calendar-alt me-1"></i> {{ $thread->created_at->format('d M Y, H:i') }}
                    </span>

                    @if($thread->job)
                        <span class="thread-job me-3">
                            <i class="fas fa-briefcase me-1"></i>
                            <a href="{{ route('website.job.details', $thread->job->slug) }}" class="text-primary" target="_blank">
                                {{ $thread->job->title }}
                                <i class="fas fa-external-link-alt fa-xs"></i>
                            </a>
                        </span>
                    @endif

                    <span class="thread-replies">
                        <i class="fas fa-comment-alt me-1"></i> {{ $thread->messages->count() }} {{ __('balasan') }}
                    </span>
                </div>

                @if($thread->ticket_number && (auth()->user()->role == 'admin' || $thread->status != 'closed'))
                    <div class="ticket-actions mt-3">
                        <div class="d-flex flex-wrap gap-2">
                            <!-- Status Dropdown -->
                            <div class="dropdown">
                                <button class="btn btn-sm btn-outline-secondary dropdown-toggle" type="button" id="statusDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                                    <i class="fas fa-tasks me-1"></i> {{ __('Status') }}
                                </button>
                                <ul class="dropdown-menu" aria-labelledby="statusDropdown">
                                    <li><a class="dropdown-item {{ $thread->status == 'open' ? 'active' : '' }}" href="#" wire:click.prevent="updateTicketStatus('open')">{{ __('Buka') }}</a></li>
                                    <li><a class="dropdown-item {{ $thread->status == 'pending' ? 'active' : '' }}" href="#" wire:click.prevent="updateTicketStatus('pending')">{{ __('Tertunda') }}</a></li>
                                    <li><a class="dropdown-item {{ $thread->status == 'closed' ? 'active' : '' }}" href="#" wire:click.prevent="updateTicketStatus('closed')">{{ __('Tutup') }}</a></li>
                                </ul>
                            </div>
                        </div>
                    </div>
                @endif
            </div>

            <div class="thread-starter-info d-flex align-items-center">
                <div class="starter-avatar me-2">
                    @if(auth()->user()->role == 'company')
                        @if($thread->is_admin_thread)
                            <img src="{{ asset('backend/image/admin-avatar.png') }}" alt="Admin" class="rounded-circle" width="48" height="48">
                        @else
                            <img src="{{ $thread->candidate && $thread->candidate->photo ? str_replace('/storage/', '/uploads/', $thread->candidate->photo) : asset('backend/image/default.png') }}" alt="User" class="rounded-circle" width="48" height="48">
                        @endif
                    @elseif(auth()->user()->role == 'candidate')
                        @if($thread->is_admin_thread)
                            <img src="{{ asset('backend/image/admin-avatar.png') }}" alt="Admin" class="rounded-circle" width="48" height="48">
                        @else
                            <img src="{{ $thread->company && $thread->company->logo ? str_replace('/company/uploads/', '/uploads/', $thread->company->logo) : asset('backend/image/default.png') }}" alt="User" class="rounded-circle" width="48" height="48">
                        @endif
                    @else
                        @if($thread->is_admin_thread)
                            <img src="{{ asset('backend/image/admin-avatar.png') }}" alt="Admin" class="rounded-circle" width="48" height="48">
                        @elseif($thread->company)
                            <img src="{{ $thread->company->logo ? str_replace('/company/uploads/', '/uploads/', $thread->company->logo) : asset('backend/image/default.png') }}" alt="Company" class="rounded-circle" width="48" height="48">
                        @elseif($thread->candidate)
                            <img src="{{ $thread->candidate->photo ? str_replace('/storage/', '/uploads/', $thread->candidate->photo) : asset('backend/image/default.png') }}" alt="Candidate" class="rounded-circle" width="48" height="48">
                        @else
                            <img src="{{ asset('backend/image/default.png') }}" alt="User" class="rounded-circle" width="48" height="48">
                        @endif
                    @endif
                </div>
                <div class="starter-info">
                    <div class="starter-name fw-bold">
                        @if(auth()->user()->role == 'company')
                            @if($thread->is_admin_thread)
                                <span title="Administrator">Admin</span>
                            @else
                                <span title="{{ $thread->candidate && $thread->candidate->user ? $thread->candidate->user->name : __('Pencaker') }}">
                                    {{ $thread->candidate && $thread->candidate->user ? \Illuminate\Support\Str::limit($thread->candidate->user->name, 15) : __('Pencaker') }}
                                </span>
                            @endif
                        @elseif(auth()->user()->role == 'candidate')
                            @if($thread->is_admin_thread)
                                <span title="Administrator">Admin</span>
                            @else
                                @php
                                    $companyName = $thread->company && $thread->company->user ? $thread->company->user->name : __('Perusahaan');
                                    $legalEntity = $thread->company && $thread->company->legal_entity ? $thread->company->legal_entity : '';
                                    $fullName = $legalEntity ? "$legalEntity $companyName" : $companyName;
                                @endphp
                                <span title="{{ $fullName }}">
                                    @if($legalEntity)
                                        <small class="text-muted">{{ $legalEntity }}</small>
                                    @endif
                                    {{ \Illuminate\Support\Str::limit($companyName, 15) }}
                                </span>
                            @endif
                        @else
                            @if($thread->is_admin_thread)
                                <span title="Administrator">Admin</span>
                            @elseif($thread->company)
                                @php
                                    $companyName = $thread->company->user ? $thread->company->user->name : __('Perusahaan');
                                    $legalEntity = $thread->company->legal_entity ?? '';
                                    $fullName = $legalEntity ? "$legalEntity $companyName" : $companyName;
                                @endphp
                                <span title="{{ $fullName }}">
                                    @if($legalEntity)
                                        <small class="text-muted">{{ $legalEntity }}</small>
                                    @endif
                                    {{ \Illuminate\Support\Str::limit($companyName, 15) }}
                                </span>
                            @elseif($thread->candidate)
                                <span title="{{ $thread->candidate->user ? $thread->candidate->user->name : __('Pencaker') }}">
                                    {{ $thread->candidate->user ? \Illuminate\Support\Str::limit($thread->candidate->user->name, 15) : __('Pencaker') }}
                                </span>
                            @else
                                {{ __('Pengguna Tidak Dikenal') }}
                            @endif
                        @endif
                    </div>
                    <div class="starter-role text-muted small">
                        @if(auth()->user()->role == 'company')
                            @if($thread->is_admin_thread)
                                {{ __('Administrator') }}
                            @else
                                {{ __('Pencari Kerja') }}
                            @endif
                        @elseif(auth()->user()->role == 'candidate')
                            @if($thread->is_admin_thread)
                                {{ __('Administrator') }}
                            @else
                                {{ __('Perusahaan') }}
                            @endif
                        @else
                            {{ $thread->is_admin_thread ? __('Administrator') : ($thread->company ? __('Perusahaan') : __('Pencari Kerja')) }}
                        @endif
                    </div>
                </div>
            </div>
        </div>

        <!-- Chat Messages Content -->
        <div class="chat-messages-content p-3" style="flex: 1; overflow-y: auto;">
            @foreach($this->groupedMessages as $date => $messages)
                <div class="day-separator">
                    <span>{{ $this->formatDate($date) }}</span>
                </div>
                @foreach($messages as $message)
                    <div class="message-item mb-4 {{ $message->sender_id == auth()->id() ? 'sender' : 'receiver' }}">
                        <div class="d-flex {{ $message->sender_id == auth()->id() ? 'justify-content-end' : 'justify-content-start' }}">
                            @if($message->sender_id != auth()->id())
                                <div class="avatar me-2">
                                    @if($message->sender && $message->sender->role == 'admin')
                                        <img src="{{ asset('backend/image/admin-avatar.png') }}" alt="Admin" class="rounded-circle" width="40" height="40">
                                    @elseif($message->sender && $message->sender->role == 'company' && $message->sender->company)
                                        <img src="{{ $message->sender->company->logo ? str_replace('/company/uploads/', '/uploads/', $message->sender->company->logo) : asset('backend/image/default.png') }}" alt="Company" class="rounded-circle" width="40" height="40">
                                    @elseif($message->sender && $message->sender->role == 'candidate' && $message->sender->candidate)
                                        <img src="{{ $message->sender->candidate->photo ? str_replace('/storage/', '/uploads/', $message->sender->candidate->photo) : asset('backend/image/default.png') }}" alt="Candidate" class="rounded-circle" width="40" height="40">
                                    @else
                                        <img src="{{ asset('backend/image/default.png') }}" alt="User" class="rounded-circle" width="40" height="40">
                                    @endif
                                </div>
                            @endif

                            <div class="message-content {{ $message->sender_id == auth()->id() ? 'bg-light-success' : 'bg-light' }}">
                                <small class="text-muted fst-italic d-block mb-1">{{ $message->created_at->format('d/m/Y - H:i') }}</small>
                                <div class="message-header d-flex justify-content-between align-items-center mb-2">
                                    <span class="fw-bold">
                                        @if($message->sender && $message->sender->role == 'admin')
                                            Admin
                                        @elseif($message->sender && $message->sender->role == 'company' && $message->sender->company)
                                            @php
                                                $companyName = $message->sender->name;
                                                $legalEntity = $message->sender->company->legal_entity ?? '';
                                                $fullName = $legalEntity ? "$legalEntity $companyName" : $companyName;
                                            @endphp
                                            {{ $fullName }}
                                        @elseif($message->sender && $message->sender->role == 'candidate' && $message->sender->candidate)
                                            {{ $message->sender->name }}
                                        @else
                                            {{ __('Pengguna Tidak Dikenal') }}
                                        @endif
                                    </span>
                                </div>
                                <div class="message-text">
                                    {!! nl2br(e($message->body)) !!}
                                </div>
                                @if($message->attachment)
                                    <div class="message-attachments mt-2">
                                        @if(isset($message->attachment['type']) && $message->attachment['type'] == 'image')
                                            <div class="image-attachment">
                                                <a href="{{ $message->attachment['url'] }}" target="_blank" class="d-block">
                                                    <img src="{{ $message->attachment['url'] }}" alt="Attachment" class="img-fluid rounded" style="max-height: 200px;">
                                                </a>
                                            </div>
                                        @else
                                            <div class="file-attachment p-2 bg-white rounded border">
                                                <a href="{{ $message->attachment['url'] ?? '#' }}" target="_blank" class="d-flex align-items-center text-decoration-none">
                                                    <div class="attachment-icon me-2">
                                                        <i class="fas fa-file-{{ isset($message->attachment['type']) && $message->attachment['type'] == 'document' ? 'pdf' : 'alt' }} text-primary"></i>
                                                    </div>
                                                    <div class="attachment-info">
                                                        <div class="attachment-name">{{ $message->attachment['name'] ?? 'File' }}</div>
                                                        @if(isset($message->attachment['size']))
                                                            <div class="attachment-size text-muted small">{{ round($message->attachment['size'] / 1024) }} KB</div>
                                                        @endif
                                                    </div>
                                                </a>
                                            </div>
                                        @endif
                                    </div>
                                @endif
                                <div class="message-status mt-1 text-end">
                                    @if($message->sender_id == auth()->id())
                                        <small class="text-muted">
                                            @if($message->read)
                                                <i class="fas fa-check-double text-primary"></i> {{ __('Dibaca') }}
                                            @else
                                                <i class="fas fa-check"></i> {{ __('Terkirim') }}
                                            @endif
                                        </small>
                                    @endif
                                </div>
                            </div>

                            @if($message->sender_id == auth()->id())
                                <div class="avatar ms-2">
                                    @if($message->sender && $message->sender->role == 'admin')
                                        <img src="{{ asset('backend/image/admin-avatar.png') }}" alt="Admin" class="rounded-circle" width="40" height="40">
                                    @elseif($message->sender && $message->sender->role == 'company' && $message->sender->company)
                                        <img src="{{ $message->sender->company->logo ? str_replace('/company/uploads/', '/uploads/', $message->sender->company->logo) : asset('backend/image/default.png') }}" alt="Company" class="rounded-circle" width="40" height="40">
                                    @elseif($message->sender && $message->sender->role == 'candidate' && $message->sender->candidate)
                                        <img src="{{ $message->sender->candidate->photo ? str_replace('/storage/', '/uploads/', $message->sender->candidate->photo) : asset('backend/image/default.png') }}" alt="Candidate" class="rounded-circle" width="40" height="40">
                                    @else
                                        <img src="{{ asset('backend/image/default.png') }}" alt="User" class="rounded-circle" width="40" height="40">
                                    @endif
                                </div>
                            @endif
                        </div>
                    </div>
                @endforeach
            @endforeach
        </div>
        @endif
</div>

<style>
    /* Forum Thread Header */
    .forum-thread-header {
        background-color: #f8f9fa;
        border-radius: 4px 4px 0 0;
    }

    .thread-title {
        color: #212529;
        font-weight: 600;
    }

    .ticket-number {
        font-size: 0.9em;
        font-weight: normal;
        color: #6c757d;
    }

    .ticket-badges .badge {
        font-size: 0.75rem;
        padding: 0.35em 0.65em;
    }

    .ticket-actions .btn {
        font-size: 0.8rem;
        padding: 0.25rem 0.5rem;
    }

    .ticket-actions .dropdown-menu {
        font-size: 0.875rem;
    }

    .ticket-actions .dropdown-item.active {
        background-color: #e9ecef;
        color: #212529;
        font-weight: 500;
    }

    .thread-meta {
        font-size: 13px;
        color: #6c757d;
    }

    .thread-date, .thread-job, .thread-replies {
        display: flex;
        align-items: center;
    }

    .thread-starter-info {
        background-color: #f0f7f4;
        padding: 10px;
        border-radius: 4px;
    }

    .starter-role {
        font-size: 12px;
    }

    /* Chat Messages */
    .day-separator {
        text-align: center;
        margin: 20px 0;
        position: relative;
    }

    .day-separator:before {
        content: '';
        position: absolute;
        top: 50%;
        left: 0;
        right: 0;
        height: 1px;
        background-color: #e9ecef;
        z-index: 1;
    }

    .day-separator span {
        background-color: #fff;
        padding: 0 10px;
        position: relative;
        z-index: 2;
        font-size: 12px;
        color: #6c757d;
    }

    /* Message Bubbles */
    .message-item {
        margin-bottom: 20px;
    }

    .message-content {
        max-width: 75%;
        border-radius: 10px;
        padding: 10px 15px;
        box-shadow: 0 1px 2px rgba(0,0,0,0.1);
        position: relative;
    }

    .sender .message-content {
        background-color: #e3f2fd !important; /* Light blue for sender */
        border: 1px solid #bbdefb;
    }

    .receiver .message-content {
        background-color: #ffffff !important; /* White for receiver */
        border: 1px solid #e0e0e0;
    }

    .message-header {
        margin-bottom: 5px;
    }

    .message-text {
        font-size: 14px;
        line-height: 1.5;
        word-break: break-word;
    }

    .message-attachments {
        margin-top: 8px;
    }

    .file-attachment {
        background-color: #f5f5f5;
        border-radius: 4px;
        padding: 8px;
    }

    .avatar img {
        border: 1px solid #e0e0e0;
        object-fit: cover;
    }

    /* Responsive */
    @media (max-width: 767.98px) {
        .message-content {
            max-width: 85%;
        }
    }
</style>
