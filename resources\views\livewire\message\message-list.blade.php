<div class="message-container">
    <div class="message-toolbar">
        <div class="d-flex justify-content-between align-items-center w-100">
            <div class="d-flex align-items-center">
                <div class="input-group input-group-sm me-2">
                    <input type="text" class="form-control" placeholder="{{ __('Cari pesan...') }}" wire:model.debounce.500ms="searchTerm">
                    <button class="btn btn-outline-secondary" type="button" wire:click="searchMessages">
                        <i class="ph-magnifying-glass"></i>
                    </button>
                </div>

                <div class="btn-group btn-group-sm me-2">
                    <button class="btn {{ $filterType == 'all' ? 'btn-primary' : 'btn-outline-secondary' }}" wire:click="filterByType('all')">
                        {{ __('Semua') }}
                    </button>
                    @if(auth()->user()->role == 'admin')
                    <button class="btn {{ $filterType == 'company' ? 'btn-primary' : 'btn-outline-secondary' }}" wire:click="filterByType('company')">
                        {{ __('Perusahaan') }}
                    </button>
                    <button class="btn {{ $filterType == 'candidate' ? 'btn-primary' : 'btn-outline-secondary' }}" wire:click="filterByType('candidate')">
                        {{ __('Pencaker') }}
                    </button>
                    @endif
                </div>
            </div>

            <div class="d-flex align-items-center">
                <div class="form-group mb-0 me-2">
                    <select class="form-select form-select-sm" wire:model="perPage" wire:change="changePerPage($event.target.value)">
                        <option value="3">3 {{ __('per halaman') }}</option>
                        <option value="10">10 {{ __('per halaman') }}</option>
                        <option value="25">25 {{ __('per halaman') }}</option>
                        <option value="50">50 {{ __('per halaman') }}</option>
                    </select>
                </div>

                @if(auth()->user()->role == 'company' && count($jobs) > 0)
                    <div class="form-group mb-0">
                        <select class="form-select form-select-sm" wire:model="jobFilter" wire:change="filterByJob($event.target.value)">
                            <option value="">{{ __('Semua Lowongan') }}</option>
                            @foreach($jobs as $job)
                                <option value="{{ $job->id }}">{{ \Illuminate\Support\Str::limit($job->title, 30) }}</option>
                            @endforeach
                        </select>
                    </div>
                @endif
            </div>
        </div>
    </div>

    <div class="message-list-content">
        <div wire:loading wire:target="refreshList, toggleUnreadFilter, filterByJob, changePerPage, filterByType, searchMessages" class="text-center p-4">
            <div class="d-flex justify-content-center">
                <div class="ph-briefcase loading-icon"></div>
            </div>
            <p class="mt-2">{{ __('Memuat pesan...') }}</p>
        </div>

        <div wire:loading.remove wire:target="refreshList, toggleUnreadFilter, filterByJob, changePerPage, filterByType, searchMessages">
            @if(count($threads) > 0)
                <table class="table table-hover message-table">
                    <thead>
                        <tr>
                            <th width="5%">#</th>
                            <th width="20%">{{ __('Pengirim') }}</th>
                            <th width="20%">{{ __('Penerima') }}</th>
                            <th width="30%">{{ __('Subjek') }}</th>
                            <th width="10%">{{ __('Status') }}</th>
                            <th width="15%">{{ __('Tanggal') }}</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach($threads as $thread)
                            <tr class="{{ $thread->unreadMessages && $thread->unreadMessages->count() > 0 ? 'unread' : '' }}"
                                wire:click="selectThread({{ $thread->id }})" style="cursor: pointer;">
                                <td>
                                    @if($thread->unreadMessages && $thread->unreadMessages->count() > 0)
                                        <span class="badge rounded-pill bg-danger">
                                            {{ $thread->unreadMessages->count() }}
                                        </span>
                                    @else
                                        <i class="ph-envelope-open text-muted"></i>
                                    @endif
                                </td>
                                <td>
                                    <div class="d-flex align-items-center">
                                        <div class="avatar-wrapper me-2">
                                            @if($thread->is_admin_thread)
                                                <img src="{{ asset('backend/image/admin-avatar.png') }}" alt="Admin" class="avatar">
                                            @elseif($thread->company)
                                                <img src="{{ $thread->company->logo ? str_replace('/company/uploads/', '/uploads/', $thread->company->logo) : asset('backend/image/default.png') }}" alt="Company" class="avatar">
                                            @elseif($thread->candidate)
                                                <img src="{{ $thread->candidate->photo ? str_replace('/public/storage/', '/uploads/', str_replace('/storage/', '/uploads/', $thread->candidate->photo)) : asset('backend/image/default.png') }}" alt="Candidate" class="avatar">
                                            @else
                                                <img src="{{ asset('backend/image/default.png') }}" alt="User" class="avatar">
                                            @endif
                                        </div>
                                        <div class="{{ $thread->unreadMessages && $thread->unreadMessages->count() > 0 ? 'fw-bold' : '' }}">
                                            @if($thread->is_admin_thread)
                                                <span title="Administrator">Admin</span>
                                            @elseif($thread->company)
                                                @php
                                                    $companyName = $thread->company->user ? $thread->company->user->name : __('Perusahaan');
                                                    $legalEntity = $thread->company->legal_entity ?? '';
                                                    $fullName = $legalEntity ? "$legalEntity $companyName" : $companyName;
                                                @endphp
                                                <span title="{{ $fullName }}">
                                                    @if($legalEntity)
                                                        <small class="text-muted">{{ $legalEntity }}</small>
                                                    @endif
                                                    {{ \Illuminate\Support\Str::limit($companyName, 15) }}
                                                </span>
                                            @elseif($thread->candidate)
                                                <span title="{{ $thread->candidate->user ? $thread->candidate->user->name : __('Pencaker') }}">
                                                    {{ $thread->candidate->user ? \Illuminate\Support\Str::limit($thread->candidate->user->name, 15) : __('Pencaker') }}
                                                </span>
                                            @else
                                                {{ __('Pengguna Tidak Dikenal') }}
                                            @endif
                                        </div>
                                    </div>
                                </td>
                                <td>
                                    <div class="d-flex align-items-center">
                                        <div class="avatar-wrapper me-2">
                                            @if($thread->is_admin_thread)
                                                @if($thread->company)
                                                    <img src="{{ $thread->company->logo ? str_replace('/company/uploads/', '/uploads/', $thread->company->logo) : asset('backend/image/default.png') }}" alt="Company" class="avatar">
                                                @elseif($thread->candidate)
                                                    <img src="{{ $thread->candidate->photo ? str_replace('/public/storage/', '/uploads/', str_replace('/storage/', '/uploads/', $thread->candidate->photo)) : asset('backend/image/default.png') }}" alt="Candidate" class="avatar">
                                                @else
                                                    <img src="{{ asset('backend/image/default.png') }}" alt="User" class="avatar">
                                                @endif
                                            @else
                                                <img src="{{ asset('backend/image/admin-avatar.png') }}" alt="Admin" class="avatar">
                                            @endif
                                        </div>
                                        <div>
                                            @if($thread->is_admin_thread)
                                                @if($thread->company)
                                                    @php
                                                        $companyName = $thread->company->user ? $thread->company->user->name : __('Perusahaan');
                                                        $legalEntity = $thread->company->legal_entity ?? '';
                                                        $fullName = $legalEntity ? "$legalEntity $companyName" : $companyName;
                                                    @endphp
                                                    <span title="{{ $fullName }}">
                                                        @if($legalEntity)
                                                            <small class="text-muted">{{ $legalEntity }}</small>
                                                        @endif
                                                        {{ \Illuminate\Support\Str::limit($companyName, 15) }}
                                                    </span>
                                                @elseif($thread->candidate)
                                                    <span title="{{ $thread->candidate->user ? $thread->candidate->user->name : __('Pencaker') }}">
                                                        {{ $thread->candidate->user ? \Illuminate\Support\Str::limit($thread->candidate->user->name, 15) : __('Pencaker') }}
                                                    </span>
                                                @else
                                                    {{ __('Pengguna Tidak Dikenal') }}
                                                @endif
                                            @else
                                                <span title="Administrator">Admin</span>
                                            @endif
                                        </div>
                                    </div>
                                </td>
                                <td>
                                    <div class="message-title {{ $thread->unreadMessages && $thread->unreadMessages->count() > 0 ? 'fw-bold' : '' }}">
                                        @if($thread->ticket_number)
                                            <span class="ticket-number">[{{ $thread->ticket_number }}]</span>
                                        @endif
                                        {{ \Illuminate\Support\Str::limit($thread->subject, 30) }}
                                    </div>
                                    @if($thread->job)
                                        <div class="message-job">
                                            <i class="fas fa-briefcase me-1"></i> {{ \Illuminate\Support\Str::limit($thread->job->title, 30) }}
                                        </div>
                                    @endif
                                </td>
                                <td>
                                    @if($thread->ticket_number)
                                        <div class="ticket-badges">
                                            {!! $thread->status_badge !!}
                                            <div class="mt-1">{!! $thread->priority_badge !!}</div>
                                        </div>
                                    @else
                                        <span class="badge bg-secondary">{{ __('Pesan') }}</span>
                                    @endif
                                </td>
                                <td>
                                    <div class="message-date">
                                        {{ $thread->latestMessage ? $thread->latestMessage->created_at->format('d M Y') : $thread->created_at->format('d M Y') }}
                                        <div class="text-muted">
                                            {{ $thread->latestMessage ? $thread->latestMessage->created_at->format('H:i') : $thread->created_at->format('H:i') }}
                                        </div>
                                    </div>
                                </td>
                            </tr>
                        @endforeach
                    </tbody>
                </table>

                <!-- Pagination -->
                @if($totalPages > 1)
                    <div class="d-flex justify-content-center mt-4 pagination-container">
                        <nav>
                            <ul class="pagination">
                                <li class="page-item {{ $currentPage == 1 ? 'disabled' : '' }}">
                                    <a class="page-link" href="javascript:void(0);" data-page="{{ $currentPage - 1 }}">
                                        <i class="fas fa-chevron-left"></i>
                                    </a>
                                </li>

                                @php
                                    $startPage = max(1, $currentPage - 2);
                                    $endPage = min($totalPages, $startPage + 4);
                                    if ($endPage - $startPage < 4 && $startPage > 1) {
                                        $startPage = max(1, $endPage - 4);
                                    }
                                @endphp

                                @if($startPage > 1)
                                    <li class="page-item">
                                        <a class="page-link" href="javascript:void(0);" data-page="1">1</a>
                                    </li>
                                    @if($startPage > 2)
                                        <li class="page-item disabled">
                                            <span class="page-link">...</span>
                                        </li>
                                    @endif
                                @endif

                                @for($i = $startPage; $i <= $endPage; $i++)
                                    <li class="page-item {{ $currentPage == $i ? 'active' : '' }}">
                                        <a class="page-link" href="javascript:void(0);" data-page="{{ $i }}">{{ $i }}</a>
                                    </li>
                                @endfor

                                @if($endPage < $totalPages)
                                    @if($endPage < $totalPages - 1)
                                        <li class="page-item disabled">
                                            <span class="page-link">...</span>
                                        </li>
                                    @endif
                                    <li class="page-item">
                                        <a class="page-link" href="javascript:void(0);" data-page="{{ $totalPages }}">{{ $totalPages }}</a>
                                    </li>
                                @endif

                                <li class="page-item {{ $currentPage == $totalPages ? 'disabled' : '' }}">
                                    <a class="page-link" href="javascript:void(0);" data-page="{{ $currentPage + 1 }}">
                                        <i class="fas fa-chevron-right"></i>
                                    </a>
                                </li>
                            </ul>
                        </nav>
                    </div>

                    <div class="text-center mt-2 text-muted small">
                        {{ __('Menampilkan') }} {{ ($currentPage - 1) * $perPage + 1 }} - {{ min($currentPage * $perPage, $totalThreads) }} {{ __('dari') }} {{ $totalThreads }} {{ __('pesan') }}
                    </div>
                @endif
            @else
                <div class="text-center py-5">
                    <div class="mb-3">
                        <svg xmlns="http://www.w3.org/2000/svg" width="48" height="48" fill="#9CA3AF" viewBox="0 0 16 16">
                            <path d="M8 15c4.418 0 8-3.134 8-7s-3.582-7-8-7-8 3.134-8 7c0 1.76.743 3.37 1.97 4.6-.097 1.016-.417 2.13-.771 2.966-.079.186.074.394.273.362 2.256-.37 3.597-.938 4.18-1.234A9.06 9.06 0 0 0 8 15z"/>
                        </svg>
                    </div>
                    <h6>{{ __('Tidak Ada Pesan') }}</h6>
                    <p class="text-muted">{{ __('Tidak ada pesan yang sesuai dengan kriteria pencarian') }}</p>
                </div>
            @endif
        </div>
    </div>
</div>

<style>
    .message-container {
        background-color: #ffffff;
        border-radius: 8px;
        overflow: hidden;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    }

    .message-toolbar {
        background-color: #f8f9fa;
        border-bottom: 1px solid #e9ecef;
        padding: 10px 15px;
    }

    .message-list-content {
        max-height: 700px;
        overflow-y: auto;
        padding-top: 0;
    }

    .message-table {
        margin-bottom: 0;
    }

    .message-table th {
        background-color: #f8f9fa;
        font-weight: 600;
        border-top: none;
    }

    .message-table td {
        vertical-align: middle;
        padding: 0.75rem;
        border-bottom: 1px solid #f0f0f0;
    }

    .message-table tr {
        transition: background-color 0.2s;
    }

    .message-table tr:hover {
        background-color: #f8f9fa;
    }

    tr.unread {
        background-color: #f0f7ff;
    }

    tr.unread:hover {
        background-color: #e6f0fa;
    }

    .ticket-number {
        color: #6c757d;
        font-size: 0.9em;
        margin-right: 4px;
    }

    .ticket-badges .badge {
        font-size: 0.7rem;
        padding: 0.25em 0.5em;
    }

    .avatar-wrapper {
        position: relative;
    }

    .avatar {
        width: 36px;
        height: 36px;
        border-radius: 50%;
        object-fit: cover;
    }

    .replies-count {
        font-size: 12px;
        color: #6c757d;
    }

    @media (max-width: 767.98px) {
        .message-info {
            max-width: 200px;
        }

        .message-table th:nth-child(2),
        .message-table td:nth-child(2),
        .message-table th:nth-child(3),
        .message-table td:nth-child(3) {
            display: none;
        }

        .message-table th:nth-child(4),
        .message-table td:nth-child(4) {
            width: 60% !important;
        }
    }
</style>
