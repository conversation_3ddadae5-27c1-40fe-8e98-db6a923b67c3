<div class="message-reply p-3 bg-light border-top {{ !$threadId ? 'd-none' : '' }}">
    @if($canReply)
        <form wire:submit.prevent="sendMessage" class="d-flex align-items-center">
            <!-- Attachment Button -->
            <div class="dropdown me-2">
                <button class="btn btn-light rounded-circle" type="button" id="attachment-button" data-bs-toggle="dropdown" aria-expanded="false">
                    <i class="fas fa-paperclip"></i>
                </button>
                <div class="dropdown-menu p-2" aria-labelledby="attachment-button">
                    <div class="d-flex flex-column">
                        <label for="file-upload" class="dropdown-item cursor-pointer">
                            <i class="fas fa-file me-2"></i> Dokumen
                        </label>
                        <label for="image-upload" class="dropdown-item cursor-pointer">
                            <i class="fas fa-image me-2"></i> Gambar
                        </label>
                    </div>
                </div>
            </div>

            <!-- Hidden File Inputs -->
            <input type="file" id="file-upload" class="d-none" wire:model="attachment" accept=".pdf,.doc,.docx,.txt">
            <input type="file" id="image-upload" class="d-none" wire:model="attachment" accept="image/*">

            <!-- Message Input -->
            <div class="flex-grow-1 me-2">
                <textarea id="message-input" class="form-control" placeholder="{{ __('Tulis pesan Anda') }}" rows="1" wire:model.debounce.500ms="message"></textarea>
            </div>

            <!-- Send Button -->
            <div class="position-relative">
                <button type="submit" class="btn btn-primary" id="send-button">
                    <i class="fas fa-paper-plane"></i>
                </button>
                <div class="send-hint d-none d-md-block">Ctrl+Enter</div>
            </div>
        </form>

        <!-- Attachment Preview -->
        @if($attachment)
            <div class="attachment-preview mt-2 p-2 border rounded">
                @php
                    $extension = strtolower($attachment->getClientOriginalExtension());
                    $isImage = in_array($extension, ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'svg', 'webp']);
                @endphp

                @if($isImage)
                    <div class="text-center mb-2">
                        <img src="{{ $attachment->temporaryUrl() }}" alt="Preview" class="img-fluid rounded" style="max-height: 200px;">
                    </div>
                @endif

                <div class="d-flex align-items-center justify-content-between">
                    <div class="d-flex align-items-center">
                        @if($isImage)
                            <i class="fas fa-image me-2 text-primary"></i>
                        @elseif(in_array($extension, ['pdf']))
                            <i class="fas fa-file-pdf me-2 text-danger"></i>
                        @elseif(in_array($extension, ['doc', 'docx']))
                            <i class="fas fa-file-word me-2 text-primary"></i>
                        @elseif(in_array($extension, ['xls', 'xlsx']))
                            <i class="fas fa-file-excel me-2 text-success"></i>
                        @elseif(in_array($extension, ['ppt', 'pptx']))
                            <i class="fas fa-file-powerpoint me-2 text-warning"></i>
                        @else
                            <i class="fas fa-file me-2 text-secondary"></i>
                        @endif
                        <div>
                            <div class="fw-bold">{{ $attachment->getClientOriginalName() }}</div>
                            <div class="text-muted small">{{ round($attachment->getSize() / 1024) }} KB</div>
                        </div>
                    </div>
                    <button type="button" class="btn btn-sm btn-link text-danger" wire:click="$set('attachment', null)">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            </div>
        @endif
    @else
        <div class="alert alert-info mb-0">
            @if(auth()->user()->role == 'admin')
                <i class="fas fa-info-circle me-2"></i> Admin hanya dapat melihat percakapan antara perusahaan dan pencaker. Anda dapat mengirim pesan hanya jika Anda yang memulai percakapan ini atau sudah pernah mengirim pesan sebelumnya.
            @else
                <i class="fas fa-info-circle me-2"></i> Anda tidak dapat membalas pesan ini.
            @endif
        </div>
    @endif
</div>

<style>
    .cursor-pointer {
        cursor: pointer;
    }

    #message-input {
        resize: none;
        overflow: hidden;
        min-height: 38px;
        max-height: 120px;
    }

    .attachment-preview {
        background-color: #f8f9fa;
        border-radius: 8px;
        transition: all 0.2s ease;
    }

    .attachment-preview:hover {
        background-color: #f0f0f0;
    }

    .dropdown-item {
        display: flex;
        align-items: center;
        padding: 8px 12px;
        transition: background-color 0.2s;
    }

    .dropdown-item:hover {
        background-color: #f8f9fa;
    }

    .message-reply {
        border-top: 1px solid #e9ecef;
        background-color: #f8f9fa;
        border-radius: 0 0 8px 8px;
    }

    #send-button {
        border-radius: 50%;
        width: 40px;
        height: 40px;
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 0;
    }

    /* Hint for Ctrl+Enter */
    .send-hint {
        position: absolute;
        bottom: -20px;
        right: 0;
        font-size: 11px;
        color: #6c757d;
    }
</style>

@push('scripts')
<script>
    document.addEventListener('livewire:load', function () {
        // Auto-resize textarea
        const messageInput = document.getElementById('message-input');
        if (messageInput) {
            messageInput.addEventListener('input', function() {
                this.style.height = 'auto';
                this.style.height = (this.scrollHeight) + 'px';
            });

            // Add CTRL+Enter to send message
            messageInput.addEventListener('keydown', function(e) {
                if (e.key === 'Enter' && e.ctrlKey) {
                    e.preventDefault();
                    document.getElementById('send-button').click();
                }
            });
        }

        // Redirect file upload clicks
        const fileUpload = document.getElementById('file-upload');
        if (fileUpload) {
            fileUpload.addEventListener('click', function(e) {
                e.stopPropagation();
            });
        }

        const imageUpload = document.getElementById('image-upload');
        if (imageUpload) {
            imageUpload.addEventListener('click', function(e) {
                e.stopPropagation();
            });
        }

        // Add tooltip to send button
        const sendButton = document.getElementById('send-button');
        if (sendButton) {
            sendButton.setAttribute('title', 'Kirim pesan (Ctrl+Enter)');

            // Initialize tooltip if Bootstrap is available
            if (typeof bootstrap !== 'undefined' && bootstrap.Tooltip) {
                new bootstrap.Tooltip(sendButton);
            }
        }
    });
</script>
@endpush
