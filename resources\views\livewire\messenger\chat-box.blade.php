<div class="chat-area d-flex flex-column" style="height: 964px;">
    @if(!$selectedUser)
        <!-- Empty State -->
        <div class="chat-empty-state d-flex flex-column justify-content-center align-items-center h-100">
            <div class="text-center">
                <div class="mb-4">
                    <svg xmlns="http://www.w3.org/2000/svg" width="64" height="64" fill="#9CA3AF" viewBox="0 0 16 16">
                        <path d="M8 15c4.418 0 8-3.134 8-7s-3.582-7-8-7-8 3.134-8 7c0 1.76.743 3.37 1.97 4.6-.097 1.016-.417 2.13-.771 2.966-.079.186.074.394.273.362 2.256-.37 3.597-.938 4.18-1.234A9.06 9.06 0 0 0 8 15z"/>
                    </svg>
                </div>
                <h5>{{ __('you_dont_have_select_any_message_till_now') }}</h5>
                <p class="text-muted">{{ __('select_a_conversation_to_start_messaging') }}</p>
            </div>
        </div>
    @else
        <!-- Chat Header -->
        <div class="chat-header p-3 border-bottom">
            <div class="d-flex align-items-center">
                <div class="flex-shrink-0">
                    @if(auth()->user()->role == 'company')
                        <img src="{{ $selectedUser->candidate && $selectedUser->candidate->user ? $selectedUser->candidate->user->image_url : asset('backend/image/default.png') }}" alt="User" class="rounded-circle" width="40" height="40">
                    @else
                        <img src="{{ $selectedUser->company && $selectedUser->company->user ? $selectedUser->company->user->image_url : asset('backend/image/default.png') }}" alt="User" class="rounded-circle" width="40" height="40">
                    @endif
                </div>
                <div class="flex-grow-1 ms-3">
                    <h6 class="mb-0">
                        @if(auth()->user()->role == 'company')
                            {{ $selectedUser->candidate && $selectedUser->candidate->user ? $selectedUser->candidate->user->name : __('Unknown User') }}
                        @else
                            {{ $selectedUser->company && $selectedUser->company->user ? $selectedUser->company->user->name : __('Unknown User') }}
                        @endif
                    </h6>
                    <small class="text-muted">
                        @if($userTyping)
                            <span class="typing-indicator">{{ __('typing') }}...</span>
                        @else
                            {{ __('online') }}
                        @endif
                    </small>
                </div>
            </div>
        </div>

        <!-- Chat Messages -->
        <div class="chat-messages p-3" style="flex: 1; overflow-y: auto;">
            @foreach($this->groupedMessages as $date => $messages)
                <div class="day-separator">
                    <span>{{ $this->formatDate($date) }}</span>
                </div>

                @foreach($messages as $message)
                    <div class="message {{ $message->from == auth()->id() ? 'sent' : 'received' }}" data-id="{{ $message->id }}">
                        @if($message->attachment)
                            @if($message->attachment['type'] == 'image')
                                <div>
                                    <p>{{ $message->body }}</p>
                                    <img src="{{ $message->attachment['url'] }}" alt="Image" class="message-image">
                                </div>
                            @else
                                <div>
                                    <p>{{ $message->body }}</p>
                                    <div class="message-attachment">
                                        <i class="fas fa-file"></i>
                                        <a href="{{ $message->attachment['url'] }}" target="_blank">{{ $message->attachment['name'] }}</a>
                                    </div>
                                </div>
                            @endif
                        @else
                            <p>{{ $message->body }}</p>
                        @endif
                        <div class="message-time">{{ \Carbon\Carbon::parse($message->created_at)->format('H:i') }}</div>
                    </div>
                @endforeach
            @endforeach
        </div>
    @endif
</div>
