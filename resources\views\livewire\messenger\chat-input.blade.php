<div class="chat-input p-3 bg-light border-top {{ !$chatId ? 'd-none' : '' }}">
    <form wire:submit.prevent="sendMessage" class="d-flex align-items-center">
        <!-- Emoji Button -->
        <div class="dropdown me-2">
            <button class="btn btn-light rounded-circle" type="button" id="emoji-button" data-bs-toggle="dropdown" aria-expanded="false">
                <i class="fas fa-smile"></i>
            </button>
            <div class="dropdown-menu p-2 emoji-picker" aria-labelledby="emoji-button" style="width: 250px; height: 200px; overflow-y: auto;">
                <div class="d-flex flex-wrap">
                    @foreach(['😀', '😃', '😄', '😁', '😆', '😅', '😂', '🤣', '😊', '😇', '🙂', '🙃', '😉', '😌', '😍', '🥰', '😘', '😗', '😙', '😚', '😋', '😛', '😝', '😜', '🤪', '🤨', '🧐', '🤓', '😎', '🤩', '🥳', '😏', '😒', '😞', '😔', '😟', '😕', '🙁', '☹️', '😣', '😖', '😫', '😩', '🥺', '😢', '😭', '😤', '😠', '😡', '🤬', '🤯', '😳', '🥵', '🥶', '😱', '😨', '😰', '😥', '😓', '🤗', '🤔', '🤭', '🤫', '🤥', '😶', '😐', '😑', '😬', '🙄', '😯', '😦', '😧', '😮', '😲', '🥱', '😴', '🤤', '😪', '😵', '🤐', '🥴', '🤢', '🤮', '🤧', '😷', '🤒', '🤕'] as $emoji)
                        <div class="emoji-item p-1" style="cursor: pointer;" wire:click="addEmoji('{{ $emoji }}')">{{ $emoji }}</div>
                    @endforeach
                </div>
            </div>
        </div>

        <!-- Attachment Button -->
        <div class="dropdown me-2">
            <button class="btn btn-light rounded-circle" type="button" id="attachment-button" data-bs-toggle="dropdown" aria-expanded="false">
                <i class="fas fa-paperclip"></i>
            </button>
            <div class="dropdown-menu p-2" aria-labelledby="attachment-button">
                <label class="dropdown-item" for="file-upload">
                    <i class="fas fa-file me-2"></i> {{ __('document') }}
                </label>
                <label class="dropdown-item" for="image-upload">
                    <i class="fas fa-image me-2"></i> {{ __('image') }}
                </label>
            </div>
        </div>

        <!-- Hidden File Inputs -->
        <input type="file" id="file-upload" class="d-none" wire:model="attachment" accept=".pdf,.doc,.docx,.txt">
        <input type="file" id="image-upload" class="d-none" wire:model="attachment" accept="image/*">

        <!-- Message Input -->
        <div class="flex-grow-1 me-2">
            <textarea id="message-input" class="form-control" placeholder="{{ __('write_your_message') }}" rows="1" wire:model.debounce.500ms="message"></textarea>
        </div>

        <!-- Send Button -->
        <button type="submit" class="btn btn-primary" id="send-button" {{ empty($message) && !$attachment ? 'disabled' : '' }}>
            <i class="fas fa-paper-plane"></i>
        </button>
    </form>

    <!-- File Preview -->
    @if($attachment)
        <div class="mt-2 p-2 bg-white rounded border">
            <div class="d-flex align-items-center">
                <div class="me-2 flex-shrink-0">
                    @if(str_contains($attachment->getMimeType(), 'image'))
                        <img src="{{ $attachment->temporaryUrl() }}" alt="Preview" style="max-height: 40px; max-width: 40px;">
                    @else
                        <i class="fas fa-file fa-2x text-primary"></i>
                    @endif
                </div>
                <div class="flex-grow-1 min-w-0 me-2">
                    <p class="mb-0 small text-truncate" title="{{ $attachment->getClientOriginalName() }}">
                        {{ $attachment->getClientOriginalName() }}
                    </p>
                    <small class="text-muted">{{ round($attachment->getSize() / 1024) }} KB</small>
                </div>
                <button type="button" class="btn btn-sm btn-link text-danger flex-shrink-0" wire:click="$set('attachment', null)">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
    @endif
</div>

@push('styles')
<style>
/* Chat input responsive styles */
.chat-input {
    position: relative;
}

/* Mobile responsive adjustments */
@media (max-width: 768px) {
    .chat-input .d-flex {
        flex-wrap: nowrap;
        gap: 0.25rem;
    }

    .chat-input .btn {
        padding: 0.375rem 0.5rem;
        min-width: 38px;
    }

    .chat-input .dropdown .btn {
        width: 38px;
        height: 38px;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .chat-input #message-input {
        min-height: 38px;
        font-size: 14px;
    }

    /* File preview mobile adjustments */
    .chat-input .bg-white.rounded.border {
        margin-top: 0.5rem;
    }

    .chat-input .text-truncate {
        max-width: 150px;
    }
}

/* Ensure file name doesn't break layout */
.min-w-0 {
    min-width: 0;
}

.text-truncate {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}
</style>
@endpush

@push('scripts')
<script>
    document.addEventListener('livewire:load', function () {
        // Auto-resize textarea
        const messageInput = document.getElementById('message-input');
        if (messageInput) {
            messageInput.addEventListener('input', function() {
                this.style.height = 'auto';
                this.style.height = (this.scrollHeight) + 'px';
            });
        }

        // Redirect file upload clicks
        document.getElementById('file-upload').addEventListener('click', function(e) {
            e.stopPropagation();
        });

        document.getElementById('image-upload').addEventListener('click', function(e) {
            e.stopPropagation();
        });

        // Handle file upload button clicks
        document.querySelector('label[for="file-upload"]').addEventListener('click', function(e) {
            e.preventDefault();
            document.getElementById('file-upload').click();
        });

        document.querySelector('label[for="image-upload"]').addEventListener('click', function(e) {
            e.preventDefault();
            document.getElementById('image-upload').click();
        });
    });
</script>
@endpush
