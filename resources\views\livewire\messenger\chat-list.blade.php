<div class="chat-sidebar">
    <div class="chat-sidebar-header d-flex justify-content-between align-items-center p-3 border-bottom">
        <h5 class="mb-0">{{ __('messages') }}</h5>
        <div class="d-flex align-items-center">
            @if(auth()->user()->role == 'company')
            <div class="me-3">
                <select class="form-select select-job" wire:model="jobFilter" wire:change="filterByJob($event.target.value)">
                    <option value="">{{ __('all_jobs') }}</option>
                    @foreach($jobs as $job)
                        <option value="{{ $job->id }}">{{ $job->title }}</option>
                    @endforeach
                </select>
            </div>
            @endif
            <div class="custom-checkbox">
                <input type="checkbox" id="unread-checkbox" class="d-none" wire:model="unreadFilter" wire:change="toggleUnreadFilter">
                <label for="unread-checkbox" class="mb-0 cursor-pointer">{{ __('unread') }}</label>
            </div>
        </div>
    </div>
    <div class="chat-user-list p-3" style="height: 900px; overflow-y: auto;">
        <ul class="list-unstyled mb-0">
            @forelse($users as $user)
                <li class="chat-user-item mb-3 p-3 rounded-3 position-relative {{ $selectedUser == $user->id ? 'active' : '' }}"
                    wire:click="selectUser({{ $user->id }})">
                    <div class="d-flex align-items-center">
                        <div class="flex-shrink-0">
                            @if(auth()->user()->role == 'company')
                                <img src="{{ $user->candidate && $user->candidate->user ? $user->candidate->user->image_url : asset('backend/image/default.png') }}" alt="User" class="rounded-circle" width="50" height="50">
                            @else
                                <img src="{{ $user->company && $user->company->user ? $user->company->user->image_url : asset('backend/image/default.png') }}" alt="User" class="rounded-circle" width="50" height="50">
                            @endif
                        </div>
                        <div class="flex-grow-1 ms-3">
                            <div class="d-flex justify-content-between align-items-center">
                                <h6 class="mb-0">
                                    @if(auth()->user()->role == 'company')
                                        {{ $user->candidate && $user->candidate->user ? $user->candidate->user->name : __('Unknown User') }}
                                    @else
                                        {{ $user->company && $user->company->user ? $user->company->user->name : __('Unknown User') }}
                                    @endif
                                </h6>
                                <small class="text-muted">{{ $user->latest_message_humans_time ?? '' }}</small>
                            </div>
                            <p class="mb-0 text-muted small">{{ Str::limit($user->latest_message ?? '', 30) }}</p>
                        </div>
                    </div>
                    @if($user->unread_count > 0)
                        <span class="position-absolute top-0 end-0 translate-middle badge rounded-pill bg-danger">
                            {{ $user->unread_count }}
                        </span>
                    @endif
                </li>
            @empty
                <li class="text-center py-5">
                    <div class="text-muted">
                        <i class="fas fa-inbox fa-3x mb-3"></i>
                        <p>{{ __('no_messages_yet') }}</p>
                    </div>
                </li>
            @endforelse
        </ul>
    </div>
</div>
