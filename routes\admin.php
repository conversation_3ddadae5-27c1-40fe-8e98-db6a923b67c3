<?php

use App\Http\Controllers\Admin\AdminController;

use App\Http\Controllers\Admin\Auth\ForgotPasswordController;
use App\Http\Controllers\Admin\Auth\LoginController;
use App\Http\Controllers\Admin\Auth\ResetPasswordController;
use App\Http\Controllers\Admin\BenefitController;
use App\Http\Controllers\Admin\CandidateController;
use App\Http\Controllers\Admin\CandidateAPIController;
use App\Http\Controllers\Admin\CandidateDataTableController;
use App\Http\Controllers\Admin\CandidateLanguageController;
use App\Http\Controllers\Admin\CmsController;
use App\Http\Controllers\Admin\CompanyController;
use App\Http\Controllers\Admin\DatabaseToolsController;
use App\Http\Controllers\Admin\EducationController;
use App\Http\Controllers\Admin\EmailTemplateController;
use App\Http\Controllers\Admin\ExperienceController;
use App\Http\Controllers\Admin\IndustryTypeController;
use App\Http\Controllers\Admin\JobCategoryController;
use App\Http\Controllers\Admin\JobController;
use App\Http\Controllers\Admin\JobDataTableController;
use App\Http\Controllers\Admin\JobRoleController;
use App\Http\Controllers\Admin\JobTypeController;
use App\Http\Controllers\Admin\LogController;
use App\Http\Controllers\Admin\MenuController;
use App\Http\Controllers\Admin\OrderController;
use App\Http\Controllers\Admin\OrganizationTypeController;
use App\Http\Controllers\Admin\PageController;
use App\Http\Controllers\Admin\PaymentController;
use App\Http\Controllers\Admin\ProfessionController;
use App\Http\Controllers\Admin\ProfileController;
use App\Http\Controllers\Admin\ReportController;
use App\Http\Controllers\Admin\RolesController;
use App\Http\Controllers\Admin\SalaryTypeController;
use App\Http\Controllers\Admin\SettingsController;
use App\Http\Controllers\Admin\SkillController;
use App\Http\Controllers\Admin\SocialiteController;
use App\Http\Controllers\Admin\TagController;
use App\Http\Controllers\Admin\TeamSizeController;
use App\Http\Controllers\Admin\UserController;
use App\Http\Controllers\Admin\ValidationController;
use App\Http\Controllers\CityController;
use App\Http\Controllers\SearchCountryController;
use App\Http\Controllers\StateController;
use App\Http\Controllers\Website\WebsiteSettingController;
use App\Http\Controllers\KecamatanController;
use App\Http\Controllers\KelurahanController;
use App\Http\Controllers\Website\MessageController;
use Illuminate\Support\Facades\Route;

// Get admin path from settings
try {
    $adminPath = \App\Helpers\AdminPathHelper::getAdminPath();
} catch (\Exception $e) {
    $adminPath = 'admin';
}

Route::prefix($adminPath)->middleware(['web'])->group(function () {
    /**
     * Auth routes
     */
    Route::get('/login', [LoginController::class, 'showLoginForm'])->name('login.admin');
    Route::post('/login', [LoginController::class, 'login'])->name('admin.login');
    Route::post('/logout', [LoginController::class, 'logout'])->name('admin.logout');

    Route::middleware(['guest:admin'])->group(function () {
        Route::post('password/email', [ForgotPasswordController::class, 'sendResetLinkEmail'])->name('admin.password.email');
        Route::get('password/reset', [ForgotPasswordController::class, 'showLinkRequestForm'])->name('admin.password.request');
        Route::post('password/reset', [ResetPasswordController::class, 'reset'])->name('admin.password.update');
        Route::get('password/reset/{token}', [ResetPasswordController::class, 'showResetForm'])->name('admin.password.reset');
    });

    Route::middleware(['auth:admin', 'admin.activity.logger'])->group(function () {
        //Dashboard Route (Dashboard, Dashboard Export, Notification)
        Route::get('/', [AdminController::class, 'index']);
        Route::get('/dashboard', function() {
            return redirect()->route('admin.dashboard');
        });
        Route::get('/dasbor', [AdminController::class, 'dashboard'])->name('admin.dashboard');

        // Setup Guide Route
        Route::post('/setup-guide/hide', [AdminController::class, 'hideSetupGuide'])->name('admin.setup-guide.hide');

        // Notification Route
        Route::post('/notifikasi/baca', [AdminController::class, 'notificationRead'])->name('admin.notification.read');
        Route::get('/notifikasi', [AdminController::class, 'allNotifications'])->name('admin.all.notification');

        // Roles Route
        Route::resource('role', RolesController::class);

        //Users Route
        Route::resource('user', UserController::class)->only(['dashboard', 'index', 'create', 'store', 'edit', 'update', 'destroy']);

        Route::get('/perusahaan/{company}/dokumen', [CompanyController::class, 'documents'])->name('admin.company.documents');
        Route::get('/perusahaan/{company}/dokumen/change', [CompanyController::class, 'toggle'])->name('admin.document.verify.change');

        Route::post('/company/{company}/documents', [CompanyController::class, 'downloadDocument'])->name('company.verify.documents.download');
        Route::get('/perusahaan/ekspor/{type}', [CompanyController::class, 'companyExport'])->name('company.export');
        //Company Route resource

        Route::resource('perusahaan', CompanyController::class)->names([
            'index' => 'company.index',
            'create' => 'company.create',
            'store' => 'company.store',
            'show' => 'company.show',
            'edit' => 'company.edit',
            'update' => 'company.update',
            'destroy' => 'company.destroy',
        ]);

        // Flyer routes
        Route::get('flyer', [\App\Http\Controllers\Admin\FlyerController::class, 'index'])->name('admin.flyer.index');
        Route::get('flyer/{id}/edit', [\App\Http\Controllers\Admin\FlyerController::class, 'edit'])->name('admin.flyer.edit');
        Route::put('flyer/{id}', [\App\Http\Controllers\Admin\FlyerController::class, 'update'])->name('admin.flyer.update');
        Route::delete('flyer/{id}', [\App\Http\Controllers\Admin\FlyerController::class, 'destroy'])->name('admin.flyer.destroy');
        Route::get('flyer/{id}/process', [\App\Http\Controllers\Admin\FlyerController::class, 'setProcessing'])->name('admin.flyer.process');
        Route::get('/perusahaan/change/status', [CompanyController::class, 'statusChange'])->name('company.status.change');
        Route::get('/perusahaan/verif/status', [CompanyController::class, 'verificationChange'])->name('company.verify.change');
        Route::get('/perusahaan/profil/verif/status', [CompanyController::class, 'profileVerificationChange'])->name('company.profile.verify.change');
        Route::get('/perusahaan/jobs', [CompanyController::class, 'getCompanyJobs'])->name('admin.company.jobs');

        // Document Preview Routes
        Route::get('/dokumen/preview', [\App\Http\Controllers\Admin\DocumentPreviewController::class, 'previewDocument'])->name('admin.document.preview');
        Route::get('/dokumen/download', [\App\Http\Controllers\Admin\DocumentPreviewController::class, 'downloadDocument'])->name('admin.document.download');

        // Candidate Route
        Route::resource('pencaker', CandidateController::class)->names([
            'index' => 'candidate.index',
            'create' => 'candidate.create',
            'store' => 'candidate.store',
            'show' => 'candidate.show',
            'edit' => 'candidate.edit',
            'update' => 'candidate.update',
            'destroy' => 'candidate.destroy',
        ]);
        Route::get('/pencaker/change/status', [CandidateController::class, 'statusChange'])->name('candidate.status.change');
        Route::get('/pencaker/ekspor/{type}', [CandidateController::class, 'candidateExport'])->name('candidate.export');
        Route::post('/pencaker/fix-data', [CandidateController::class, 'fixBrokenData'])->name('candidate.fix.data');
        Route::get('/pencaker/datatable', CandidateDataTableController::class)->name('candidate.datatable');
        Route::post('/pencaker/bulk/activate', [CandidateController::class, 'bulkActivate'])->name('candidate.bulk.activate');
        Route::post('/pencaker/bulk/deactivate', [CandidateController::class, 'bulkDeactivate'])->name('candidate.bulk.deactivate');
        Route::post('/pencaker/bulk/delete', [CandidateController::class, 'bulkDelete'])->name('candidate.bulk.delete');
        Route::get('/pencaker/{candidateId}/dokumen', [AdminController::class, 'showCandidateDocuments'])->name('candidate.documents');

        // Validasi NIK, Email, dan Nomor HP
        Route::post('/check/nik', [ValidationController::class, 'checkNik'])->name('admin.check.nik');
        Route::post('/check/email', [ValidationController::class, 'checkEmail'])->name('admin.check.email');
        Route::post('/check/phone', [ValidationController::class, 'checkPhone'])->name('admin.check.phone');

        // Candidate API Routes
        Route::get('/api/candidate/{id}/documents', [CandidateAPIController::class, 'getDocuments'])->name('api.candidate.documents');
        Route::get('/api/candidate/{id}/profile', [CandidateAPIController::class, 'getProfile'])->name('api.candidate.profile');
        Route::get('/api/candidate/{id}/applied-jobs', [CandidateAPIController::class, 'getAppliedJobs'])->name('api.candidate.applied-jobs');

        //JobCategory Route resource
        Route::resource('jobCategory', JobCategoryController::class)->except('show');
        Route::post('/loker/kategori/bulk/import', [JobCategoryController::class, 'bulkImport'])->name('admin.job.category.bulk.import');

        //job Route resource
        Route::resource('loker', JobController::class)->parameters([
            'loker' => 'job'
        ])->names([
            'index' => 'job.index',
            'create' => 'job.create',
            'store' => 'job.store',
            'show' => 'job.show',
            'edit' => 'job.edit',
            'update' => 'job.update',
            'destroy' => 'job.destroy',
        ]);
        Route::get('/loker/hapus-terpilih', [JobController::class, 'deleteSelected'])->name('jobs.deleteSelected');
        Route::get('lamaran/loker', [JobController::class, 'appliedJobs'])->name('applied.jobs');
        Route::get('lamaran/loker/{applied_job}', [JobController::class, 'appliedJobsShow'])->name('applied.job.show');
        Route::get('pencaker/profil/detail', [\App\Http\Controllers\Admin\CandidateProfileController::class, 'getProfileDetails'])->name('admin.candidate.profile.details');
        Route::get('pencaker/cv/{resume}/pratinjau', [\App\Http\Controllers\Admin\CandidateResumeController::class, 'preview'])->name('admin.candidate.resume.preview');

        // API routes for candidate documents
        Route::get('api/pencaker/{id}/dokumen', [\App\Http\Controllers\Admin\CandidateDocumentController::class, 'getDocuments'])->name('api.candidate.documents');
        Route::get('api/pencaker/{id}/profil', [\App\Http\Controllers\Admin\CandidateDocumentController::class, 'getProfile'])->name('api.candidate.profile');
        Route::get('api/pencaker/{id}/lamaran', [\App\Http\Controllers\Admin\CandidateDocumentController::class, 'getAppliedJobs'])->name('api.candidate.applied-jobs');
        Route::get('loker/{job}/lamaran', [JobController::class, 'appliedJobs'])->name('admin.job.applications');
        Route::post('/loker/bulk/import', [JobController::class, 'bulkImport'])->name('admin.job.bulk.import');
         Route::post('loker/ubah/status/{job}', [JobController::class, 'jobStatusChange'])->name('admin.job.status.change');
        Route::get('loker/duplikat/{job:slug}', [JobController::class, 'clone'])->name('admin.job.clone');
        Route::get('loker/diedit/daftar', [JobController::class, 'editedJobList'])->name('admin.job.edited.index');
        Route::get('loker/diedit/tampil/{job:slug}', [JobController::class, 'editedShow'])->name('admin.job.edited.show');
        Route::put('loker/diedit/disetujui/{job:slug}', [JobController::class, 'editedApproved'])->name('admin.job.edited.approved');

        // Job Detail Routes
        Route::get('loker/{id}/detail', [\App\Http\Controllers\Admin\JobDetailController::class, 'getJobDetail']);
        Route::post('loker/terbitkan', [\App\Http\Controllers\Admin\JobDetailController::class, 'publishJob']);
        Route::post('loker/revisi', [\App\Http\Controllers\Admin\JobDetailController::class, 'revisionJob']);
        Route::post('loker/tolak', [\App\Http\Controllers\Admin\JobDetailController::class, 'rejectJob']);

        // DataTables AJAX routes
        Route::get('loker/datatable/data', [JobDataTableController::class, 'index'])->name('admin.job.datatable');
        Route::get('lamaran-loker/datatable/data', [\App\Http\Controllers\Admin\AppliedJobDataTableController::class, 'index'])->name('admin.applied.job.datatable');
        Route::get('lamaran-loker/statistik-bulanan', [\App\Http\Controllers\Admin\AppliedJobDataTableController::class, 'getMonthlyStats'])->name('admin.applied.job.monthly.stats');

        // job role route resource
        Route::resource('jobRole', JobRoleController::class)->except('show', 'create');
        Route::post('/job/role/bulk/import', [JobRoleController::class, 'bulkImport'])->name('admin.job.role.bulk.import');

        // industry type route resource
        Route::resource('industryType', IndustryTypeController::class)->except('show', 'create');
        Route::post('/industry/type/bulk/import', [IndustryTypeController::class, 'bulkImport'])->name('admin.industry.type.bulk.import');
        Route::post('/industry/store', [IndustryTypeController::class, 'storeAjax'])->name('admin.industry.store');

        // Organization Type route resource
        Route::resource('organizationType', OrganizationTypeController::class)->except('show', 'create');
        Route::post('/organization/type/bulk/import', [OrganizationTypeController::class, 'bulkImport'])->name('admin.organization.type.bulk.import');

        // Salary Type  route resource
        Route::resource('salaryType', SalaryTypeController::class)->except('show', 'create');
        Route::post('/salary/type/bulk/import', [SalaryTypeController::class, 'bulkImport'])->name('admin.salary.type.bulk.import');

        // profession route resource
        Route::resource('profession', ProfessionController::class)->except('show', 'create');
        Route::post('/profession/bulk/import', [ProfessionController::class, 'bulkImport'])->name('admin.profession.bulk.import');

        // skills route resource
        Route::resource('skill', SkillController::class)->except('show', 'create');
        Route::post('/skill/bulk/import', [SkillController::class, 'bulkImport'])->name('admin.skill.bulk.import');

        // benefit route resource
        Route::resource('benefit', BenefitController::class)->except('show', 'create');

        //  education route resource
        Route::resource('education', EducationController::class)->except('show', 'create');

        //  experience route resource
        Route::resource('experience', ExperienceController::class)->except('show', 'create');

        //  team size route resource
        Route::resource('teamSize', TeamSizeController::class)->except('show', 'create');

        //  job type route resource
        Route::resource('jobType', JobTypeController::class)->except('show', 'create');

        // tags route resource
        Route::resource('tags', TagController::class);
        Route::post('tags/status/change/{tag}', [TagController::class, 'statusChange'])->name('tags.status.change');
        Route::post('/tags/bulk/import', [TagController::class, 'bulkImport'])->name('admin.tags.bulk.import');

        // menu settings
        Route::post('menu-settings/status-update/{menuSetting}', [MenuController::class, 'statusChange'])->name('menu-setting.status.change');
        Route::resource('settings/menu-settings', MenuController::class);
        Route::post('settings/menu-settings/sort', [MenuController::class, 'sortAble'])->name('menu-setting.sort-able');

        // About Page
        Route::controller(CmsController::class)->group(function () {
            Route::get('settings/delete/about/logo/{name}', 'aboutLogoDelete')->name('settings.aboutLogo.delete');
            Route::get('settings/delete/payment/logo/{name}', 'paymentLogoDelete')->name('settings.paymentLogo.delete');
            Route::put('settings/about', 'aboutupdate')->name('settings.aboutupdate');
            Route::put('settings/payments', 'paymentupdate')->name('settings.paymentupdate');
            Route::put('settings/others', 'othersupdate')->name('settings.others.update');
            Route::put('settings/home', 'home')->name('settings.home.update');
            Route::put('settings/auth', 'auth')->name('settings.auth.update');
            Route::put('settings/faq', 'faq')->name('settings.faq.update');
            Route::put('settings/errorpages', 'updateErrorPages')->name('settings.errorpage.update');
            Route::put('settings/comingsoon', 'comingsoon')->name('settings.comingsoon.update');
            Route::put('settings/account/complete/update', 'accountCompleteUpdate')->name('settings.account.complate.update');
            Route::put('settings/maintenance/mode/update', 'maintenanceModeUpdate')->name('settings.maintenance.mode.update');
        });

        //Dashboard Export Route
        Route::controller(AdminController::class)->group(function () {
            Route::get('/dasbor/ekspor', 'exportDashboard')->name('admin.dashboard.export');
            Route::post('/admin/cari', 'search')->name('admin.search');
            Route::post('/admin/download/transaksi/invoice/{transaction}', 'downloadTransactionInvoice')->name('admin.transaction.invoice.download');
            Route::post('/lihat/transaksi/invoice/{transaction}', 'viewTransactionInvoice')->name('admin.transaction.invoice.view');
        });

        // Feedback Routes
        Route::controller(\App\Http\Controllers\Admin\FeedbackController::class)->prefix('feedback')->name('admin.feedback.')->group(function () {
            Route::get('/', 'index')->name('index');
            Route::get('/data', 'getData')->name('data');
            Route::get('/user-profile', 'getUserProfile')->name('user-profile');
            Route::get('/{id}', 'show')->name('show');
            Route::put('/{id}/mark-as-read', 'markAsRead')->name('mark-as-read');
            Route::delete('/{id}', 'destroy')->name('destroy');
        });

        // Inbox Routes
        Route::controller(\App\Http\Controllers\Admin\InboxController::class)->group(function () {
            Route::get('/inbox', 'index')->name('admin.inbox.index');
            Route::get('/inbox/create', 'create')->name('admin.inbox.create');
            Route::post('/inbox/store', 'store')->name('admin.inbox.store');
            Route::get('/inbox/broadcast', 'broadcast')->name('admin.inbox.broadcast');
            Route::post('/inbox/broadcast/send', 'sendBroadcast')->name('admin.inbox.broadcast.send');
            Route::post('/inbox/reply', 'reply')->name('admin.inbox.reply');
            Route::get('/inbox/load-more', 'loadMoreMessages')->name('admin.inbox.load-more');
        });

        // Report Routes
        Route::controller(ReportController::class)->group(function () {
            // Dashboard Laporan
            Route::get('/laporan', 'index')->name('admin.reports');
            Route::get('/laporan/user-comparison', 'getUserComparisonData')->name('admin.reports.user-comparison');
            Route::get('/laporan/jobs-by-month', 'getJobsByMonthData')->name('admin.reports.jobs-by-month');
            Route::get('/laporan/application-status', 'getApplicationStatusData')->name('admin.reports.application-status');
            Route::get('/laporan/education-distribution', 'getEducationDistributionData')->name('admin.reports.education-distribution');
            Route::get('/laporan/age-distribution', 'getAgeDistributionData')->name('admin.reports.age-distribution');
            Route::get('/laporan/application-trends', 'getApplicationTrendsData')->name('admin.reports.application-trends');
            Route::get('/laporan/job-absorption', 'getJobAbsorptionData')->name('admin.reports.job-absorption');
            Route::get('/laporan/job-type-comparison', 'getJobTypeComparisonData')->name('admin.reports.job-type-comparison');

            // New Reports
            Route::get('/laporan/salary-analysis', 'getSalaryAnalysisData')->name('admin.reports.salary-analysis');
            Route::get('/laporan/gender-distribution', 'getGenderDistributionData')->name('admin.reports.gender-distribution');
            Route::get('/laporan/job-category-analysis', 'getJobCategoryAnalysisData')->name('admin.reports.job-category-analysis');
            Route::get('/laporan/location-analysis', 'getLocationAnalysisData')->name('admin.reports.location-analysis');
            Route::get('/laporan/remote-work-analysis', 'getRemoteWorkAnalysisData')->name('admin.reports.remote-work-analysis');
            // Halaman Laporan Terpisah - DIHAPUS untuk Query Reporting
            // Route::get('/laporan/serapan-lowongan', 'jobAbsorptionIndex')->name('admin.reports.job-absorption.index');
            // Route::get('/laporan/jenis-pekerjaan', 'jobTypeIndex')->name('admin.reports.job-type.index');
            // Route::get('/laporan/pendidikan', 'educationIndex')->name('admin.reports.education.index');
            // Route::get('/laporan/umur', 'ageIndex')->name('admin.reports.age.index');

            // Export
            Route::post('/laporan/export-pdf', 'exportPdf')->name('admin.reports.export-pdf');
            Route::post('/laporan/preview-pdf', 'previewPdf')->name('admin.reports.preview-pdf');
            Route::post('/laporan/export-excel', 'exportExcel')->name('admin.reports.export-excel');
        });

        //Profile Route
        Route::controller(ProfileController::class)->group(function () {
            Route::get('/pengaturan/profil', 'setting')->name('profile.setting');
            Route::get('/profil', 'profile')->name('profile');
            Route::put('/profil', 'profile_update')->name('profile.update');
        });

        // Order Route
        Route::controller(OrderController::class)->group(function () {
            Route::get('/orders', 'index')->name('order.index');
            Route::get('/order/create', 'create')->name('order.create');
            Route::post('/order/store', 'store')->name('order.store');
            Route::get('/orders/{id}', 'show')->name('order.show');

            Route::get('/order/user/plan/{earning}', 'updateUserPlan')->name('order.user.plan.update');
            Route::put('/user/plan/update/{user}', 'UserPlanUpdate')->name('user.plan.update');
        });

        // ========================================================
        // ====================Setting=============================
        // ========================================================

        // Website Setting Route
        Route::put('settings/terms/conditions/update', [CmsController::class, 'termsConditionsUpdate'])->name('admin.privacy.terms.update');
        Route::controller(WebsiteSettingController::class)
            ->prefix('settings')
            ->name('settings.')
            ->group(function () {
                Route::get('/websitesetting', 'website_setting')->name('websitesetting');
                Route::post('/session/terms-privacy', 'sessionUpdateTermsPrivacy')->name('session.update.tems-privacy');
                Route::delete('/cms/content', 'cmsContentDestroy')->name('cms.content.destroy');
            });

        // Admin Setting Route
        Route::controller(SettingsController::class)
            ->prefix('settings')
            ->name('settings.')
            ->group(function () {
                Route::get('umum', 'general')->name('general');
                Route::put('umum', 'generalUpdate')->name('general.update');
                Route::put('preferensi', 'preferenceUpdate')->name('preference.update');
                Route::get('layout', 'layout')->name('layout');
                Route::put('layout', 'layoutUpdate')->name('layout.update');
                Route::put('mode', 'modeUpdate')->name('mode.update');
                Route::get('tema', 'theme')->name('theme');
                Route::put('tema', 'colorUpdate')->name('theme.update');
                Route::get('custom', 'custom')->name('custom');
                Route::put('custom', 'custumCSSJSUpdate')->name('custom.update');
                Route::get('email', 'email')->name('email');
                Route::get('email/logs', 'emailLogs')->name('email.logs');
                Route::put('email', 'emailUpdate')->name('email.update');
                Route::post('test-email', 'testEmailSent')->name('email.test');

                // system update
                Route::get('sistem', 'system')->name('system');
                Route::put('sistem/update', 'systemUpdate')->name('system.update');
                Route::put('sistem/mode/update', 'systemModeUpdate')->name('system.mode.update');
                Route::put('sistem/jobdeadline/update', 'systemJobdeadlineUpdate')->name('system.jobdeadline.update');

                // system update end
                Route::put('search/indexing', 'searchIndexing')->name('search.indexing');
                Route::put('google-analytics', 'googleAnalytics')->name('google.analytics');
                Route::put('allowLangChanging', 'allowLaguageChanage')->name('allow.langChange');
                Route::put('change/timezone', 'timezone')->name('change.timezone');

                // cookies routes
                Route::get('cookies', 'cookies')->name('cookies');
                Route::put('cookies/update', 'cookiesUpdate')->name('cookies.update');

                // seo
                Route::get('seo/index', 'seoIndex')->name('seo.index');
                Route::get('seo/edit/{page}', 'seoEdit')->name('seo.edit');
                Route::put('seo/update/{content}', 'seoUpdate')->name('seo.update');
                Route::get('buat/sitemap', 'generateSitemap')->name('generateSitemap');

                // database backup end
                Route::put('working-process/update', 'workingProcessUpdate')->name('working.process.update');

                // pwa option Update
                Route::put('pwa/update', 'pwaUpdate')->name('pwa.update');

                // recaptcha Update
                Route::put('recaptcha/update', 'recaptchaUpdate')->name('recaptcha.update');

                // pusher Update
                Route::put('pusher/update', 'pusherUpdate')->name('pusher.update');

                // analytics Update
                Route::put('analytics/update', 'analyticsUpdate')->name('analytics.update');

                // payperjob Update
                Route::put('payperjob/update', 'payperjobUpdate')->name('payperjob.update');

                // upgrade application
                Route::get('upgrade', 'upgrade')->name('upgrade');
                Route::post('upgrade/apply', 'upgradeApply')->name('upgrade.apply');

                // systemInfo
                Route::get('/sistem/info', 'systemInfo')->name('systemInfo');

                // landing page
                Route::put('landing-page', 'landingPageUpdate')->name('landingPage.update');

                Route::get('/sistem/ad_setting', 'ad_setting')->name('ad_setting');
                Route::put('/update_ad_info', 'update_ad_info')->name('adinfo.update');
                Route::put('/update_ad_status', 'update_ad_status')->name('adstatus.update');
            });

        // Log Routes
        Route::get('settings/logs/login', [LogController::class, 'loginLogs'])->name('settings.admin.logs.login');
        Route::get('settings/logs/activity', [LogController::class, 'activityLogs'])->name('settings.admin.logs.activity');

        // Database Tools Routes
        Route::prefix('settings/database-tools')
            ->name('settings.database-tools.')
            ->group(function () {
                Route::get('/', [DatabaseToolsController::class, 'index'])->name('index');
                Route::post('/backup/create', [DatabaseToolsController::class, 'createBackup'])->name('backup.create');
                Route::post('/backup/download', [DatabaseToolsController::class, 'downloadBackup'])->name('backup.download');
                Route::post('/backup/delete', [DatabaseToolsController::class, 'deleteBackup'])->name('backup.delete');
                Route::post('/backup/restore', [DatabaseToolsController::class, 'restoreBackup'])->name('backup.restore');
                Route::post('/backup/restore-upload', [DatabaseToolsController::class, 'restoreUploadBackup'])->name('backup.restore-upload');
                Route::post('/backup/schedule', [DatabaseToolsController::class, 'updateBackupSchedule'])->name('backup.schedule');
                Route::post('/database/reset', [DatabaseToolsController::class, 'resetDatabase'])->name('database.reset');
                Route::post('/database/optimize', [DatabaseToolsController::class, 'optimizeDatabase'])->name('database.optimize');
                Route::post('/delete-candidates', [DatabaseToolsController::class, 'deleteCandidates'])->name('delete-candidates');
                Route::post('/delete-companies', [DatabaseToolsController::class, 'deleteCompanies'])->name('delete-companies');
            });

        // Email Template Route
        Route::group(['prefix' => 'settings/email-templates', 'as' => 'settings.email-templates.'], function () {
            Route::get('/', [EmailTemplateController::class, 'index'])->name('list');
            Route::post('/save', [EmailTemplateController::class, 'save'])->name('save');
        });

        Route::controller(PageController::class)->prefix('settings/pages')->name('settings.')->group(function () {
            Route::get('/', 'index')->name('pages.index');
            Route::get('/buat', 'create')->name('pages.create');
            Route::post('/buat', 'store')->name('pages.store');
            Route::get('/edit/{page}', 'edit')->name('pages.edit');
            Route::put('/update/{page}', 'update')->name('pages.update');
            Route::delete('/hapus/{page}', 'delete')->name('pages.delete');
            Route::get('/status/showinheader', 'changeShowInheader')->name('pages.header.status');
            Route::get('/status/showinfooter', 'changeShowInFooter')->name('pages.footer.status');
        });
        // Socialite Route
        Route::controller(SocialiteController::class)->group(function () {
            Route::get('settings/social-login', 'index')->name('settings.social.login');
            Route::put('settings/social-login', 'update')->name('settings.social.login.update');
            Route::post('settings/social-login/status', 'updateStatus')->name('settings.social.login.status.update');
        });

        // Payment Route
        Route::controller(PaymentController::class)
            ->prefix('settings/payment')
            ->name('settings.')
            ->group(function () {
                // Automatic Payment
                Route::get('/auto', 'autoPayment')->name('payment');
                Route::put('/', 'update')->name('payment.update');

                // Manual Payment
                Route::get('/manual', 'manualPayment')->name('payment.manual');
                Route::post('/manual/store', 'manualPaymentStore')->name('payment.manual.store');
                Route::get('/manual/{manual_payment}/edit', 'manualPaymentEdit')->name('payment.manual.edit');
                Route::put('/manual/{manual_payment}/update', 'manualPaymentUpdate')->name('payment.manual.update');
                Route::delete('/manual/{manual_payment}/delete', 'manualPaymentDelete')->name('payment.manual.delete');
                Route::get('/manual/status/change', 'manualPaymentStatus')->name('payment.manual.status');
            });

        // candidate language
        Route::resource('candidate/language/index', CandidateLanguageController::class, ['names' => 'admin.candidate.language']);
        Route::controller(SearchCountryController::class)->prefix('settings/location/country')->name('location.country.')->group(function () {
            Route::get('/', 'index')->name('country');
            Route::get('/tambah', 'create')->name('create');
            Route::post('/tambah', 'store')->name('store');
            Route::get('/edit/{id}', 'edit')->name('edit');
            Route::put('/edit/{id}', 'update')->name('update');
            Route::delete('/hapus/{id}', 'destroy')->name('destroy');
        });
        Route::controller(StateController::class)->prefix('settings/location/state')->name('location.state.')->group(function () {
            Route::get('/', 'index')->name('state');
            Route::get('/tambah', 'create')->name('create');
            Route::post('/tambah', 'store')->name('store');
            Route::get('/edit/{id}', 'edit')->name('edit');
            Route::put('/edit/{id}', 'update')->name('update');
            Route::delete('/hapus/{id}', 'destroy')->name('destroy');
        });
        Route::controller(CityController::class)->prefix('settings/location/city')->name('location.city.')->group(function () {
            Route::get('/', 'index')->name('city');
            Route::get('/tambah', 'create')->name('create');
            Route::post('/tambah', 'store')->name('store');
            Route::get('/edit/{id}', 'edit')->name('edit');
            Route::put('/edit/{id}', 'update')->name('update');
            Route::delete('/hapus/{id}', 'destroy')->name('destroy');
        });
        // Routes untuk Kecamatan
        Route::controller(KecamatanController::class)->prefix('settings/location/kecamatan')->name('location.kecamatan.')->group(function () {
            Route::get('/', 'index')->name('index'); // Menampilkan semua kecamatan
            Route::get('/tambah', 'create')->name('create'); // Form tambah kecamatan
            Route::post('/tambah', 'store')->name('store'); // Simpan kecamatan baru
            Route::get('/edit/{kecamatan}', 'edit')->name('edit'); // Form edit kecamatan
            Route::put('/edit/{kecamatan}', 'update')->name('update'); // Update kecamatan
            Route::delete('/hapus/{kecamatan}', 'destroy')->name('destroy'); // Hapus kecamatan
        });
        // Routes untuk Kelurahan
        Route::controller(KelurahanController::class)->prefix('settings/location/kelurahan')->name('location.kelurahan.')->group(function () {
            Route::get('/', 'index')->name('index'); // Menampilkan semua kelurahan
            Route::get('/tambah', 'create')->name('create'); // Form tambah kelurahan
            Route::post('/tambah', 'store')->name('store'); // Simpan kelurahan baru
            Route::get('/edit/{kelurahan}', 'edit')->name('edit'); // Form edit kelurahan
            Route::put('/edit/{kelurahan}', 'update')->name('update'); // Update kelurahan
            Route::delete('/hapus/{kelurahan}', 'destroy')->name('destroy'); // Hapus kelurahan
            Route::delete('/hapus/bulk', 'bulkDestroy')->name('bulkDestroy'); // Hapus kelurahan secara massal
            Route::get('/data', 'getKelurahanData')->name('data'); // Data kelurahan untuk datatable
            Route::get('/import', 'importForm')->name('import.form'); // Form import kelurahan
            Route::post('/import', 'importData')->name('import.data'); // Import kelurahan dari API
        });
    });
});
