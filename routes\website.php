<?php

namespace App;

use App\Http\Controllers\Admin\AdminController;
use App\Http\Controllers\Admin\PageController;
use App\Http\Controllers\Auth\RegisterController;
use App\Http\Controllers\Auth\SocialLoginController;
use App\Http\Controllers\Payment\PayPalController;
use App\Http\Controllers\Website\CandidateController;
use App\Http\Controllers\Website\CompanyController;
use App\Http\Controllers\Website\CompanyVerifyDocuments;
use App\Http\Controllers\Website\GlobalController;
use App\Http\Controllers\Website\MessageController;
use App\Http\Controllers\Website\MessengerController;
use App\Http\Controllers\Website\WebsiteController;
use App\Http\Controllers\FileController;
use App\Http\Requests\EmailVerificationUpdateRequest;
use Illuminate\Foundation\Auth\EmailVerificationRequest;
use Illuminate\Support\Facades\Auth;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;
use Modules\Seo\Entities\Seo;
use App\Http\Controllers\Api\CompanyController as ApiCompanyController;

Route::get('/test-debugbar', function () {
    \Log::info('Testing debugbar');
    return view('frontend.layouts.master', ['title' => 'Test Debugbar']);
});

Route::get('/test-admin-certification', function () {
    $candidate = \App\Models\Candidate::with('certifications')->first();
    if (!$candidate) {
        return 'No candidate found';
    }

    return [
        'candidate_id' => $candidate->id,
        'candidate_name' => $candidate->user->name ?? 'No name',
        'certifications_count' => $candidate->certifications->count(),
        'certifications' => $candidate->certifications
    ];
});

Route::get('/test-certification', function () {
    $candidate = \App\Models\Candidate::first();
    if (!$candidate) {
        return 'No candidate found';
    }

    // Test creating a certification
    $certification = \App\Models\CandidateCertification::create([
        'candidate_id' => $candidate->id,
        'name' => 'Test Certificate',
        'organization' => 'Test Organization',
        'issue_date' => '2023-01-01',
        'expiration_date' => '2025-01-01',
        'credential_id' => 'TEST123',
        'credential_url' => 'https://example.com'
    ]);

    return [
        'candidate' => $candidate->user->name,
        'certification' => $certification,
        'formatted_issue_date' => $certification->formatted_issue_date,
        'formatted_expiration_date' => $certification->formatted_expiration_date
    ];
});

// Route::get('/test', function () {
//     Seo::query()->delete();

//     $pages = [
//         [
//             'page_slug' => 'home',
//             'title' => 'Welcome To Jobpilot',
//         ],
//         [
//             'page_slug' => 'jobs',
//             'title' => 'Jobs',
//         ],
//         [
//             'page_slug' => 'job-details',
//             'title' => 'Job Details',
//         ],
//         [
//             'page_slug' => 'candidates',
//             'title' => 'Candidates',
//         ],
//         [
//             'page_slug' => 'candidate-details',
//             'title' => 'Candidate Details',
//         ],
//         [
//             'page_slug' => 'company',
//             'title' => 'Company',
//         ],
//         [
//             'page_slug' => 'company-details',
//             'title' => 'Company Details',
//         ],
//         [
//             'page_slug' => 'blog',
//             'title' => 'Blog',
//         ],
//         [
//             'page_slug' => 'post-details',
//             'title' => 'Post Details',
//         ],
//         [
//             'page_slug' => 'pricing',
//             'title' => 'Pricing',
//         ],
//         [
//             'page_slug' => 'login',
//             'title' => 'Login',
//         ],
//         [
//             'page_slug' => 'register',
//             'title' => 'Register',
//         ],
//         [
//             'page_slug' => 'about',
//             'title' => 'About',
//         ],
//         [
//             'page_slug' => 'contact',
//             'title' => 'Contact',
//         ],
//         [
//             'page_slug' => 'faq',
//             'title' => 'FAQ',
//         ],
//         [
//             'page_slug' => 'terms-condition',
//             'title' => 'Terms Condition',
//         ],

//     ];

//     $seo_chunks = array_chunk($pages, ceil(count($pages) / 3));

//     foreach ($seo_chunks as $key => $seo) {
//         // return $seo[0]['page_slug'];
//         $page = Seo::insert([
//             'page_slug' => $seo[$key]['page_slug'],
//             // 'title' => $seo['title'],
//         ]);

//         // $page->contents()->create([
//         //     'language_code' => 'en',
//         //     'title' => $seo['title'],
//         //     'description' => 'Jobpilot is job portal laravel script designed to create, manage and publish jobs posts. Companies can create their profile and publish jobs posts. Candidate can apply job posts.',
//         //     'image' => 'frontend/assets/images/jobpilot.png',
//         // ]);
//     }

//     return Seo::all();
// });


// =====================================================================
// =============================Authentication Routes===================
// ======================================================================
if (! app()->runningInConsole()) {
    Auth::routes(['verify' => setting('email_verification')]);
} else {
    Auth::routes(['verify' => false]);
}

// Email Verification
Route::get('/email/verifikasi/{id}/{hash}', function (EmailVerificationRequest $request) {
    $request->fulfill();

    if (authUser()->role == 'company') {
        return redirect()->route('company.dashboard', ['verified' => true]);
    } else {
        return redirect()->route('candidate.dashboard', ['verified' => true]);
    }
})->middleware(['auth', 'signed'])->name('verification.verify');

// Email Verification Update
Route::get('/email/verifikasi/update/{id}/{newEmail}', function (EmailVerificationUpdateRequest $request, $id, $newEmail) {
    if (! $request->hasValidSignature()) {
        abort(401);
    }
    $request->fulfill($newEmail);

    if (authUser()->role == 'company') {
        return redirect()->route('company.dashboard', ['verified' => true]);
    } else {
        return redirect()->route('candidate.dashboard', ['verified' => true]);
    }
})->middleware(['auth', 'signed'])->name('email.verification.update.verify');

// Social Authentication
Route::controller(SocialLoginController::class)->group(function () {
    Route::post('/auth/social/register', 'register')->name('social.register');
    Route::get('/auth/{provider}/redirect', 'redirect')->name('social.login');
    Route::get('/auth/{provider}/callback', 'callback');
});

// =====================================================================
// =============================Guest Routes=============================
// ======================================================================
Route::controller(WebsiteController::class)->name('website.')->group(function () {
    Route::get('/', 'index')->name('home');
    Route::get('/tentang-kami', 'about')->name('about');
    Route::get('/kontak', 'contact')->name('contact');
    Route::get('/saran-masukan', [\App\Http\Controllers\Website\FeedbackController::class, 'index'])->name('feedback');
    Route::post('/saran-masukan', [\App\Http\Controllers\Website\FeedbackController::class, 'store'])->name('feedback.store');
    Route::get('/lanjut', 'pricing')->name('plan');
    Route::get('/lanjut/{label}', 'planDetails')->name('plan.details');
    Route::get('/faq', 'faq')->name('faq');
    Route::get('/syarat-dan-ketentuan', 'termsCondition')->name('termsCondition');
    Route::get('/kebijakan-privasi', 'privacyPolicy')->name('privacyPolicy');
    Route::get('/refund-policy', 'refundPolicy')->name('refundPolicy');
    Route::get('/coming-soon', 'comingSoon')->name('comingsoon');
    Route::get('/loker', 'jobs')->name('job');
    Route::get('/kategori-loker', 'jobCategories')->name('job.category');
    Route::get('/loker/kategori/{category}', 'jobs')->name('job.category.slug');
    Route::get('/loker/{job:slug}', 'jobDetails')->name('job.details');
    Route::get('/loker/{job:slug}/bookmark', 'toggleBookmarkJob')->name('job.bookmark')->middleware('user_active');
    Route::post('/loker/lamar', 'toggleApplyJob')->name('job.apply')->middleware('user_active');
    Route::get('/pencaker', 'candidates')->name('candidate');
    Route::get('/candidates/{candidate:username}', 'candidateDetails')->name('candidate.details');
    Route::get('/pencaker/{candidate:username}', 'candidateDetails')->name('candidate.profile');
    Route::get('/candidate/profile/details', 'candidateProfileDetails')->name('candidate.profile.details');
    Route::get('/candidate/application/profile/details', 'candidateApplicationProfileDetails')->name('candidate.application.profile.details');
    Route::post('/candidate/profile/viewed', 'candidateProfileViewed')->name('candidate.profile.viewed');
    Route::get('/candidates/download/cv/{resume}', 'candidateDownloadCv')->name('candidate.download.cv');
    Route::get('/candidates/preview/cv/{resume}', 'candidatePreviewCv')->name('candidate.preview.cv');
    Route::get('/perusahaan', 'company')->name('company');
    Route::get('/perusahaan/{user:username}', 'employersDetails')->name('employe.details');
    Route::get('/blog', 'posts')->name('posts');
    Route::get('/blog/{post:slug}', 'post')->name('post');
    Route::post('/comment/{post:slug}/add', 'comment')->name('comment');
    Route::post('/markasread/single/notification', 'markReadSingleNotification')->name('markread.notification');
    Route::post('/set/session', 'setSession')->name('set.session');
    Route::get('/selected/country', 'setSelectedCountry')->name('set.country');
    Route::get('/selected/country/remove', 'removeSelectedCountry')->name('remove.country');
    Route::get('job/autocomplete', 'jobAutocomplete')->name('job.autocomplete');
    Route::post('/job/benefits/create', 'jobBenefitCreate')->name('job.benefit.create');
    Route::get('transaksi-sukses', 'successTransaction')->name('paypal.successTransaction');
    Route::get('/locality', 'getLocality')->name('locality');
    Route::get('/cities-with-jobs', 'getCitiesWithJobs')->name('cities.with.jobs');
    Route::get('/kecamatan-by-city', 'getKecamatanByCity')->name('kecamatan.by.city');
});

// Route::get('/employe', [WebsiteController::class,'employees'])->name('company');
// Route::get('/employe/details/{username}', [WebsiteController::class, 'employersDetails'])->name('website.employe.details');
Route::middleware(['auth:admin'])->prefix('admin')->name('admin.')->group(function () {
    // Tambahkan route untuk download CV
    Route::get('/candidate/{candidateId}/download-cv', [AdminController::class, 'downloadCV'])->name('candidate.downloadCV');
});
// Menampilkan & Download CV pelamar
Route::get('/admin/candidate/documents/{candidateId}', [AdminController::class, 'showCandidateDocuments'])->name('admin.candidate.documents');
Route::post('/admin/candidate/cv/download/{candidateId}', [AdminController::class, 'downloadCV'])->name('candidate.cv.download');
// Download AK1 di backend
Route::post('/candidate/{id}/ak1/download', [CandidateController::class, 'downloadAK1'])->name('candidate.ak1.download');


// Routes daftar
// Masih dalam proses, ntar aja lanjut di next update wkwkwk

// Route::get('/register/pelamar', [RegisterController::class, 'showPelamarRegistrationForm'])->name('register.pelamar');
// Route::post('/register/pelamar', [RegisterController::class, 'registerPelamar'])->name('register.pelamar.submit');

// Route::get('/register/perusahaan', [RegisterController::class, 'showPerusahaanRegistrationForm'])->name('register.perusahaan');
// Route::post('/register/perusahaan', [RegisterController::class, 'registerPerusahaan'])->name('register.perusahaan.submit');

// ======================================================================
// =============================Authenticated Routes=====================
// ======================================================================
Route::middleware('auth:user', 'verified')->group(function () {
    // Dashboard Route
    Route::get('/user/dashboard', [WebsiteController::class, 'dashboard'])->name('user.dashboard');

    Route::post('/user/notification/read', [WebsiteController::class, 'notificationRead'])->name('user.notification.read');

    // Candidate Routes
    Route::controller(CandidateController::class)->prefix('candidate')->middleware(['auth:user', 'candidate'])->name('candidate.')->group(function () {
        Route::get('dasbor', 'dashboard')->name('dashboard');
        Route::get('loker-dilamar', 'appliedjobs')->name('appliedjob');
        Route::get('bookmarks', 'bookmarks')->name('bookmark');
        Route::get('pengaturan', 'setting')->name('setting');
        Route::put('settings/update', 'settingUpdate')->name('settingUpdate');
        Route::get('/semua/notifikasi', 'allNotification')->name('allNotification');
        Route::get('/inbox', [MessageController::class, 'candidateInbox'])->name('inbox');
        Route::get('/loker/pemberitahuan', 'jobAlerts')->name('job.alerts');
        Route::get('/loker/pemberitahuan/buat', 'createJobAlert')->name('job.alerts.create');
        Route::post('/resume/store', 'resumeStore')->name('resume.store');
        Route::post('/resume/store/ajax', 'resumeStoreAjax')->name('resume.store.ajax');
        Route::post('/get/resume/ajax', 'getResumeAjax')->name('get.resume.ajax');
        Route::post('/resume/update', 'resumeUpdate')->name('resume.update');
        Route::delete('/resume/delete/{resume}', 'resumeDelete')->name('resume.delete');
        Route::post('/experiences/store', 'experienceStore')->name('experiences.store');
        Route::put('/experiences/update', 'experienceUpdate')->name('experiences.update');
        Route::delete('/experiences/{experience}', 'experienceDelete')->name('experiences.destroy');
        Route::post('/educations/store', 'educationStore')->name('educations.store');
        Route::put('/educations/update', 'educationUpdate')->name('educations.update');
        Route::delete('/educations/{education}', 'educationDelete')->name('educations.destroy');
        Route::post('/certifications/store', 'certificationStore')->name('certifications.store');
        Route::put('/certifications/update', 'certificationUpdate')->name('certifications.update');
        Route::delete('/certifications/{certification}', 'certificationDelete')->name('certifications.destroy');
        Route::post('/cv/show', 'cvShow')->name('cv.show');
    });

    Route::get('company/verify-documents/{mediaId}/show', [CompanyVerifyDocuments::class, 'showPDF'])->name('company.verify.documents.show');

    // Company Routes
    Route::controller(CompanyController::class)->prefix('company')->middleware(['auth:user', 'company', 'has_plan'])->name('company.')->group(function () {
        Route::middleware('company.profile')->group(function () {
            Route::get('dasbor', 'dashboard')->name('dashboard');
            Route::get('lanjutkan', 'plan')->name('plan'); // ->middleware('user_active'); // jadi untuk melanjutkan tidak usah melewati middleware user harus aktif
            // Route::post('download/transaction/invoice/{transaction}', 'downloadTransactionInvoice')->name('transaction.invoice.download');
            // Route::get('lihat/transaksi/invoice/{transaction:order_id}', 'viewTransactionInvoice')->name('transaction.invoice.view');
            Route::get('loker-saya', 'myjobs')->name('myjob')->withoutMiddleware('has_plan');
            Route::get('loker-saya/data', 'myjobsData')->name('myjob.data')->withoutMiddleware('has_plan');
            Route::get('pending-edited-jobs', 'pendingEditedJobs')->name('pending.edited.jobs');
            // Route::get('buat/bayar-per-loker', 'payPerJob')->name('job.payPerJobCreate')->withoutMiddleware('has_plan');
            // Route::post('/store/payper/job', 'storePayPerJob')->name('payperjob.store')->withoutMiddleware('has_plan');
            Route::get('buat/loker', 'createJob')->name('job.create')->middleware('user_active');
            Route::post('/store/job', 'storeJob')->name('job.store');
            Route::get('/loker/pembayaran', 'payPerJobPayment')->name('payperjob.payment')->withoutMiddleware('has_plan');
            Route::get('/loker/sukses/{job:slug}', 'showPromoteJob')->name('job.promote.show');
            // Route::get('/promosi/{job:slug}', 'jobPromote')->name('promote');
            Route::get('/kloning/{job:slug}', 'jobClone')->name('clone');
            Route::post('/promote/job/{jobCreated}', 'promoteJob')->name('job.promote');
            Route::get('edit/{job:slug}/job', 'editJob')->name('job.edit')->withoutMiddleware('has_plan');
            Route::post('make/job/expire/{job}', 'makeJobExpire')->name('job.make.expire');
            Route::post('make/job/active/{job}', 'makeJobActive')->name('job.make.active');
            Route::put('/update/{job:slug}/job', 'updateJob')->name('job.update')->withoutMiddleware('has_plan');
            Route::get('loker/lamaran-kerja', 'jobApplications')->name('job.application');
            Route::get('loker/lamaran-kerja-simple', 'simpleJobApplications')->name('job.application.simple');
            Route::get('loker/lamaran-kerja-simple/datatable', [\App\Http\Controllers\Company\SimpleApplicationDataTableController::class, 'index'])->name('job.application.simple.datatable');
            Route::post('loker/lamaran-kerja/pindahkan', 'moveApplication')->name('application.move');
            Route::post('loker/lamaran-kerja/pindahkan-massal', 'moveApplicationBulk')->name('application.move.bulk');
            Route::put('applications/sync', 'applicationsSync')->name('application.sync');
            Route::post('applications/column/store', 'applicationColumnStore')->name('applications.column.store');
            Route::delete('applications/group/delete/{group}', 'applicationColumnDelete')->name('applications.column.delete');
            Route::put('applications/group/update', 'applicationColumnUpdate')->name('applications.column.update');
            Route::delete('delete/{job:id}/application', 'destroyApplication')->name('application.delete');
            Route::get('bookmarks', function() {
                try {
                    $request = request();
                    $query = currentCompany()->bookmarkCandidates();

                    if ($request->category != 'all' && $request->has('category') && $request->category != null) {
                        $query->wherePivot('category_id', $request->category);
                    }

                    $bookmarks = $query
                        ->with('profession')
                        ->paginate(12)
                        ->withQueryString();
                    $categories = \App\Models\CompanyBookmarkCategory::where('company_id', auth()->user()->company->id)->get();

                    return view('frontend.pages.company.bookmark-modern', compact('bookmarks', 'categories'));
                } catch (\Exception $e) {
                    flashError('Terjadi kesalahan: '.$e->getMessage());

                    return back();
                }
            })->name('bookmark');

            // AJAX route untuk bookmark data
            Route::get('bookmarks/data', function() {
                try {
                    $request = request();
                    $query = currentCompany()->bookmarkCandidates();

                    if ($request->category != 'all' && $request->has('category') && $request->category != null) {
                        $query->wherePivot('category_id', $request->category);
                    }

                    $bookmarks = $query
                        ->with('profession')
                        ->paginate(12)
                        ->withQueryString();

                    $html = view('frontend.pages.company.partials._bookmark_candidates', compact('bookmarks'))->render();

                    return response()->json([
                        'success' => true,
                        'html' => $html,
                        'pagination' => view('vendor.pagination.frontend-ajax-generic', ['paginator' => $bookmarks])->render(),
                        'total' => $bookmarks->total()
                    ]);
                } catch (\Exception $e) {
                    return response()->json([
                        'success' => false,
                        'message' => 'Terjadi kesalahan: ' . $e->getMessage()
                    ], 500);
                }
            })->name('bookmark.data');
            Route::get('pengaturan', 'setting')->name('setting')->withoutMiddleware('has_plan');
            Route::put('settings/update', 'settingUpdateInformation')->name('settingUpdateInformation')->withoutMiddleware('has_plan');
            Route::get('/semua/notifikasi', 'allNotification')->name('allNotification');
            Route::get('/inbox', [MessageController::class, 'companyInbox'])->name('inbox');
            Route::post('applications/group/store', 'applicationsGroupStore')->name('applications.group.store');
            Route::put('applications/group/update/{group}', 'applicationsGroupUpdate')->name('applications.group.update');
            Route::delete('applications/group/destroy/{group}', 'applicationsGroupDestroy')->name('applications.group.destroy');
            Route::post('/pertanyaan-screening', 'storeQuestion')->name('questions.store');
            Route::get('/pertanyaan-screening', 'manageQuestion')->name('questions.manage');
            Route::post('/pertanyaan-screening/featureToggle', 'featureToggle')->name('questions.featureToggle');
            Route::delete('/pertanyaan-screening/{question}', 'deleteQuestion')->name('questions.delete');
        });

        Route::post('/company/bookmark/{candidate}', 'companyBookmarkCandidate')->name('companybookmarkcandidate')->middleware('user_active');
        Route::get('account-progress', 'accountProgress')->name('account-progress')->withoutMiddleware('has_plan');
        Route::put('/profile/complete/{id}', 'profileCompleteProgress')->name('profile.complete')->withoutMiddleware('has_plan');
        Route::post('/industry/store', 'storeIndustryAjax')->name('industry.store')->withoutMiddleware('has_plan');
        Route::get('/bookmark/kategori', 'bookmarkCategories')->name('bookmark.category.index');
        Route::post('/bookmark/categories/store', 'bookmarkCategoriesStore')->name('bookmark.category.store');
        Route::get('/bookmark/kategori/edit/{category}', 'bookmarkCategoriesEdit')->name('bookmark.category.edit');
        Route::put('/bookmark/categories/update/{category}', 'bookmarkCategoriesUpdate')->name('bookmark.category.update');
        Route::delete('/bookmark/categories/destroy/{category}', 'bookmarkCategoriesDestroy')->name('bookmark.category.destroy');
        Route::post('username/change', 'usernameUpdate')->name('username.change');
    });

    Route::prefix('company')->middleware(['company', 'has_plan'])->group(function () {
        // Old routes
        // Route::get('upload-flyer-old', [CompanyVerifyDocuments::class, 'index'])->name('company.verify.documents.index');
        Route::post('verify-documents', [CompanyVerifyDocuments::class, 'store'])->name('company.verify.documents.store');

        // New flyer routes
        Route::get('upload-flyer', [\App\Http\Controllers\Website\FlyerController::class, 'index'])->name('company.flyer.index');
        Route::get('upload-flyer/create', [\App\Http\Controllers\Website\FlyerController::class, 'create'])->name('company.flyer.create');
        Route::post('upload-flyer', [\App\Http\Controllers\Website\FlyerController::class, 'store'])->name('company.flyer.store');
        Route::get('upload-flyer/{id}/edit', [\App\Http\Controllers\Website\FlyerController::class, 'edit'])->name('company.flyer.edit');
        Route::put('upload-flyer/{id}', [\App\Http\Controllers\Website\FlyerController::class, 'update'])->name('company.flyer.update');
        Route::delete('upload-flyer/{id}', [\App\Http\Controllers\Website\FlyerController::class, 'destroy'])->name('company.flyer.destroy');
    });
});

// Old Messenger Routes (akan dihapus setelah migrasi selesai)
Route::controller(MessengerController::class)->middleware('auth:user', 'verified')->group(function () {
    Route::get('/company/messages', function(Request $request) {
        return app(\App\Http\Controllers\MessageController::class)->index($request);
    })->name('company.messages')->middleware('company');
    Route::get('/candidate/messages', function(Request $request) {
        return app(\App\Http\Controllers\MessageController::class)->index($request);
    })->name('candidate.messages')->middleware('candidate');
    // Route::post('/company/message/candidate', 'messageSendCandidate')->name('company.message.candidate'); // Moved to MessageController
    Route::get('/get/messages/{username}', 'fetchMessages');
    Route::post('/send/message', 'sendMessage');
    Route::post('/send/attachment', 'sendAttachment');
    Route::post('/typing-indicator', 'typingIndicator');
    Route::post('/mark-message-read', 'markMessageRead');
    Route::post('/check-new-messages', 'checkNewMessages');
    Route::post('message/markas/read/{username}', 'messageMarkasRead')->name('message.markas.read');
    Route::get('/get/users', 'filterUsers');
    Route::get('/sync/user-list', 'syncUserList');
    // Redirect old route to new route for backward compatibility
    Route::get('/load-unread-count', function() {
        return redirect()->route('messages.unread.count');
    })->name('load.unread.count');
});

// New Message Routes
Route::controller(MessageController::class)->middleware('auth:user', 'verified')->group(function () {
    Route::get('/messages/threads', 'getThreads')->name('messages.threads');
    Route::get('/messages/thread/{threadId}', 'getMessages')->name('messages.thread');
    Route::post('/messages/send', 'sendMessage')->name('messages.send');
    Route::post('/messages/mark-as-read', 'markAsRead')->name('messages.mark.read');
    Route::get('/messages/unread-count', 'getUnreadCount')->name('messages.unread.count');

    // Route untuk mengirim pesan dari perusahaan ke kandidat
    Route::post('/company/message/candidate', 'companyMessageCandidate')->name('company.message.candidate');

    // Ticket Support Routes
    Route::post('/ticket/status/update', 'updateTicketStatus')->name('ticket.status.update');
    Route::post('/ticket/priority/update', 'updateTicketPriority')->name('ticket.priority.update');
    Route::post('/ticket/category/update', 'updateTicketCategory')->name('ticket.category.update');
});

// Non-AJAX Message Routes
Route::controller(\App\Http\Controllers\MessageController::class)->middleware('auth:user', 'verified')->group(function () {
    Route::post('/messages/load-more', 'loadMoreMessages')->name('messages.load-more');
    Route::post('/messages/reply', 'reply')->name('messages.reply');
    Route::post('/messages/threads/ajax', 'getThreadsAjax')->name('messages.threads.ajax');
});

// ======================================================================
// ===================Global & Artisan Command Routes====================
// ======================================================================
Route::controller(GlobalController::class)->group(function () {
    Route::get('/check/username/{name}', 'checkUsername');
    Route::get('/translated/texts', 'fetchCurrentTranslatedText');
    Route::get('/lang/{lang}', 'changeLanguage');
    Route::get('/migrate/data', 'migrateData');
    Route::get('/optimize-clear', 'optimizeClear')->name('app.optimize-clear');
    Route::post('/ckeditor/upload', 'ckeditorImageUpload')->name('ckeditor.upload');
});

Route::get('/{slug}', [PageController::class, 'showCustomPage'])->name('showCustomPage');
Route::controller(PayPalController::class)->group(function () {
    Route::post('paypal/payment', 'processTransaction')->name('paypal.post');
    Route::get('success-transaction', 'successTransaction')->name('paypal.successTransaction');
    Route::get('cancel-transaction', 'cancelTransaction')->name('paypal.cancelTransaction');
});
Route::get('/payment-from-app/{label}',[ApiCompanyController::class, 'payment']);
Route::post('/validate-field', [RegisterController::class, 'validateField'])->name('validate.field');

// handle file upload AK1
Route::post('/upload-file', [RegisterController::class, 'uploadFile'])->name('upload.file');
Route::post('register', [RegisterController::class, 'register'])->name('register');
